package com.dl.basicservice.web.controller.internal.adm.sys.oplog;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.basicservice.biz.dal.sys.oplog.po.OpLogPO;
import com.dl.basicservice.biz.manager.sys.oplog.OpLogManager;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogAddBO;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogPageBO;
import com.dl.basicservice.web.controller.internal.adm.sys.oplog.convert.OpLogConvert;
import com.dl.basicservice.web.controller.internal.adm.sys.oplog.dto.OpLogDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.oplog.req.OpLogAddReq;
import com.dl.basicservice.web.controller.internal.adm.sys.oplog.req.OpLogPageReq;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 14:50
 */
@RestController
@RequestMapping("/internal/oplog")
public class InternalOpLogController {

    @Resource
    private OpLogManager opLogManager;

    @ApiOperation("新增操作日志")
    @PostMapping("/newlog")
    public ResultModel<Void> newOpLog(@RequestBody @Validated OpLogAddReq req) {
        OpLogAddBO addBO = OpLogConvert.cnvOpLogAddReq2BO(req);
        opLogManager.newOpLog(addBO);
        return ResultModel.success(null);
    }

    @ApiOperation("分页查询操作日志")
    @PostMapping("/page")
    public ResultPageModel<OpLogDTO> page(@RequestBody @Validated OpLogPageReq req) {
        OpLogPageBO pageBO = OpLogConvert.cnvOpLogPageReq2BO(req);
        Page<OpLogPO> pageResult = opLogManager.page(pageBO);

        ResultPageModel<OpLogDTO> resultPageModel = new ResultPageModel<>();
        resultPageModel.setPageSize(pageResult.getSize());
        resultPageModel.setPageIndex(pageResult.getCurrent());
        resultPageModel.setTotalPage(pageResult.getPages());
        resultPageModel.setTotal(pageResult.getTotal());
        resultPageModel.setDataResult(
                pageResult.getRecords().stream().map(OpLogConvert::cnvOpLogPO2DTO).collect(Collectors.toList()));
        return resultPageModel;
    }

}
