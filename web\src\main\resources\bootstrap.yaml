spring:
  application:
    name: dl-basic-service
  cloud:
    compatibility-verifier:
      enabled: false
logging:
  level:
    com:
      alibaba:
        cloud:
          nacos:
            client: debug
ribbon:
  nacos:
    enabled: true
dubbo:
  cloud:
    subscribed-services: qywx_app
  application:
    id: ${spring.application.name}
    qos-enable: false
  protocol:
    name: dubbo  #协议
    port: -1
  registry:
    address: spring-cloud://localhost
  consumer:
    check: false
---
spring:
  profiles: test
  cloud:
    nacos:
      username: nacos
      password: dinglitec@1234
      discovery:
        server-addr: *************:8848
      config:
        file-extension: yaml
        server-addr: *************:8848
        refresh-enabled: true
        extension-configs:
          - dataId: tencent_cloud-test.yaml
            refresh: false
          - dataId: elasticsearch_client-test.yaml
            refresh: false
          - dataId: session-test.yaml
            refresh: false

---
spring:
  profiles: stable
  cloud:
    nacos:
      username: nacos
      password: dinglitec@1234
      discovery:
        server-addr: *************:8848
      config:
        file-extension: yaml
        server-addr: *************:8848
        refresh-enabled: true
        extension-configs:
          - dataId: tencent_cloud-test.yaml
            refresh: false
          - dataId: elasticsearch_client-test.yaml
            refresh: false
          - dataId: session-test.yaml
            refresh: false
        group: stable
---
spring:
  profiles: prod
  cloud:
    nacos:
      username: nacos
      password: dinglitec@nacosprod123
      discovery:
        server-addr: 172.18.1.14:8848,172.18.2.8:8848,172.18.3.12:8848
      config:
        file-extension: yaml
        server-addr: 172.18.1.14:8848,172.18.2.8:8848,172.18.3.12:8848
        refresh-enabled: true
        extension-configs:
          - dataId: tencent_cloud-prod.yaml
            refresh: false
          - dataId: elasticsearch_client-prod.yaml
            refresh: false
          - dataId: session-prod.yaml
            refresh: false