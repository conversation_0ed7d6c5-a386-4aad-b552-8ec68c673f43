package com.dl.basicservice.web.controller.internal.adm.sys.menu.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 14:24
 */
@Data
public class InternalTenantMenuSaveParamDTO {

    @NotNull
    @ApiModelProperty("租户code")
    private String tenantCode;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("菜单主键数组")
    private List<String> menuIds;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("功能编码数组")
    private List<String> functionIds;

    @NotNull(message = "操作人id不能为空")
    @ApiModelProperty("操作人id")
    private Long operatorId;

    @NotEmpty(message = "操作人名不能为空")
    @ApiModelProperty("操作人名")
    private String operatorName;
}
