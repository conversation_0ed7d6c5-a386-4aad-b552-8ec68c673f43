package com.dl.basicservice.biz.manager.sys.function.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.dal.sys.function.TenantRoleFunctionMapper;
import com.dl.basicservice.biz.dal.sys.function.po.TenantRoleFunctionPO;
import com.dl.basicservice.biz.manager.sys.function.TenantRoleFunctionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TenantRoleFunctionManagerImpl extends ServiceImpl<TenantRoleFunctionMapper, TenantRoleFunctionPO>
        implements
        TenantRoleFunctionManager {

}
