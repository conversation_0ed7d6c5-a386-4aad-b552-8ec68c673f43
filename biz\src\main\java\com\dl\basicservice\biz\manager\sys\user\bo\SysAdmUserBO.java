package com.dl.basicservice.biz.manager.sys.user.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

/**
 * 管理端用户
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("用户信息")
public class SysAdmUserBO {
    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("账号")
    @Size(min = 4, max = 32, message = "账号长度必须在4-36位之间")
    private String account;

    @ApiModelProperty("手机号")
    private String mobile;
    /**
     * 账号状态
     * 0-启用
     * 1-锁定
     * 2-禁用
     * <p>
     * ADM_USER_STATUS
     */
    @ApiModelProperty("账号状态 0-启用 1-锁定 2-禁用")
    private String status;

    @ApiModelProperty("姓名")
    @Size(min = 0, max = 64, message = "姓名长度最长64位")
    private String userName;

    /**
     * 1-男 2-女
     */
    @ApiModelProperty("性别 1-男 2-女")
    private String gender;

    /**
     * 联系电话
     */
    @ApiModelProperty("联系电话")
    private String phone;

    /**
     * 是否超管
     * 1-是
     * 0-否
     */
    @ApiModelProperty("是否超管 1-是 0-否")
    private String isSuperAdm;

}
