package com.dl.basicservice.biz.manager.sys.userbind.helper;

import com.dl.basicservice.biz.dal.sys.userbind.po.TenantAdmUserBindPO;
import com.dl.basicservice.biz.manager.sys.userbind.dto.TenantAdmUserBindDTO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-01 17:40
 */
public class TenantAdmUserBindHelper {

    public static TenantAdmUserBindDTO cnvTenantAdmUserBindPO2DTO(TenantAdmUserBindPO input) {
        if(Objects.isNull(input)){
            return null;
        }

        TenantAdmUserBindDTO result = new TenantAdmUserBindDTO();
        result.setId(input.getId());
        result.setTenantCode(input.getTenantCode());
        result.setExtUserId(input.getExtUserId());
        result.setUserId(input.getUserId());
        result.setIsDeleted(input.getIsDeleted());
        result.setCreateDt(input.getCreateDt());
        result.setModifyDt(input.getModifyDt());
        return result;
    }

}
