package com.dl.basicservice.biz.manager.sys.function.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.dal.sys.function.TenantFunctionMapper;
import com.dl.basicservice.biz.dal.sys.function.po.TenantFunctionPO;
import com.dl.basicservice.biz.manager.sys.function.TenantFunctionManager;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class TenantFunctionManagerImpl extends ServiceImpl<TenantFunctionMapper, TenantFunctionPO> implements
        TenantFunctionManager {

}
