package com.dl.basicservice.biz.manager.sys.user.dto;

import com.dl.basicservice.biz.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@Builder
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class SysAdmUserWithProfileDTO extends BaseDTO {
    private static final long serialVersionUID = -5540085945257278467L;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("手机")
    private String mobile;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("token")
    private String token;

    private String tenantCode;

    @ApiModelProperty(hidden = true)
    private Integer isSuperAdm;

    @ApiModelProperty(value = "状态", hidden = true)
    private Integer status;

    private Long createBy;

    private Long modifyBy;

}
