package com.dl.basicservice.web.controller.adm.interceptor;

import com.dl.basicservice.biz.common.annotation.AdminAuth;
import com.dl.basicservice.biz.common.annotation.NotLogin;
import com.dl.basicservice.biz.common.annotation.Permission;
import com.dl.basicservice.biz.common.properties.WebIgnoreProperties;
import com.dl.basicservice.biz.common.util.OperatorUtil;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.manager.sys.role.TenantUserRoleManager;
import com.dl.basicservice.biz.manager.sys.role.bo.PermissionBO;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.dto.SysBasicUserDTO;
import com.dl.basicservice.web.util.AccountCompent;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.framework.core.interceptor.expdto.CertificateException;
import com.dl.framework.core.interceptor.expdto.ForceExitException;
import com.dl.framework.core.interceptor.expdto.SessionTimeoutException;
import io.jsonwebtoken.lang.Collections;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.PathMatcher;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.resource.ResourceHttpRequestHandler;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;

import static com.dl.basicservice.biz.common.constant.Const.*;

@Component
public class AuthenticationInterceptor implements HandlerInterceptor, Ordered {

    private final PathMatcher pathMatcher = new AntPathMatcher();
    @Autowired
    private SysAdmUserService sysAdmUserService;
    @Autowired
    private TenantUserRoleManager tenantUserRoleManager;
    @Autowired
    private WebIgnoreProperties properties;
    @Autowired
    private OperatorUtil operatorUtil;

    private boolean isIgnoreUrl(HttpServletRequest request) {
        String url = request.getRequestURI();
        List<String> urlPatterns = properties.getUrls();
        if (Collections.isEmpty(urlPatterns)) {
            return false;
        }
        for (String urlPattern : urlPatterns) {
            if (pathMatcher.match(urlPattern, url)) {
                return true;
            }
        }
        return false;
    }

    private boolean isInternal(HttpServletRequest request) {
        String url = request.getRequestURI();
        return pathMatcher.match("/internal/**", url);
    }

    private boolean checkNotNull(String headValue){
        if(StringUtils.isBlank(headValue) || "null".equals(headValue)){
            return false;
        }
        return true;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        //判断是否为内部调用，内部调用请求不走此判断
        if (isInternal(request)) {
            Long operator = checkNotNull(request.getHeader(BASIC_SERVICE_INVOKE_OPERATOR)) ?
                    Long.parseLong(request.getHeader(BASIC_SERVICE_INVOKE_OPERATOR)) :
                    null;
            String tenantCode = checkNotNull(request.getHeader(BASIC_SERVICE_INVOKE_TENANTCODE)) ?
                    request.getHeader(BASIC_SERVICE_INVOKE_TENANTCODE) :
                    StringUtils.EMPTY;
            operatorUtil.init(operator, tenantCode);
            return true;
        }
        //判断是否忽略url
        if (isIgnoreUrl(request)) {
            return true;
        } else if (handler instanceof ResourceHttpRequestHandler) {
            //静态资源
            return false;
        }

        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();
        //2、判断 NotLogin，有则跳过认证
        if (method.isAnnotationPresent(NotLogin.class)) {
            NotLogin loginToken = method.getAnnotation(NotLogin.class);
            if (loginToken.required()) {
                return true;
            }
        }

        // 从 http 请求头中取出 token和systemCode
        String token = request.getHeader(TOKEN_HEADER_NAME);
        String systemCode = getSystemCode(request);
        //1、如果token为空的情况下，返回非法token，禁止访问
        if (StringUtils.isEmpty(token)) {
            //非法token
            throw new CertificateException();
        } else {
            SysBasicUserDTO dto;
            try {
                token = sysAdmUserService.formatToken(token);
                dto = sysAdmUserService.parseJwtToken(token);
            } catch (Exception e) {
                throw new CertificateException("非法请求");
            }

            //2、redis中登录超时
            String sessionToken = sysAdmUserService.getSessionToken(dto.getUserId());
            if (StringUtils.isEmpty(sessionToken)) {
                throw new SessionTimeoutException();
            }
            //3、判断是否被其他登录踢掉
            if (!token.equals(sessionToken)) {
                throw new ForceExitException();
            }
            //biz端的用户注入
            operatorUtil.init(dto.getUserId(), dto.getTenantCode());

            TenantAdmUserPO detail = sysAdmUserService.lambdaQuery().eq(TenantAdmUserPO::getUserId, dto.getUserId()).one();
            if (Objects.isNull(detail)) {
                throw BusinessServiceException.getInstance("不存在该用户");
            }
            //刷新用户缓存
            sysAdmUserService.refreshToken(dto.getUserId());
            //是否超管  //判断功能是否要求admin权限
            if (Objects.equals(detail.getIsSuperAdm(), 1)) {
                return true;
            } else if (method.isAnnotationPresent(AdminAuth.class)) {
                //功能需要admin权限
                throw BusinessServiceException.getInstance("该用户无此权限");
            }
            // 检查是否有 permission 注释，有则跳过权限验证
            if (method.isAnnotationPresent(Permission.class)) {
                Permission permission = method.getAnnotation(Permission.class);
                if (!permission.required()) {
                    return true;
                }
                if (!this.permissionLogic(permission, systemCode, dto.getUserId())) {
                    throw BusinessServiceException.getInstance("该用户无此权限");
                }
            }

        }
        return true;
    }

    /**
     * 权限判断
     *
     * @param permission
     * @param userId
     * @return
     */
    private boolean permissionLogic(Permission permission,String systemCode, Long userId) {
        PermissionBO permissionBO = new PermissionBO();
        permissionBO.setLogical(permission.logical());
        permissionBO.setRequired(permission.required());
        permissionBO.setValue(permission.value());
        return tenantUserRoleManager.permissionLogic(permissionBO, systemCode, userId);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler,
            Exception ex) {
        if (!isInternal(request)) {
            //去除biz端的用户注入
            operatorUtil.remove();
        }

    }

    @Override
    public int getOrder() {
        return 0;
    }

    public String getSystemCode(HttpServletRequest request) {
        String systemCode = request.getHeader(SYSTEM_CODE);
        if(StringUtils.isBlank(systemCode)){
            systemCode = DEFAULT_SYSTEM_CODE;
        }
        return systemCode;
    }
}
