package com.dl.basicservice.web.aop;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import java.util.concurrent.TimeUnit;

/**
 * @describe: 分布式锁
 * @author: zhousx
 * @date: 2022/5/23 11:11
 */
@Target({ ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RedisLock {
    String prefix() default ""; // 锁前缀

    String dynamicVal() default ""; // 动态变量

    int waitTime() default 0; // 等待获取锁时间

    int leaseTime() default 0; // 分布式锁的续约时间

    TimeUnit timeUnit() default TimeUnit.SECONDS; //时间单位
}
