package com.dl.basicservice.web.controller.adm.sys.admuser.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-02 15:05
 */
@Data
@ApiModel("批量新增后台账号(通讯录员工关联)信息")
public class BatchAddUserEmployeeParam {

    @ApiModelProperty("员工id列表")
    private List<String> employeeIdList;

    @ApiModelProperty("部门id列表")
    private List<String> deptIdList;

    @NotNull
    @Length(min = 6, max = 12, message = "密码长度必须在6-12位之间")
    @ApiModelProperty("登录密码")
    private String password;

}
