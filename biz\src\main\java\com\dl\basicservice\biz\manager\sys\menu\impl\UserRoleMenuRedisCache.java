package com.dl.basicservice.biz.manager.sys.menu.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.util.RedisUtil;
import com.dl.basicservice.biz.dal.sys.function.PrivilegeFunctionMapper;
import com.dl.basicservice.biz.dal.sys.function.TenantFunctionMapper;
import com.dl.basicservice.biz.dal.sys.function.TenantRoleFunctionMapper;
import com.dl.basicservice.biz.dal.sys.function.dto.TenantFunctionDTO;
import com.dl.basicservice.biz.dal.sys.function.po.PrivilegeFunctionPO;
import com.dl.basicservice.biz.dal.sys.function.po.TenantRoleFunctionPO;
import com.dl.basicservice.biz.dal.sys.menu.PrivilegeMenuMapper;
import com.dl.basicservice.biz.dal.sys.menu.TenantRoleMenuMapper;
import com.dl.basicservice.biz.dal.sys.menu.dto.PrivilegeMenuDTO;
import com.dl.basicservice.biz.dal.sys.menu.po.PrivilegeMenuPO;
import com.dl.basicservice.biz.dal.sys.menu.po.TenantRoleMenuPO;
import com.dl.basicservice.biz.dal.sys.tenant.SysTenantInfoMapper;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.dal.sys.user.TenantUserRoleMapper;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserRolePO;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleMenuParamBO;
import com.dl.basicservice.biz.manager.sys.role.bo.RoleFunctionParamBO;
import com.dl.basicservice.biz.manager.sys.user.ISysAuthCacheService;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserRolesParamBO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 用户-角色-菜单权限缓存
 */
@Component
public class UserRoleMenuRedisCache implements CommandLineRunner, ISysAuthCacheService {

    public static final String ALL_AUTH_KEY = "dl.authSet_key.all";

    public static final String ROLE_MENU_SET_CACHE_KEY_PREFIX = "dl.roleMenu_set.key.";

    public static final String SYSTEM_MENY_CACHE_KEY = "dl.menus.";

    public static final String FUNCTION_ALL_CACHE_KEY = "dl.functions.all";

    public static final String USER_ROLE_SET_CACHE_KEY_PREFIX = "dl.userRole_set.key.";

    public static final String ROLE_FUNCTION_SET_CACHE_KEY_PREFIX = "dl.roleFunction_set.key.";

    public static final String ADMUSER_KEY_PREFIX = "dl.adm_user.key.";

    public static final String TENANT_INFO_KEY_PREFIX = "dl.tenantInfo.key.";

    public static final Long ONE_DAY_EXPIRE_SECONDS = 24 * 60 * 60L;

    public static final Long FIVE_MINUTES_EXPIRE_SECONDS = 5 * 60L;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private TenantUserRoleMapper tenantUserRoleMapper;
    @Autowired
    private TenantRoleMenuMapper tenantRoleMenuMapper;
    @Autowired
    private TenantRoleFunctionMapper tenantRoleFunctionMapper;
    @Autowired
    private TenantFunctionMapper tenantFunctionMapper;
    @Autowired
    private PrivilegeFunctionMapper privilegeFunctionMapper;
    @Autowired
    private SysTenantInfoMapper sysTenantInfoMapper;
    @Autowired
    private PrivilegeMenuMapper privilegeMenuMapper;

    private String getMenuKey(String systemCode) {
        return SYSTEM_MENY_CACHE_KEY + systemCode;
    }

    private String getFunctionKey(String systemCode) {
        return FUNCTION_ALL_CACHE_KEY + systemCode;
    }

    private String getRoleMenuSetCacheKey(String systemCode, Long roleId) {
        return ROLE_MENU_SET_CACHE_KEY_PREFIX + systemCode + "_" + roleId;
    }

    private String getRoleMenuSetCacheKey(String systemCodeAndRoleId) {
        return ROLE_MENU_SET_CACHE_KEY_PREFIX + systemCodeAndRoleId;
    }

    private String getUserRoleSetCacheKey(String systemCode, Long userId) {
        return USER_ROLE_SET_CACHE_KEY_PREFIX + systemCode + "_" + userId;
    }

    private String getUserRoleSetCacheKey(String systemCodeAndUserId) {
        return USER_ROLE_SET_CACHE_KEY_PREFIX + systemCodeAndUserId;
    }

    private String getRoleFuntionSetCacheKey(Long roleId) {
        return ROLE_FUNCTION_SET_CACHE_KEY_PREFIX + roleId;
    }

    private String getSysAdmUserKey(Long userId) {
        return ADMUSER_KEY_PREFIX + userId;
    }

    private void addCacheKey(String... keys){
        redisUtil.addSetAll(ALL_AUTH_KEY,keys);
    }

    @Override
    public void run(String... args) throws Exception {
        refresh();
    }

    public void refresh() {
        //清除旧缓存
        clearAllCache();
        //全部租户
        initTenantInfoCache();
        //菜单实体
        initMenusCache();
        //功能实体
        initFunctionCache();
        //用户-角色
        initUserRoleSetCache();
        //角色-菜单
        initRoleMenusCache();
        //角色-功能
        initRoleFunctionCache();
    }

    private void clearAllCache() {
        Set<String> set = redisUtil.getSet(ALL_AUTH_KEY);
        if (CollectionUtils.isNotEmpty(set)) {
            redisUtil.del(set.toArray(new String[set.size()]));
        }
        redisUtil.del(ALL_AUTH_KEY);
    }

    public void initTenantInfoCache() {
        List<SysTenantInfoPO> tenantInfoPOList = sysTenantInfoMapper.selectList(Wrappers.lambdaQuery());

        if (CollectionUtils.isEmpty(tenantInfoPOList)) {
            return;
        }

        List<String> keys = new ArrayList<>(tenantInfoPOList.size());
        for (SysTenantInfoPO tenantInfoPO : tenantInfoPOList) {
            String key = TENANT_INFO_KEY_PREFIX + tenantInfoPO.getTenantCode();
            redisUtil.set(key, JSONUtil.toJsonStr(tenantInfoPO));
            keys.add(key);
        }
        addCacheKey(keys.toArray(new String[keys.size()]));
    }

    public SysTenantInfoPO getTenantInfo(String tenantCode) {
        if (StringUtils.isBlank(tenantCode)) {
            return null;
        }
        String key = TENANT_INFO_KEY_PREFIX + tenantCode;
        String value = redisUtil.get(key);
        if (StringUtils.isNotBlank(value)) {
            if (Const.NULL.equals(value)) {
                return null;
            }
            return JSONUtil.toBean(value, SysTenantInfoPO.class);
        }

        SysTenantInfoPO sysTenantInfoPO = sysTenantInfoMapper
                .selectOne(Wrappers.lambdaQuery(SysTenantInfoPO.class).eq(SysTenantInfoPO::getTenantCode, tenantCode));
        //如果db中不存在该租户，则放置"null"到redis中，防止缓存穿透，缓存5分钟
        if (Objects.isNull(sysTenantInfoPO)) {
            redisUtil.set(key, Const.NULL, FIVE_MINUTES_EXPIRE_SECONDS);
            addCacheKey(key);
            return null;
        }

        redisUtil.set(key, JSONUtil.toJsonStr(sysTenantInfoPO), ONE_DAY_EXPIRE_SECONDS);
        addCacheKey(key);
        return sysTenantInfoPO;
    }

    public void refreshSingleTenantInfo(String tenantCode) {
        if (StringUtils.isBlank(tenantCode)) {
            return;
        }
        String key = TENANT_INFO_KEY_PREFIX + tenantCode;
        redisUtil.del(key);

        SysTenantInfoPO sysTenantInfoPO = sysTenantInfoMapper
                .selectOne(Wrappers.lambdaQuery(SysTenantInfoPO.class).eq(SysTenantInfoPO::getTenantCode, tenantCode));
        //如果db中不存在该租户，则放置"null"到redis中，防止缓存穿透，缓存5分钟
        if (Objects.isNull(sysTenantInfoPO)) {
            redisUtil.set(key, Const.NULL, FIVE_MINUTES_EXPIRE_SECONDS);
            addCacheKey(key);
            return;
        }
        redisUtil.set(key, JSONUtil.toJsonStr(sysTenantInfoPO), ONE_DAY_EXPIRE_SECONDS);
        addCacheKey(key);
    }

    /**
     * 用户-角色缓存
     * 使用set缓存，key：systemCode_userId，set：roleId集合
     */
    public Map<String, Set<Long>> initUserRoleSetCache() {
        Set<String> keys = new HashSet<>();
        List<TenantUserRolePO> list = tenantUserRoleMapper.selectList(Wrappers.lambdaQuery());

        //key:systemCode_userId
        Map<String, Set<Long>> systemUserRoleMap = new HashMap<>();
        list.forEach(p -> {
            Long roleId = p.getRoleId();
            String systemCodeAndUserId = p.getSystemCode() + "_" + p.getUserId();
            if (systemUserRoleMap.containsKey(systemCodeAndUserId)) {
                systemUserRoleMap.get(systemCodeAndUserId).add(roleId);
            } else {
                Set<Long> roleIds = new HashSet<>();
                roleIds.add(roleId);
                systemUserRoleMap.put(systemCodeAndUserId, roleIds);
            }
        });

        if (MapUtils.isNotEmpty(systemUserRoleMap)) {
            systemUserRoleMap.forEach((k, v) -> {
                String key = getUserRoleSetCacheKey(k);
                keys.add(key);
                redisUtil.addSetAll(key, v.stream().map(l -> String.valueOf(l)).toArray());
            });
            addCacheKey(keys.toArray(new String[keys.size()]));
        }
        return systemUserRoleMap;
    }

    public Map<String, List<TenantFunctionDTO>> initFunctionCache() {
        List<TenantFunctionDTO> list = tenantFunctionMapper.listFunctions();

        //根据systemCode分组
        Map<String, List<TenantFunctionDTO>> systemFunctionMap = list.stream()
                .collect(Collectors.groupingBy(TenantFunctionDTO::getSystemCode));
        Iterator<Map.Entry<String, List<TenantFunctionDTO>>> iterator = systemFunctionMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<TenantFunctionDTO>> entry = iterator.next();
            String systemCode = entry.getKey();
            List<TenantFunctionDTO> funcDTOList = entry.getValue();
            String redisKey = getFunctionKey(systemCode);

            Map<String, TenantFunctionDTO> map = new LinkedHashMap<>();
            if (CollectionUtils.isNotEmpty(funcDTOList)) {
                funcDTOList.forEach(fun -> map.put(String.valueOf(fun.getFunctionCode()), fun));
            }
            redisUtil.hset(redisKey, map);
            addCacheKey(redisKey);
        }

        return systemFunctionMap;
    }

    /**
     * Map<Long,List<Long>> 角色-菜单缓存
     * 使用set缓存，set key：systemCode_roleId，set：menuId集合
     */
    public Map<String, Set<Long>> initRoleMenusCache() {
        List<TenantRoleMenuPO> list = tenantRoleMenuMapper.selectList(Wrappers.lambdaQuery());

        //systemCode_roleId
        Map<String, Set<Long>> systemRoleMenuMap = new HashMap<>();
        list.forEach(p -> {
            Long menuId = p.getMenuId();
            String systemCodeAndRoleId = p.getSystemCode() + "_" + p.getRoleId();
            if (systemRoleMenuMap.containsKey(systemCodeAndRoleId)) {
                systemRoleMenuMap.get(systemCodeAndRoleId).add(menuId);
            } else {
                Set<Long> roleIdSet = new HashSet<>();
                systemRoleMenuMap.put(systemCodeAndRoleId, roleIdSet);
                roleIdSet.add(menuId);
            }
        });

        if (MapUtils.isNotEmpty(systemRoleMenuMap)) {
            List<String> keys = new ArrayList<>(systemRoleMenuMap.size());
            systemRoleMenuMap.forEach((systemCodeAndRoleId, menus) -> {
                String key = getRoleMenuSetCacheKey(systemCodeAndRoleId);
                keys.add(key);
                redisUtil.addSetAll(key, menus.stream().map(l -> String.valueOf(l)).toArray());
            });
            addCacheKey(keys.toArray(new String[keys.size()]));
        }
        return systemRoleMenuMap;
    }

    /**
     * 获取角色相关菜单集合
     *
     * @param roleIds
     * @return
     */
    public Set<Long> getMenuIds(String systemCode,Collection<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return Collections.emptySet();
        }
        List<String> keys = roleIds.stream().map(roleId -> getRoleMenuSetCacheKey(systemCode,roleId)).collect(Collectors.toList());
        return redisUtil.union(keys);
    }

    /**
     * 从缓存获取菜单，不保证顺序
     *
     * @param roleIds
     * @return
     */
    public List<PrivilegeMenuDTO> getMenus(String systemCode,Collection<Long> roleIds) {
        String key = getMenuKey(systemCode);
        Set<Long> menuIds = getMenuIds(systemCode,roleIds);
        List<PrivilegeMenuDTO> list = redisUtil.hget(key, menuIds);
        return list.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 菜单对象缓存
     * 使用redis hash，hk：menuid,hv : SysMenuPO
     */
    public Map<String, List<PrivilegeMenuDTO>> initMenusCache() {
        //查询全部启用的菜单
        List<PrivilegeMenuPO> allMenuList = privilegeMenuMapper
                .selectList(Wrappers.lambdaQuery(PrivilegeMenuPO.class).eq(PrivilegeMenuPO::getDisable, Const.ZERO));
        List<PrivilegeMenuDTO> allMenuDTOList = allMenuList.stream().map(po -> {
            PrivilegeMenuDTO dto = new PrivilegeMenuDTO();
            dto.setMenuId(po.getMenuId());
            dto.setMenuLevel(po.getMenuLevel());
            dto.setIcon(po.getIcon());
            dto.setName(po.getName());
            dto.setSort(po.getSort());
            dto.setParentId(po.getParentId());
            dto.setUrl(po.getUrl());
            dto.setSystemCode(po.getSystemCode());
            return dto;
        }).collect(Collectors.toList());
        //根据systemCode分组
        Map<String, List<PrivilegeMenuDTO>> systemMenuMap = allMenuDTOList.stream()
                .collect(Collectors.groupingBy(PrivilegeMenuDTO::getSystemCode));
        Iterator<Map.Entry<String, List<PrivilegeMenuDTO>>> iterator = systemMenuMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<PrivilegeMenuDTO>> entry = iterator.next();
            String systemCode = entry.getKey();
            List<PrivilegeMenuDTO> menuDTOList = entry.getValue();
            String redisKey = getMenuKey(systemCode);

            Map<String, PrivilegeMenuDTO> map = new LinkedHashMap<>();
            if (CollectionUtils.isNotEmpty(menuDTOList)) {
                menuDTOList.forEach(menu -> map.put(String.valueOf(menu.getMenuId()), menu));
            }
            redisUtil.hset(redisKey, map);
            addCacheKey(redisKey);
        }
        return systemMenuMap;
    }

    /**
     * 构建角色-》功能缓存
     */
    private void initRoleFunctionCache() {
        List<TenantRoleFunctionPO> list = tenantRoleFunctionMapper.selectList(Wrappers.lambdaQuery());
        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        List<PrivilegeFunctionPO> functionList = privilegeFunctionMapper.selectList(Wrappers.lambdaQuery(PrivilegeFunctionPO.class).in(PrivilegeFunctionPO::getFunctionId, list.stream().map(TenantRoleFunctionPO::getFunctionId).distinct().collect(
                Collectors.toList())));
        Map<Long, PrivilegeFunctionPO> functionMap = functionList.stream().collect(Collectors.toMap(PrivilegeFunctionPO::getFunctionId,
                Function.identity()));

        //角色-功能
        Map<Long, Set<PrivilegeFunctionPO>> roleFunMap = new HashMap<>();
        list.stream().forEach(po -> {
            Long roleId = po.getRoleId();
            PrivilegeFunctionPO function = functionMap.get(po.getFunctionId());
            Set<PrivilegeFunctionPO> functions;
            if (roleFunMap.containsKey(roleId)) {
                functions = roleFunMap.get(roleId);
            } else {
                functions = new HashSet<>();
                roleFunMap.put(roleId, functions);
            }
            functions.add(function);
        });

        //缓存
        if (MapUtils.isNotEmpty(roleFunMap)) {
            List<String> keys = new ArrayList<>(roleFunMap.size());
            roleFunMap.forEach((k, v) -> {
                String key = getRoleFuntionSetCacheKey(k);
                keys.add(key);
                redisUtil.addSetAll(key, v.toArray());
            });
            addCacheKey(keys.toArray(new String[keys.size()]));
        }
    }

    /**
     * 更新角色-菜单缓存
     *
     * @param bo
     */
    public void updateRoleMenu(RoleMenuParamBO bo) {
        Long roleId = bo.getRoleId();
        //角色-菜单key
        String key = getRoleMenuSetCacheKey(bo.getSystemCode(),roleId);

        //1、更新角色-菜单
        //删除旧缓存
        redisUtil.del(key);
        List<Long> menuIds = bo.getMenuIds();
        if (menuIds == null) {
            menuIds = Collections.emptyList();
        }
        redisUtil.addSetAll(key, menuIds.stream().map(String::valueOf).toArray());
    }

    /**
     * 更新角色-功能缓存
     *
     * @param bo
     */
    public void updateRoleFunction(RoleFunctionParamBO bo) {
        Long roleId = bo.getRoleId();
        //角色-功能key
        String key = getRoleFuntionSetCacheKey(roleId);

        //更新角色-功能
        //删除旧缓存
        redisUtil.del(key);
        List<Long> functionIds = bo.getFunctionIds();
        if(CollectionUtils.isEmpty(functionIds)) {
            return;
        }
        List<PrivilegeFunctionPO> functionList = privilegeFunctionMapper.selectList(Wrappers.lambdaQuery(PrivilegeFunctionPO.class).in(PrivilegeFunctionPO::getFunctionId, functionIds));
        redisUtil.addSetAll(key, functionList.toArray(functionList.toArray(new PrivilegeFunctionPO[functionList.size()])));
    }

    /**
     * 更新用户-角色缓存
     *
     * @param bo
     */
    public void updateUserRoles(SysUserRolesParamBO bo) {
        List<Long> roleIds = bo.getRoleIds();
        Long userId = bo.getUserId();
        String systemCode = bo.getSystemCode();
        String key = getUserRoleSetCacheKey(systemCode,userId);
        //删除旧缓存
        redisUtil.del(key);
        //新增缓存
        if (CollectionUtils.isNotEmpty(roleIds)) {
            List<String> ids = roleIds.stream().map(String::valueOf).collect(Collectors.toList());
            redisUtil.addSetAll(key, ids.toArray());
        }
    }

    /**
     * 获取用户权限编码
     *
     * @param userId
     * @return
     */
    public Set<PrivilegeFunctionPO> getFunction(String systemCode, Long userId) {
        Set<Long> roleIds = getUserRoleIds(systemCode, userId);
        List<String> keys = roleIds.stream().map(this::getRoleFuntionSetCacheKey).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(keys)) {
            return redisUtil.union(keys);
        }
        return Collections.emptySet();
    }

    /**
     * 判断角色roleid是否拥有functionCode权限
     *
     * @param roleId
     * @param functionCode
     * @return
     */
    public boolean hasPermission(Long roleId, String functionCode) {
        Set<PrivilegeFunctionPO> functionSet = redisUtil.getSet(getRoleFuntionSetCacheKey(roleId));
        if(CollectionUtils.isEmpty(functionSet)) {
            return false;
        }
        return functionSet.stream().map(PrivilegeFunctionPO::getFunctionCode).collect(Collectors.toList()).contains(functionCode);
    }

    public boolean hasPermission(Collection<Long> roleIds, String functionCode) {
        if(CollectionUtils.isEmpty(roleIds)) {
            return false;
        }
        for (Long roleId : roleIds) {
            if (hasPermission(roleId, functionCode)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 返回用户关联角色id
     *
     * @param userId
     * @return
     */
    public Set<Long> getUserRoleIds(String systemCode, Long userId) {
        Set<String> roleids = redisUtil.getSet(getUserRoleSetCacheKey(systemCode, userId));
        return roleids.stream().map(Long::parseLong).collect(Collectors.toSet());
    }

    public SysAdmUserDTO getUserDetail(Long userId) {
        return redisUtil.get(getSysAdmUserKey(userId));
    }

    public void setUserDetailCache(SysAdmUserDTO userDTO) {
        redisUtil.set(getSysAdmUserKey(userDTO.getUserId()), userDTO, ONE_DAY_EXPIRE_SECONDS);
    }

    public void delUserDetailCache(Long userId){
        redisUtil.del(getSysAdmUserKey(userId));
    }

}