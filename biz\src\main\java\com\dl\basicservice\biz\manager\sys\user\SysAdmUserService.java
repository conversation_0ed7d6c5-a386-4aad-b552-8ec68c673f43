package com.dl.basicservice.biz.manager.sys.user;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.basicservice.biz.common.service.CommonService;
import com.dl.basicservice.biz.dal.sys.function.po.PrivilegeFunctionPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.manager.sys.user.bo.AddUserBO;
import com.dl.basicservice.biz.manager.sys.user.bo.ResetUserPasswordBO;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserSearchParamBO;
import com.dl.basicservice.biz.manager.sys.user.bo.UpdatePwdParamBO;
import com.dl.basicservice.biz.manager.sys.user.bo.UpdateUserSelfBO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserWithProfileDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysBasicUserDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface SysAdmUserService extends IService<TenantAdmUserPO>, CommonService {

    /**
     * 解析token
     *
     * @param token
     * @return
     */
    SysBasicUserDTO parseJwtToken(String token);

    SysBasicUserDTO directLogin(String account,String tenantCode,Long userId);

    SysAdmUserDTO findUserDetailFromCache(Long userId);

    /**
     * 刷新用户详情的缓存
     *
     * @param userId
     * @return
     */
    SysAdmUserDTO refreshUserDetailCache(Long userId);

    /**
     * 删除用户详情的缓存
     *
     * @param userId
     */
    void delUserDetailCache(Long userId);

    /**
     * token格式化
     *
     * @param token
     * @return
     */
    String formatToken(String token);

    /**
     * 从redis中获取会话token
     *
     * @param userId
     * @return
     */
    String getSessionToken(Long userId);

    /**
     * 刷新缓存过期时间
     * @param userId
     */
    void refreshToken(Long userId);

    /**
     * 是否拥有权限
     *
     * @param roleIds
     * @param functionCode 权限代码
     * @return
     */
    boolean hasPermission(Collection<Long> roleIds, String functionCode);

    Set<PrivilegeFunctionPO> getUserFunctions(String systemCode, Long userId);

    /**
     * 重置用户密码
     * @param bo
     * @return
     */
    boolean resetUserPassword(ResetUserPasswordBO bo);

    SysBasicUserDTO login(TenantAdmUserPO po);

    /**
     * 登出
     *
     * @param token
     */
    void logoutJwtToken(String token);

    /**
     * 根据userId删除redis中的session
     * @param userId
     */
    void removeLoginSession(Long userId);

    String createJwtToken(SysBasicUserDTO dto);

    IPage<SysAdmUserWithProfileDTO> findUsers(SysUserSearchParamBO bo);

    /**
     * 修改密码
     *
     * @param bo
     */
    boolean updatePassword(UpdatePwdParamBO bo);

    /**
     * 更新用户信息
     *
     * @param po
     */
    void updateUserDetail(TenantAdmUserPO po,String userName);

    /**
     * 用户修改个人信息(仅供用户修改自己信息)
     * @param bo
     * @return
     */
    boolean updateUserSelfDetail(UpdateUserSelfBO bo);

    /**
     * 新增后台用户
     * @param bo
     * @param roleIds
     * @return
     */
    Long addUser(AddUserBO bo,List<Long> roleIds);

    /**
     * 获取资源中心token
     *
     * @param userId
     * @return
     */
    String getRcToken(Long userId, String userName, String tenantCode);

}
