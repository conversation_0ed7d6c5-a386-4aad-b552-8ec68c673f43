package com.dl.basicservice.biz.dal.sys.menu.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("sys_privilege_menu_function")
public class PrivilegeMenuFunctionPO extends BasePO {

    @TableId("id")
    public Long id;

    @TableField("menu_id")
    private Long menuId;

    @TableField("function_id")
    private Long functionId;

    /**
     * 系统code，如dl-wealth-center
     */
    @TableField("system_code")
    private String systemCode;

}
