package com.dl.basicservice.web.controller.adm.sys.role.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel("小程序新增角色配置")
public class MinAppRoleParam {

    @ApiModelProperty("角色主键")
    private String roleId;

    @ApiModelProperty("角色名称")
    @Size(min = 0, max = 10)
    private String name;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("菜单主键数组")
    private List<String> menuIds;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("功能编码数组")
    private List<String> functionIds;
}
