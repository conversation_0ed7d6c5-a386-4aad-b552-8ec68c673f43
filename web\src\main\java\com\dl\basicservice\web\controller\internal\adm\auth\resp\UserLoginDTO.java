package com.dl.basicservice.web.controller.internal.adm.auth.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 09:59
 */
@Data
public class UserLoginDTO implements Serializable {
    private static final long serialVersionUID = -617960309925460466L;

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("token")
    private String token;

    private String tenantCode;
}
