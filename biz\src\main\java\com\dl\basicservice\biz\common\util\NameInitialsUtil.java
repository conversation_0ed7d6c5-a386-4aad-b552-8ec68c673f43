package com.dl.basicservice.biz.common.util;

import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName NameInitialsUtil
 * @Description 名称首字母提取工具类
 * <AUTHOR>
 * @Date 2022/11/9 14:12
 * @Version 1.0
 **/
public class NameInitialsUtil {

    private static final Pattern P = Pattern.compile("[\u4e00-\u9fa5]");

    public static String get(String sourceStr) {
        if (StringUtils.isBlank(sourceStr)) {
            return sourceStr;
        }
        if (!isContainChinese(sourceStr)) {
            return StringUtils.EMPTY;
        }
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        StringBuilder firstPinyin = new StringBuilder();
        char[] chineseArr = sourceStr.trim().toCharArray();
        try {
            for (int i = 0, len = chineseArr.length; i < len; i++) {
                if (Character.toString(chineseArr[i]).matches("[\\u4E00-\\u9FA5]+")) {
                    String[] pys = PinyinHelper.toHanyuPinyinStringArray(chineseArr[i], format);
                    firstPinyin.append(pys[0].charAt(0));
                }
            }
        } catch (BadHanyuPinyinOutputFormatCombination badHanyuPinyinOutputFormatCombination) {
            badHanyuPinyinOutputFormatCombination.printStackTrace();
        }
        return firstPinyin.toString();
    }

    public static boolean isContainChinese(String str) {
        Matcher m = P.matcher(str);
        if (m.find()) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }
}
