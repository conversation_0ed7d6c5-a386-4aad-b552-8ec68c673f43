package com.dl.basicservice.web.controller.adm.sys.admuser.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("新增后台账号(通讯录员工关联)信息")
public class AddUserEmployeeParam {

    @NotNull @Size(min = 1, max = 64, message = "员工名称长度需在1-64位之间")
    @ApiModelProperty("员工名称")
    String name;

    @ApiModelProperty("手机号")
    String mobile;

    @NotBlank
    @Size(min = 4, max = 36, message = "登录账号长度需在4-36位之间")
    @ApiModelProperty("登录账号")
    String account;

    @NotBlank @Length(min = 6, max = 12, message = "密码长度必须在6-12位之间")
    @ApiModelProperty("登录密码")
    String password;

    @ApiModelProperty("角色id")
    @Size(max = 10)
    private List<Long> roleIds;
}
