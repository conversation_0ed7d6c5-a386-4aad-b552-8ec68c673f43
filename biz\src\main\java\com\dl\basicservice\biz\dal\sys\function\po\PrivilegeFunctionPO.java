package com.dl.basicservice.biz.dal.sys.function.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.Data;

/**
 * 功能
 */
@Data
@TableName("sys_privilege_function")
public class PrivilegeFunctionPO extends BasePO {
    @TableId("id")
    public Long id;

    /**
     * 功能id
     */
    @TableField("function_id")
    private Long functionId;

    /**
     * 功能名称
     */
    @TableField("name")
    private String name;

    /**
     * 图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 权限编码
     */
    @TableField("function_code")
    private String functionCode;

    /**
     * 显示顺序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 系统code，如dl-wealth-center
     */
    @TableField("system_code")
    private String systemCode;
}
