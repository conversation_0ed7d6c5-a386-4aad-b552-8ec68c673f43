package com.dl.basicservice.biz.manager.sys.menu;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.basicservice.biz.common.service.CommonService;
import com.dl.basicservice.biz.dal.sys.menu.po.TenantMenuPO;
import com.dl.basicservice.biz.manager.sys.menu.bo.TenantMenuSaveParamBO;

public interface TenantMenuManager extends IService<TenantMenuPO>, CommonService {

    /**
     * 保存租户权限信息
     *
     * @param bo
     */
    Boolean saveTenantMenu(TenantMenuSaveParamBO bo);

}
