CREATE TABLE `sys_tenant_admuser_bind` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `tenant_code` varchar(100) NOT NULL COMMENT '租户编码',
  `ext_user_id` varchar(255) NOT NULL COMMENT '外部用户id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除，0-否，1-是',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_tenantCode_extUserId` (`tenant_code`,`ext_user_id`) USING BTREE,
  UNIQUE KEY `uniq_tenantCode_userId` (`tenant_code`,`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='外部用户绑定表';