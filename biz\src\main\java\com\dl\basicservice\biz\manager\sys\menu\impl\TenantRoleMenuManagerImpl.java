package com.dl.basicservice.biz.manager.sys.menu.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.dal.sys.function.TenantRoleFunctionMapper;
import com.dl.basicservice.biz.dal.sys.function.po.TenantRoleFunctionPO;
import com.dl.basicservice.biz.dal.sys.menu.TenantRoleMenuMapper;
import com.dl.basicservice.biz.dal.sys.menu.po.TenantRoleMenuPO;
import com.dl.basicservice.biz.dal.sys.role.TenantRoleMapper;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.manager.sys.role.enums.RoleTypeEnum;
import com.dl.basicservice.biz.manager.sys.menu.TenantRoleMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.assist.MenuTreeHelp;
import com.dl.basicservice.biz.manager.sys.menu.bo.MinAppRoleMenuParamBO;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleIdsMenuParamBO;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleMenuParamBO;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.biz.manager.sys.role.bo.RoleFunctionParamBO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class TenantRoleMenuManagerImpl extends ServiceImpl<TenantRoleMenuMapper, TenantRoleMenuPO>
        implements TenantRoleMenuManager {

    @Autowired
    private TenantRoleFunctionMapper sysRoleFunctionMapper;

    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @Autowired
    private TenantRoleManager tenantRoleManager;

    @Autowired
    private TenantRoleMapper roleMapper;

    @Autowired
    private HostTimeIdg hostTimeIdg;

    @Override
    public boolean hasMenu(Long roleId) {
        Assert.notNull(roleId, "角色ID不能为空");
        Integer count = baseMapper.selectCount(
                Wrappers.<TenantRoleMenuPO>lambdaQuery().eq(TenantRoleMenuPO::getRoleId, roleId));
        return count > 0;
    }

    private void restRoleMenu(RoleMenuParamBO bo) {
        Long roleId = bo.getRoleId();
        Long loginUserId = bo.getLoginUserId();
        List<Long> menuIds = bo.getMenuIds();
        String systemCode = bo.getSystemCode();
        //1、处理角色-菜单
        LambdaQueryWrapper<TenantRoleMenuPO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(TenantRoleMenuPO::getRoleId, roleId);
        wrappers.eq(TenantRoleMenuPO::getSystemCode, bo.getSystemCode());
        baseMapper.delete(wrappers);
        if (CollectionUtils.isNotEmpty(menuIds)) {
            List<TenantRoleMenuPO> list = menuIds.stream().map(menuId -> {
                TenantRoleMenuPO po = new TenantRoleMenuPO();
                po.setCreateBy(loginUserId);
                po.setRoleId(roleId);
                po.setMenuId(menuId);
                po.setSystemCode(systemCode);
                return po;
              }).collect(Collectors.toList());
            saveBatch(list);
        }
        //2、更新缓存
        userRoleMenuRedisCache.updateRoleMenu(bo);
    }

    private void restRoleFunction(RoleMenuParamBO bo) {
        Long roleId = bo.getRoleId();
        Long loginUserId = bo.getLoginUserId();
        List<Long> functionIds = bo.getFunctionIds();
        String systemCode = bo.getSystemCode();
        RoleFunctionParamBO roleFunctionParamBO = new RoleFunctionParamBO();
        roleFunctionParamBO.setRoleId(roleId);
        roleFunctionParamBO.setFunctionIds(functionIds);
        roleFunctionParamBO.setSystemCode(systemCode);
        //1、处理角色-功能
        LambdaQueryWrapper<TenantRoleFunctionPO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(TenantRoleFunctionPO::getRoleId, roleId);
        wrappers.eq(TenantRoleFunctionPO::getSystemCode, bo.getSystemCode());
        sysRoleFunctionMapper.delete(wrappers);
        if (CollectionUtils.isNotEmpty(functionIds)) {
            List<TenantRoleFunctionPO> list = functionIds.stream().map(f -> {
                        TenantRoleFunctionPO po = new TenantRoleFunctionPO();
                        po.setCreateBy(loginUserId);
                        po.setRoleId(roleId);
                        po.setFunctionId(f);
                        po.setSystemCode(systemCode);
                        return po;
                    })
                    .collect(Collectors.toList());
            for (TenantRoleFunctionPO po : list) {
                sysRoleFunctionMapper.insert(po);
            }
        }
        //更新缓存
        userRoleMenuRedisCache.updateRoleFunction(roleFunctionParamBO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRoleMenu(RoleMenuParamBO bo) {
        MenuTreeHelp.validate(roleMapper, Lists.newArrayList(bo.getRoleId()), TenantRolePO::getRoleId, "roleId参数非法");
        restRoleMenu(bo);
        restRoleFunction(bo);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delRoleMenu(RoleIdsMenuParamBO bo) {
        deleteRoleMenu(bo);
        deleteRoleFunction(bo);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String addMiniAppRoleMenu(MinAppRoleMenuParamBO bo) {
        //新增角色
        TenantRolePO rolePO = new TenantRolePO();
        rolePO.setRoleType(RoleTypeEnum.NORMAL.getCode());
        rolePO.setName(bo.getName());
        rolePO.setTenantCode(bo.getTenantCode());
        rolePO.setRoleId(hostTimeIdg.generateId().longValue());
        rolePO.setSystemCode(bo.getSystemCode());
        tenantRoleManager.save(rolePO);
        //保存角色菜单以及权限点位
        RoleMenuParamBO roleMenuParamBO = new RoleMenuParamBO();
        roleMenuParamBO.setMenuIds(bo.getMenuIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        roleMenuParamBO.setFunctionIds(bo.getFunctionIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        roleMenuParamBO.setRoleId(rolePO.getRoleId());
        roleMenuParamBO.setSystemCode(bo.getSystemCode());
        this.saveRoleMenu(roleMenuParamBO);
        return String.valueOf(rolePO.getRoleId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String editMiniAppRoleMenu(MinAppRoleMenuParamBO bo) {
        //修改角色名称
        TenantRolePO rolePO = tenantRoleManager.getOne(
                Wrappers.lambdaQuery(TenantRolePO.class).eq(TenantRolePO::getRoleId, Long.valueOf(bo.getRoleId())));
        Assert.notNull(rolePO,"修改的角色不能为空");
        rolePO.setName(bo.getName());
        tenantRoleManager.updateById(rolePO);
        //修改角色对应权限
        RoleMenuParamBO roleMenuParamBO = new RoleMenuParamBO();
        if (CollectionUtils.isNotEmpty(bo.getMenuIds())){
            roleMenuParamBO.setMenuIds(bo.getMenuIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(bo.getFunctionIds())){
            roleMenuParamBO.setFunctionIds(bo.getFunctionIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        roleMenuParamBO.setRoleId(rolePO.getRoleId());
        roleMenuParamBO.setSystemCode(bo.getSystemCode());
        this.saveRoleMenu(roleMenuParamBO);
        return bo.getRoleId();
    }

    private void deleteRoleMenu(RoleIdsMenuParamBO bo) {
        List<Long> roleIds = bo.getRoleIds();
        List<Long> menuIds = bo.getMenuIds();

        //1、获取角色原有菜单
        LambdaQueryWrapper<TenantRoleMenuPO> q = Wrappers.lambdaQuery();
        q.in(TenantRoleMenuPO::getRoleId, roleIds);
        List<TenantRoleMenuPO> cmenus = baseMapper.selectList(q);
        List<Long> delMenuIds = new ArrayList<>();
        List<Long> updateMenuIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(cmenus)){
            cmenus.forEach(m -> {
                if(menuIds.contains(m.getMenuId())){
                    updateMenuIds.add(m.getMenuId());
                }else{
                    delMenuIds.add(m.getMenuId());
                }
            });
        }

        //2.删除已去掉权限的菜单
        if(CollectionUtils.isNotEmpty(delMenuIds)){
            LambdaQueryWrapper<TenantRoleMenuPO> wrappers = Wrappers.lambdaQuery();
            wrappers.in(TenantRoleMenuPO::getRoleId, roleIds).in(TenantRoleMenuPO::getMenuId, delMenuIds);
            baseMapper.delete(wrappers);
        }

        //3.更新缓存
        if(CollectionUtils.isNotEmpty(updateMenuIds)){
            roleIds.forEach(r->{
                RoleMenuParamBO roleMenuParamBO = new RoleMenuParamBO();
                roleMenuParamBO.setMenuIds(updateMenuIds);
                roleMenuParamBO.setRoleId(r);
                roleMenuParamBO.setLoginUserId(bo.getLoginUserId());
                roleMenuParamBO.setSystemCode(bo.getSystemCode());
                userRoleMenuRedisCache.updateRoleMenu(roleMenuParamBO);
            });
        }
    }

    private void deleteRoleFunction(RoleIdsMenuParamBO bo) {
        List<Long> roleIds = bo.getRoleIds();
        List<Long> functionIds = bo.getFunctionIds();

        //1、获取角色原有功能权限
        LambdaQueryWrapper<TenantRoleFunctionPO> q = Wrappers.lambdaQuery();
        q.in(TenantRoleFunctionPO::getRoleId, roleIds);
        List<TenantRoleFunctionPO> cfs = sysRoleFunctionMapper.selectList(q);
        List<Long> delFunctionIds = new ArrayList<>();
        List<Long> updateFunctionIds = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(cfs)){
            cfs.forEach(m -> {
                if(functionIds.contains(m.getFunctionId())){
                    updateFunctionIds.add(m.getFunctionId());
                }else{
                    delFunctionIds.add(m.getFunctionId());
                }
            });
        }

        //2.删除已去掉权限的功能权限
        if(CollectionUtils.isNotEmpty(delFunctionIds)){
            LambdaQueryWrapper<TenantRoleFunctionPO> wrappers = Wrappers.lambdaQuery();
            wrappers.in(TenantRoleFunctionPO::getRoleId, roleIds).in(TenantRoleFunctionPO::getFunctionId, delFunctionIds);
            sysRoleFunctionMapper.delete(wrappers);
        }

        //3.更新缓存
        if(CollectionUtils.isNotEmpty(updateFunctionIds)){
            roleIds.forEach(r->{
                RoleFunctionParamBO roleFunctionParamBO = new RoleFunctionParamBO();
                roleFunctionParamBO.setFunctionIds(updateFunctionIds);
                roleFunctionParamBO.setRoleId(r);
                roleFunctionParamBO.setLoginUserId(bo.getLoginUserId());
                roleFunctionParamBO.setSystemCode(bo.getSystemCode());
                userRoleMenuRedisCache.updateRoleFunction(roleFunctionParamBO);
            });
        }
    }
}
