package com.dl.basicservice.web.controller.adm.sys.tenant.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("用户菜单信息")
public class UserMenuVO {
    @ApiModelProperty("菜单主键")
    private String menuId;

    @ApiModelProperty("菜单名称")
    private String name;

    @ApiModelProperty("菜单图标")
    private String icon;

    @ApiModelProperty("菜单url")
    private String url;

    @ApiModelProperty("菜单顺序")
    private Integer sort;

    @ApiModelProperty("菜单等级【1-一级菜单2-二级菜单···】")
    private Integer level;

    @ApiModelProperty("上级菜单id")
    private String parentId;

    @ApiModelProperty("子菜单")
    private List<UserMenuVO> children = new ArrayList<>();
}
