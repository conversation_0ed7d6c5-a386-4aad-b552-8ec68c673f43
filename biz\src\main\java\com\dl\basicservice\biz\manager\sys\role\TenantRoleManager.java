package com.dl.basicservice.biz.manager.sys.role;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.basicservice.biz.common.service.CommonService;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.manager.sys.role.bo.RoleParamBO;
import com.dl.basicservice.biz.manager.sys.role.bo.RoleSearchParamBO;
import com.dl.basicservice.biz.manager.sys.role.dto.SysRoleDTO;

import java.util.List;
import java.util.Set;

public interface TenantRoleManager extends IService<TenantRolePO>, CommonService {

    /**
     * 员工是否拥有该角色
     *
     * @param roleId
     * @return
     */
    boolean belongUsers(Long roleId);

    IPage<SysRoleDTO> findRoles(RoleSearchParamBO roleParamBO);

    void deleteRole(Long roleId);

    void update(RoleParamBO roleParamBO);

    /**
     * 设置默认角色
     *
     * @param tenantCode
     * @param systemCode
     * @param roleId
     */
    void setDefaultRole(String tenantCode,String systemCode,Long roleId);

    /**
     * 查询租户在指定systemCode下的默认角色
     *
     * @param tenantCode
     * @param systemCode
     * @return
     */
    SysRoleDTO queryDefaultRole(String tenantCode,String systemCode);

    /**
     * 查询租户的默认角色列表
     *
     * @param tenantCode
     * @return
     */
    List<SysRoleDTO> queryDefaultRoles(String tenantCode);
}
