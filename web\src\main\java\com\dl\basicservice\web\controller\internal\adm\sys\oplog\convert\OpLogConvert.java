package com.dl.basicservice.web.controller.internal.adm.sys.oplog.convert;

import com.dl.basicservice.biz.dal.sys.oplog.po.OpLogPO;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogAddBO;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogPageBO;
import com.dl.basicservice.web.controller.internal.adm.sys.oplog.dto.OpLogDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.oplog.req.OpLogAddReq;
import com.dl.basicservice.web.controller.internal.adm.sys.oplog.req.OpLogPageReq;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 15:37
 */
public class OpLogConvert {

    public static OpLogAddBO cnvOpLogAddReq2BO(OpLogAddReq input) {
        OpLogAddBO result = new OpLogAddBO();
        result.setBizCode(input.getBizCode());
        result.setTenantCode(input.getTenantCode());
        result.setOpObject(input.getOpObject());
        result.setOpKey(input.getOpKey());
        result.setOpType(input.getOpType());
        result.setOpBefore(input.getOpBefore());
        result.setOpAfter(input.getOpAfter());
        result.setRemark(input.getRemark());
        result.setOpUserId(input.getOpUserId());
        result.setOpUserName(input.getOpUserName());
        result.setOpDt(input.getOpDt());
        result.setExtData(input.getExtData());
        return result;
    }

    public static OpLogPageBO cnvOpLogPageReq2BO(OpLogPageReq input){
        OpLogPageBO result = new OpLogPageBO();
        result.setBizCode(input.getBizCode());
        result.setTenantCode(input.getTenantCode());
        result.setOpObject(input.getOpObject());
        result.setOpKey(input.getOpKey());
        result.setOpType(input.getOpType());
        result.setOpUserId(input.getOpUserId());
        result.setOpSince(input.getOpSince());
        result.setOpUntil(input.getOpUntil());
        result.setPageIndex(input.getPageIndex());
        result.setPageSize(input.getPageSize());
        return result;
    }

    public static OpLogDTO cnvOpLogPO2DTO(OpLogPO input){
        OpLogDTO result = new OpLogDTO();
        result.setLogId(input.getLogId());
        result.setTenantCode(input.getTenantCode());
        result.setBizCode(input.getBizCode());
        result.setOpObject(input.getOpObject());
        result.setOpKey(input.getOpKey());
        result.setOpType(input.getOpType());
        result.setOpBefore(input.getOpBefore());
        result.setOpAfter(input.getOpAfter());
        result.setRemark(input.getRemark());
        result.setOpUserId(input.getOpUserId());
        result.setOpUserName(input.getOpUserName());
        result.setOpDt(input.getOpDt());
        result.setExtData(input.getExtData());
        return result;
    }
}
