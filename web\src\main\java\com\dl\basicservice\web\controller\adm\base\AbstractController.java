package com.dl.basicservice.web.controller.adm.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysBasicUserDTO;
import com.dl.basicservice.web.util.AccountCompent;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.controller.BaseController;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Set;
import java.util.function.Function;

import static com.dl.basicservice.biz.common.constant.Const.DEFAULT_SYSTEM_CODE;
import static com.dl.basicservice.biz.common.constant.Const.SYSTEM_CODE;
import static com.dl.basicservice.biz.common.constant.Const.TOKEN_HEADER_NAME;

/**
 * @describe: AbstractController
 * @author: zhousx
 * @date: 2022/5/21 15:22
 */
public class AbstractController extends BaseController {
    @Resource
    private AccountCompent accountCompent;

    @Resource
    private HttpServletRequest request;

    @Resource
    private TenantInfoManager tenantInfoManager;

    protected SysBasicUserDTO getCurrentUser() {
        return accountCompent.getUser();
    }

    protected SysAdmUserDTO getCurrentDetail() {
        return accountCompent.getCurrentDetail();
    }

    protected Set<Long> getUserRoleids() {
        return accountCompent.getRoleIds(getSystemCode());
    }

    protected String getTenantCode() {
        return accountCompent.getUser().getTenantCode();
    }

    protected Long getUserId() {
        return getCurrentUser().getUserId();
    }

    protected <T> ResponsePageQueryDO<T> pageQueryDO(IPage<T> page) {
        ResponsePageQueryDO qd = new ResponsePageQueryDO();
        qd.setPageIndex(page.getCurrent());
        qd.setPageSize(page.getSize());
        qd.setTotal(page.getTotal());
        qd.setDataResult(page.getRecords());
        return qd;
    }

    protected <T> ResultPageModel<T> pageQueryModel(IPage<T> page) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(page.getRecords());
        return model;
    }

    protected <T, R> ResultPageModel<R> pageQueryModel(IPage<T> page, Function<T, R> function) {
        ResultPageModel<R> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(page.convert(t -> function.apply(t)).getRecords());
        return model;
    }

    protected <T> ResultPageModel<T> pageQueryModel(ResponsePageQueryDO page, Collection<T> records) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getPageIndex());
        model.setPageSize(page.getPageSize());
        model.setTotalPage(page.getTotalPage());
        model.setTotal(page.getTotal());
        model.setDataResult(records);
        return model;
    }

    protected <T> ResultPageModel<T> pageQueryModel(IPage page, Collection<T> records) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(records);
        return model;
    }

    @Override
    public String getToken() {
        return request.getHeader(TOKEN_HEADER_NAME);
    }

    public String getSystemCode() {
        String systemCode = request.getHeader(SYSTEM_CODE);
        if(StringUtils.isBlank(systemCode)){
            systemCode = DEFAULT_SYSTEM_CODE;
        }
        return systemCode;
    }
}
