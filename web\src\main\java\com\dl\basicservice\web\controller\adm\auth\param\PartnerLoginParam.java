package com.dl.basicservice.web.controller.adm.auth.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-02 14:56
 */
@Data
@ApiModel("合作方联合登录参数")
public class PartnerLoginParam {

    @NotBlank(message = "合作方信息token不能为空")
    @ApiModelProperty("合作方信息token")
    private String token;

    @NotBlank(message = "租户编码不能为空")
    @ApiModelProperty("租户编码")
    private String tenantCode;

}
