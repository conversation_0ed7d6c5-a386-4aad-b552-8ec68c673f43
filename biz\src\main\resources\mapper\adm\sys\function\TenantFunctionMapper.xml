<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dl.basicservice.biz.dal.sys.function.TenantFunctionMapper">

    <select id="listFunctions" resultType="com.dl.basicservice.biz.dal.sys.function.dto.TenantFunctionDTO">
        SELECT m.tenant_code AS tenantCode,f.function_id AS functionId,f.function_code AS functionCode,f.name,f.icon,f.sort ,f.system_code
        FROM sys_tenant_function m,sys_privilege_function f
        WHERE m.function_id  = f.function_id
    </select>

</mapper>