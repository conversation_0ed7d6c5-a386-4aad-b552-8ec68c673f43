<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.dl.basic-service</groupId>
    <artifactId>share</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

<!--    <distributionManagement>-->
<!--        <repository>-->
<!--            <id>deeplinktech-wealthpartner-maven</id>-->
<!--            <name>maven</name>-->
<!--            <url>https://deeplinktech-maven.pkg.coding.net/repository/wealthpartner/maven/</url>-->
<!--        </repository>-->
<!--    </distributionManagement>-->

    <build>
        <finalName>${artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>
