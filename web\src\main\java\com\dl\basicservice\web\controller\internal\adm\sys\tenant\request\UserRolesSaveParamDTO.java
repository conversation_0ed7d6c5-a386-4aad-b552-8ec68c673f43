package com.dl.basicservice.web.controller.internal.adm.sys.tenant.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-17 11:05
 */
@Data
public class UserRolesSaveParamDTO implements Serializable {

    @NotNull(message = "userId不能为空")
    private Long userId;

    @NotBlank(message = "租户编号不能为空")
    private String tenantCode;

    @NotEmpty(message = "角色id列表不能为空")
    private List<Long> roleIds;

}
