package com.dl.basicservice.web.controller.adm.sys.tenant.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

/**
 * @describe: AddTenantParam
 * @author: zhousx
 * @date: 2022/7/7 9:34
 */
@Data
public class AddTenantParam {

    @ApiModelProperty(value = "租户名称", required = true)
    @NotBlank(message = "租户名称必填")
    private String name;

    @ApiModelProperty("租户访问路径片段，常用于本地化部署")
    private String pathPart;

    @ApiModelProperty("是否服务商： 0 否 ；1 是")
    private Integer isProvider;

    @Length(max = 512, message = "logo图片地址过长")
    @ApiModelProperty("logo图片")
    private String logoImg;

    @ApiModelProperty("是否企微入驻租户：0 否； 1 是")
    private Integer isWeWork;

    @ApiModelProperty("租户信息描述")
    private String tenantDesc;

    @ApiModelProperty("授权模式，1-微信全托管模式，2-微信第三方平台")
    private Integer authMode;

    @ApiModelProperty("页面小logo")
    private String smallLogo;

    /**
     * 模拟本地化预览 0-否，1-是
     * 像安信这类客户，要 抹除 所有和 “定力数影”有关的设置，比如banner图不一样等等的
     */
    @ApiModelProperty("模拟本地化预览 0-否，1-是")
    private Integer simulateLocalPreview;

}
