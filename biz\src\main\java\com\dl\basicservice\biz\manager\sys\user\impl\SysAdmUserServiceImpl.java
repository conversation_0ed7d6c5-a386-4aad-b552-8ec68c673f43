package com.dl.basicservice.biz.manager.sys.user.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.constant.RcConsts;
import com.dl.basicservice.biz.common.enums.SymbolE;
import com.dl.basicservice.biz.common.properties.session.SessionProperty;
import com.dl.basicservice.biz.common.util.JwtUtil;
import com.dl.basicservice.biz.common.util.OperatorUtil;
import com.dl.basicservice.biz.common.util.RedisUtil;
import com.dl.basicservice.biz.common.util.RsaUtil;
import com.dl.basicservice.biz.dal.sys.function.po.PrivilegeFunctionPO;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.dal.sys.user.TenantAdmUserMapper;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserProfilePO;
import com.dl.basicservice.biz.manager.sys.menu.impl.UserRoleMenuRedisCache;
import com.dl.basicservice.biz.manager.sys.role.TenantUserRoleManager;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.TenantUserProfileManager;
import com.dl.basicservice.biz.manager.sys.user.bo.AddUserBO;
import com.dl.basicservice.biz.manager.sys.user.bo.ResetUserPasswordBO;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserRolesParamBO;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserSearchParamBO;
import com.dl.basicservice.biz.manager.sys.user.bo.UpdatePwdParamBO;
import com.dl.basicservice.biz.manager.sys.user.bo.UpdateUserSelfBO;
import com.dl.basicservice.biz.manager.sys.user.dto.RcTenantAuthTokenDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserWithProfileDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysBasicUserDTO;
import com.dl.basicservice.biz.manager.sys.user.enums.SysUserStatusEnum;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import io.jsonwebtoken.Claims;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RefreshScope
public class SysAdmUserServiceImpl extends ServiceImpl<TenantAdmUserMapper, TenantAdmUserPO>
        implements SysAdmUserService {

    String TOKEN_KEY_PREFIX = "dl.admUser.token.";

    private static final String SUPER_ADMIN = "admin";

    private final String subject = "dl";

    private static final String RESOURCE_CENTER_TOKEN_KEY_PREFIX = "dl-resource-token:";
    private static final Logger LOGGER = LoggerFactory.getLogger(SysAdmUserServiceImpl.class);

    private final Long RC_TOKEN_CACHE_EXPIRE_TIME = 1 * 60 * 60L;


    private static final SecureRandom secureRandom = new SecureRandom();

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @Autowired
    private SessionProperty sessionProperty;

    @Autowired
    private TenantUserProfileManager tenantUserProfileManager;

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Autowired
    private TenantUserRoleManager iSysUserRoleService;

    @Resource
    private TenantInfoManager tenantInfoManager;

    @Resource
    private OperatorUtil operatorUtil;

    @Override
    public SysBasicUserDTO parseJwtToken(String token) {
        token = formatToken(token);
        Claims claims = JwtUtil.parseJWT(token);
        Long id = claims.get("id", Long.class);
        //        String sessionToken = getTokenBySession(id);
        //        //判断是否被踢
        //        if (!token.equals(sessionToken)) {
        //            return null;
        //        }
        String account = claims.get("account", String.class);
        String tenantCode = claims.get("tenantCode", String.class);

        return SysBasicUserDTO.builder().userId(id).account(account).tenantCode(tenantCode).build();
    }

    @Override
    public SysBasicUserDTO directLogin(String account, String tenantCode, Long userId) {
        SysBasicUserDTO dto = SysBasicUserDTO.builder().account(account).userId(userId).tenantCode(tenantCode).build();
        //生成用户登录token
        String token = this.createJwtToken(dto);
        if ("YJSQ".equals(dto.getTenantCode()) || "YJ".equals(dto.getTenantCode())){
            //将token放入缓存
            this.setLoginSessionWithOutExpire(dto.getUserId(), token);
        } else {
            this.setLoginSession(dto.getUserId(), token);
        }
        dto.setToken(token);
        return dto;
    }

    @Override
    public SysAdmUserDTO findUserDetailFromCache(Long userId) {
        SysAdmUserDTO dto = userRoleMenuRedisCache.getUserDetail(userId);
        if (dto != null) {
            return dto;
        }
        return this.refreshUserDetailCache(userId);
    }

    @Override
    public SysAdmUserDTO refreshUserDetailCache(Long userId) {
        TenantAdmUserPO po = baseMapper.selectOne(
                Wrappers.lambdaQuery(TenantAdmUserPO.class).eq(TenantAdmUserPO::getUserId, userId));
        TenantUserProfilePO userProfilePO = tenantUserProfileManager.lambdaQuery()
                .eq(TenantUserProfilePO::getUserId, userId).eq(TenantUserProfilePO::getIsDeleted, Const.ZERO)
                .one();
        Assert.notNull(po, "未找到指定系统用户，请联系管理员");
        SysAdmUserDTO dto = this.convert(po);
        if (Objects.nonNull(userProfilePO)) {
            dto.setUserName(userProfilePO.getName());
        }
        userRoleMenuRedisCache.setUserDetailCache(dto);
        return dto;
    }

    @Override
    public void delUserDetailCache(Long userId) {
        Assert.notNull(userId,"userId不能为空");
       userRoleMenuRedisCache.delUserDetailCache(userId);
    }

    private SysAdmUserDTO convert(TenantAdmUserPO t) {
        SysAdmUserDTO dto = SysAdmUserDTO.builder().isSuperAdm(t.getIsSuperAdm()).tenantCode(t.getTenantCode()).userId(t.getUserId())
                .account(t.getAccount()).status(t.getStatus()).createBy(t.getCreateBy()).modifyBy(t.getModifyBy()).build();
        return dto;
    }

    @Override
    public String formatToken(String token) {
        return token.substring(token.indexOf(" ") + 1);
    }

    @Override
    public String getSessionToken(Long userId) {
        return redisUtil.get(getSessionKey(userId));
    }

    private String getSessionKey(Long userId) {
        return TOKEN_KEY_PREFIX + userId;
    }

    @Override
    public void refreshToken(Long userId) {
        redisUtil.expire(getSessionKey(userId), sessionProperty.getAdm().getExpire());
    }

    @Override
    public boolean hasPermission(Collection<Long> roleIds, String functionCode) {
        return userRoleMenuRedisCache.hasPermission(roleIds, functionCode);
    }

    @Override
    public Set<PrivilegeFunctionPO> getUserFunctions(String systemCode, Long userId) {
        return userRoleMenuRedisCache.getFunction(systemCode, userId);
    }

    @Override
    public boolean resetUserPassword(ResetUserPasswordBO bo) {
        Assert.notNull(bo.getPassword(),"新密码不能为空");
        //去除密码首尾空格
        Long userId = Long.valueOf(bo.getUserId());
        String password = bo.getPassword().trim();
        LambdaQueryWrapper<TenantAdmUserPO> queryWrapper = Wrappers.lambdaQuery(TenantAdmUserPO.class);
        queryWrapper.eq(TenantAdmUserPO::getUserId, userId).eq(TenantAdmUserPO::getTenantCode, bo.getTenantCode());

        TenantAdmUserPO admUserPO = this.getOne(queryWrapper);
        Assert.notNull(admUserPO, "当前用户不存在");
        if (StringUtils.isNotBlank(bo.getOldPassword())) {
            Assert.isTrue(this.pwdMatch(bo.getOldPassword(), admUserPO.getPassword()), "原密码不正确");
        }
        admUserPO.setPassword(this.generatePwd(password));
        //去除用户token
        removeLoginSession(userId);
        return this.updateById(admUserPO);
    }

    @Override
    public SysBasicUserDTO login(TenantAdmUserPO po) {
        SysTenantInfoPO tenantInfo = tenantInfoManager.getTenantInfoFromCache(po.getTenantCode());

        Assert.notNull(tenantInfo, "租户不存在！");
        Assert.isTrue(Const.ONE.equals(tenantInfo.getStatus()), "租户已停用！");
        TenantAdmUserPO user = baseMapper.selectOne(
                Wrappers.<TenantAdmUserPO>lambdaQuery().eq(TenantAdmUserPO::getAccount, po.getAccount())
                        .eq(TenantAdmUserPO::getTenantCode, po.getTenantCode()));
        if (user != null) {
            Assert.isTrue(Objects.equals(user.getStatus(), Const.ONE), "账号已停用，请联系管理员");
            if (pwdMatch(po.getPassword(), user.getPassword())) {
                if (!StringUtils.equals(po.getAccount(), SUPER_ADMIN) && Objects
                        .equals(tenantInfo.getIsWeWork(), Const.ONE)) {
                }
                return directLogin(user.getAccount(),user.getTenantCode(),user.getUserId());
            }
        }
        return null;
    }

    @Override
    public void removeLoginSession(Long userId) {
        redisUtil.del(getSessionKey(userId));
    }

    private String generatePwd(String rawPassword) {
        BCryptPasswordEncoder passwordEncoder = passwordEncoder();
        return passwordEncoder.encode(rawPassword);
    }

    private static BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(BCryptPasswordEncoder.BCryptVersion.$2A, secureRandom);
    }

    private void setLoginSession(Long id, String token) {
        redisUtil.set(getSessionKey(id), token, sessionProperty.getAdm().getExpire());
    }

    /**
     * 不过期的
     * @param id
     * @param token
     */
    private void setLoginSessionWithOutExpire(Long id, String token) {
        redisUtil.set(getSessionKey(id), token);
    }

    @Override
    public void logoutJwtToken(String token) {
        token = formatToken(token);
        if (JwtUtil.isVerify(token)) {
            SysBasicUserDTO dto = parseJwtToken(token);
            removeLoginSession(dto.getUserId());
        }
    }

    @Override
    public String createJwtToken(SysBasicUserDTO dto) {
        //创建payload的私有声明（根据特定的业务需要添加，如果要拿这个做验证，一般是需要和jwt的接收方提前沟通好验证方式的）
        Map<String, Object> claims = new HashMap<String, Object>();
        claims.put("account", dto.getAccount());
        claims.put("id", dto.getUserId());
        claims.put("tenantCode", dto.getTenantCode());
        return JwtUtil.createJWT(claims, subject);
    }

    @Override
    public IPage<SysAdmUserWithProfileDTO> findUsers(SysUserSearchParamBO bo) {
        LambdaQueryWrapper<TenantAdmUserPO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotEmpty(bo.getUserName())) {
            List<TenantUserProfilePO> employees = tenantUserProfileManager.lambdaQuery()
                    .eq(TenantUserProfilePO::getTenantCode, bo.getTenantCode())
                    .like(TenantUserProfilePO::getName, bo.getUserName()).list();
            if (CollectionUtils.isEmpty(employees)) {
                return new Page<>(bo.getPageIndex(), bo.getPageSize());
            }
            List<Long> userIds = employees.stream()
                    .map(TenantUserProfilePO::getUserId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(userIds)) {
                lambdaQueryWrapper.in(TenantAdmUserPO::getUserId, userIds);
            }
        }
        if (StringUtils.isNotEmpty(bo.getAccount())) {
            lambdaQueryWrapper.like(TenantAdmUserPO::getAccount, bo.getAccount());
        }
        lambdaQueryWrapper.eq(TenantAdmUserPO::getTenantCode, bo.getTenantCode());
        if (Objects.nonNull(bo.getStatus())){
            lambdaQueryWrapper.eq(TenantAdmUserPO::getStatus,bo.getStatus());
        }else {
            lambdaQueryWrapper.ne(TenantAdmUserPO::getStatus, SysUserStatusEnum.QUIT.getCode());
        }
        lambdaQueryWrapper.eq(TenantAdmUserPO::getAccountType,bo.getAccountType());
        lambdaQueryWrapper.orderByAsc(TenantAdmUserPO::getId);
        IPage<TenantAdmUserPO> page = baseMapper.selectPage(convert(bo), lambdaQueryWrapper);
        Map<Long, TenantUserProfilePO> employeeMap = new HashMap<>();
        if (Objects.nonNull(page) && CollectionUtils.isNotEmpty(page.getRecords())) {
            List<TenantUserProfilePO> employees = tenantUserProfileManager.lambdaQuery()
                    .eq(TenantUserProfilePO::getTenantCode, bo.getTenantCode()).in(TenantUserProfilePO::getUserId,
                            page.getRecords().stream().map(TenantAdmUserPO::getUserId).collect(Collectors.toList()))
                    .list();
            employeeMap.putAll(employees.stream()
                    .collect(Collectors.toMap(TenantUserProfilePO::getUserId, Function.identity(), (v1, v2) -> v1)));
        }
        return page.convert(t -> convert(t, employeeMap.get(t.getUserId())));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(UpdatePwdParamBO bo) {
        String oldPwd = bo.getPassword().trim();
        String newPwd = bo.getNewPwd().trim();
        Long userId = operatorUtil.getOperator();
        LambdaUpdateWrapper<TenantAdmUserPO> useridWrapper = Wrappers.lambdaUpdate();
        useridWrapper.eq(TenantAdmUserPO::getUserId, userId);
        TenantAdmUserPO po = baseMapper.selectOne(useridWrapper);
        if (pwdMatch(oldPwd, po.getPassword())) {
            po.setPassword(generatePwd(newPwd));
            baseMapper.updateById(po);
            return true;
        } else {
            throw BusinessServiceException.getInstance("原密码错误");
        }
    }

    private SysAdmUserWithProfileDTO convert(TenantAdmUserPO t, TenantUserProfilePO s) {
        SysAdmUserWithProfileDTO dto = SysAdmUserWithProfileDTO.builder().isSuperAdm(t.getIsSuperAdm())
                .tenantCode(t.getTenantCode()).userId(t.getUserId()).account(t.getAccount()).status(t.getStatus())
                .createBy(t.getCreateBy()).modifyBy(t.getModifyBy()).build();
        if (Objects.nonNull(s)) {
            dto.setGender(s.getGender() + "");
            dto.setUserName(s.getName());
            dto.setMobile(s.getMobile());
        } else {
            dto.setUserName(dto.getAccount());
        }
        return dto;
    }

    private SysAdmUserDTO convertToSysAdminUser(TenantAdmUserPO t, TenantUserProfilePO s) {
        SysAdmUserDTO dto = SysAdmUserDTO.builder().isSuperAdm(t.getIsSuperAdm()).tenantCode(t.getTenantCode()).userId(t.getUserId())
                .account(t.getAccount()).status(t.getStatus()).createBy(t.getCreateBy()).modifyBy(t.getModifyBy()).build();
        return dto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserDetail(TenantAdmUserPO po,String userName) {
        Assert.notNull(po.getUserId());
        TenantUserProfilePO sysEmployeePO = tenantUserProfileManager.lambdaQuery()
                .eq(TenantUserProfilePO::getUserId, po.getUserId()).one();
        Assert.notNull(sysEmployeePO,"用户所属员工不存在，请联系管理员");
        //1 更新用户信息
        LambdaUpdateWrapper<TenantAdmUserPO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        if (Objects.nonNull(po.getIsSuperAdm())) {
            lambdaUpdateWrapper.set(TenantAdmUserPO::getIsSuperAdm, po.getIsSuperAdm());
        }
        if (StringUtils.isNotBlank(po.getAccount())) {
            TenantAdmUserPO existPO = baseMapper.selectOne(
                    Wrappers.lambdaQuery(TenantAdmUserPO.class).eq(TenantAdmUserPO::getAccount, po.getAccount())
                            .eq(TenantAdmUserPO::getTenantCode, po.getTenantCode()));
            if (Objects.nonNull(existPO) && !existPO.getUserId().equals(po.getUserId())) {
                throw BusinessServiceException.getInstance("登录账号已存在，请重新输入");
            } else {
                //修改用户名
                lambdaUpdateWrapper.set(TenantAdmUserPO::getAccount, po.getAccount().trim());
            }
        }
        if (Objects.nonNull(po.getStatus())) {
            lambdaUpdateWrapper.set(TenantAdmUserPO::getStatus, po.getStatus());
        }
        lambdaUpdateWrapper.eq(TenantAdmUserPO::getUserId,po.getUserId());
        int cnt = baseMapper.update(null, lambdaUpdateWrapper);

        //2 如果名称不为空则修改员工名称
        if (StringUtils.isNotBlank(userName)){
            tenantUserProfileManager.lambdaUpdate().eq(TenantUserProfilePO::getUserId,sysEmployeePO.getUserId()).
                    set(TenantUserProfilePO::getName,userName).update();
        }

        //3 更新用户详情基本信息缓存
        if (cnt > 0) {
            TenantAdmUserPO user = baseMapper.selectOne(Wrappers.<TenantAdmUserPO>lambdaQuery().eq(TenantAdmUserPO::getUserId, po.getUserId()));
            SysAdmUserDTO cache = userRoleMenuRedisCache.getUserDetail(po.getUserId());
            //仅当有缓存时做更新缓存操作
            if (cache != null) {
                userRoleMenuRedisCache.setUserDetailCache(convertToSysAdminUser(user, sysEmployeePO));
            }
        }

        //4 如果状态不是正常，去除用户token
        if (Objects.nonNull(po.getStatus()) && !Const.ONE.equals(po.getStatus())){
            //去除token
            removeLoginSession(sysEmployeePO.getUserId());
        }
    }

    @Transactional(rollbackFor = Throwable.class)
    @Override
    public boolean updateUserSelfDetail(UpdateUserSelfBO bo) {
        Assert.notNull(bo.getUserId(), "userId不能为空");
        Assert.notNull(bo.getTenantCode(), "租户编号不能为空");
        //查询用户
        TenantAdmUserPO user = baseMapper
                .selectOne(Wrappers.<TenantAdmUserPO>lambdaQuery().eq(TenantAdmUserPO::getUserId, bo.getUserId()));
        if (Objects.isNull(user)) {
            log.error("未在系统中找到要修改的用户 userId =" + bo.getUserId());
            throw BusinessServiceException.getInstance("未在系统中找到要修改的用户,请联系管理员");
        }

        if (StringUtils.isNotBlank(bo.getAccount())) {
            //判断用户名是否存在
            TenantAdmUserPO userPOTmp = baseMapper.selectOne(
                    Wrappers.<TenantAdmUserPO>lambdaQuery().eq(TenantAdmUserPO::getTenantCode, bo.getTenantCode())
                            .eq(TenantAdmUserPO::getAccount, bo.getAccount()));
            if (Objects.nonNull(userPOTmp) && !bo.getUserId().equals(userPOTmp.getUserId())) {
                throw BusinessServiceException.getInstance("登录账号已存在，请重新输入");
            }
            user.setAccount(bo.getAccount().trim());
            this.updateById(user);
        }

        //查询用户信息
        TenantUserProfilePO userProfilePO = tenantUserProfileManager.lambdaQuery()
                .eq(TenantUserProfilePO::getUserId, bo.getUserId()).one();
        if (Objects.nonNull(userProfilePO)) {
            tenantUserProfileManager.lambdaUpdate().eq(TenantUserProfilePO::getUserId, bo.getUserId())
                    .set(StringUtils.isNotBlank(bo.getUserName()), TenantUserProfilePO::getName, bo.getUserName())
                    .set(Objects.nonNull(bo.getGender()), TenantUserProfilePO::getGender, bo.getGender())
                    .set(StringUtils.isNotBlank(bo.getMobile()), TenantUserProfilePO::getMobile, bo.getMobile())
                    .set(StringUtils.isNotBlank(bo.getThumbAvatar()), TenantUserProfilePO::getThumbAvatar, bo.getThumbAvatar())
                    .set(TenantUserProfilePO::getModifyDt, new Date())
                    .set(TenantUserProfilePO::getModifyBy, bo.getModifyBy()).update();
        } else {
            TenantUserProfilePO insertProfilePO = new TenantUserProfilePO();
            insertProfilePO.setProfileId(hostTimeIdg.generateId().longValue());
            insertProfilePO.setUserId(bo.getUserId());
            insertProfilePO.setName(bo.getUserName());
            insertProfilePO.setMobile(bo.getMobile());
            insertProfilePO.setGender(bo.getGender());
            insertProfilePO.setTenantCode(bo.getTenantCode());
            insertProfilePO.setThumbAvatar(bo.getThumbAvatar());
            tenantUserProfileManager.save(insertProfilePO);
        }

        //删除用户详情缓存
        this.delUserDetailCache(bo.getUserId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addUser(AddUserBO bo,List<Long> roleIds) {
        TenantAdmUserPO user = new TenantAdmUserPO();
        user.setUserId(hostTimeIdg.generateId().longValue());
        user.setStatus(Const.ONE);
        user.setIsSuperAdm(Const.ZERO);
        user.setPassword(this.generatePwd(bo.getPassword()));
        user.setAccount(bo.getAccount());
        user.setTenantCode(bo.getTenantCode());
        this.save(user);

        //用户信息表
        TenantUserProfilePO insertUserProfilePO = new TenantUserProfilePO();
        insertUserProfilePO.setProfileId(hostTimeIdg.generateId().longValue());
        insertUserProfilePO.setUserId(user.getUserId());
        insertUserProfilePO.setName(Objects.nonNull(bo.getName())?bo.getName(): SymbolE.BLANK.getValue());
        insertUserProfilePO.setTenantCode(bo.getTenantCode());
        insertUserProfilePO.setGender(Const.ZERO);
        insertUserProfilePO.setIsDeleted(Const.ZERO);
        tenantUserProfileManager.save(insertUserProfilePO);

        //角色分配
        SysUserRolesParamBO sysUserRolesParamBO = new SysUserRolesParamBO();
        sysUserRolesParamBO.setUserId(user.getUserId());
        sysUserRolesParamBO.setRoleIds(roleIds);
        sysUserRolesParamBO.setTenantCode(bo.getTenantCode());
        sysUserRolesParamBO.setSystemCode(bo.getSystemCode());
        iSysUserRoleService.saveUserRoles(sysUserRolesParamBO);
        return user.getUserId();
    }

    @Override
    public String getRcToken(Long userId, String userName, String tenantCode) {
        String key = this.generateRcTokenKey(userId);
        String token = redisUtil.get(key);
        if (StringUtils.isNotBlank(token)) {
            return token;
        }
        String rcToken = this.generateRcToken(userId, tenantCode, userName);
        if (StringUtils.isBlank(rcToken)) {
            return null;
        }
        redisUtil.set(key, rcToken, RC_TOKEN_CACHE_EXPIRE_TIME);
        return rcToken;
    }
    private String generateRcToken(Long userId, String tenantCode, String userName) {
        RcTenantAuthTokenDTO rcTenantAuthTokenDTO = new RcTenantAuthTokenDTO();
        rcTenantAuthTokenDTO.setTokenCreateDt(new Date());
        rcTenantAuthTokenDTO.setUserId(userId);
        rcTenantAuthTokenDTO.setTenantCode(tenantCode);
        rcTenantAuthTokenDTO.setUserName(userName);

        try {
            return RsaUtil.encryptByPublicKey(RcConsts.RC_PUBLIC_KEY, JSONUtil.toJsonStr(rcTenantAuthTokenDTO));
        } catch (Exception e) {
            LOGGER.error("资源中心token生成异常！,rcTenantAuthTokenDTO:{},e:", JSONUtil.toJsonStr(rcTenantAuthTokenDTO), e);
            return null;
        }
    }


    private String generateRcTokenKey(Long userId) {
        return RESOURCE_CENTER_TOKEN_KEY_PREFIX + userId;
    }


    private boolean pwdMatch(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = passwordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
