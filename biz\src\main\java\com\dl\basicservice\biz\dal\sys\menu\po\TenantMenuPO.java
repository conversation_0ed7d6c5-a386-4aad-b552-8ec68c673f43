package com.dl.basicservice.biz.dal.sys.menu.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("sys_tenant_menu")
public class TenantMenuPO extends BasePO {
    @TableId("id")
    private Long id;

    /**
     * 菜单id
     */
    @TableField("menu_id")
    private Long menuId;


    @TableField("tenant_code")
    private String tenantCode;

    /**
     * 系统code，如dl-wealth-center
     */
    @TableField("system_code")
    private String systemCode;


}
