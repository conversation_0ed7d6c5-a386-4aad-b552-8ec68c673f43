package com.dl.basicservice.war;

import cn.easyes.starter.config.EasyEsConfigProperties;
import cn.easyes.starter.config.EsAutoConfiguration;
import com.dl.basicservice.biz.mq.DlChannels;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.stream.annotation.EnableBinding;

@MapperScan(value = "com.dl.basicservice.biz.dal")
@EnableDiscoveryClient
@SpringBootApplication(exclude = { EsAutoConfiguration.class }, scanBasePackages = {"com.dl.basicservice"})
@EnableConfigurationProperties({ EasyEsConfigProperties.class })
@EnableBinding({ DlChannels.class })
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
