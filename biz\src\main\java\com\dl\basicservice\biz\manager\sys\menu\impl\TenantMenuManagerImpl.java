package com.dl.basicservice.biz.manager.sys.menu.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.dal.sys.function.PrivilegeFunctionMapper;
import com.dl.basicservice.biz.dal.sys.function.po.PrivilegeFunctionPO;
import com.dl.basicservice.biz.dal.sys.function.po.TenantFunctionPO;
import com.dl.basicservice.biz.dal.sys.menu.PrivilegeMenuMapper;
import com.dl.basicservice.biz.dal.sys.menu.TenantMenuMapper;
import com.dl.basicservice.biz.dal.sys.menu.po.PrivilegeMenuPO;
import com.dl.basicservice.biz.dal.sys.menu.po.TenantMenuPO;
import com.dl.basicservice.biz.manager.sys.function.TenantFunctionManager;
import com.dl.basicservice.biz.manager.sys.menu.TenantMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.assist.MenuTreeHelp;
import com.dl.basicservice.biz.manager.sys.menu.bo.TenantMenuSaveParamBO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class TenantMenuManagerImpl extends ServiceImpl<TenantMenuMapper, TenantMenuPO> implements TenantMenuManager {
    @Autowired
    private PrivilegeFunctionMapper functionMapper;
    @Autowired
    private PrivilegeMenuMapper menuMapper;
    @Autowired
    private TenantFunctionManager tenantFunctionManager;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveTenantMenu(TenantMenuSaveParamBO bo) {
        MenuTreeHelp.validate(functionMapper, bo.getFunctionIds(), PrivilegeFunctionPO::getFunctionId, "functionId参数非法");
        MenuTreeHelp.validate(menuMapper, bo.getMenuIds(), PrivilegeMenuPO::getMenuId, "menuId参数非法");
        restTenantMenu(bo);
        restTenantFunction(bo);
        return true;
    }

    private void restTenantMenu(TenantMenuSaveParamBO bo) {
        String tenantCode = bo.getTenantCode();
        Long loginUserId = bo.getLoginUserId();
        List<Long> menuIds = bo.getMenuIds();
        String systemCode = bo.getSystemCode();
        //1、处理角色-菜单
        LambdaQueryWrapper<TenantMenuPO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(TenantMenuPO::getTenantCode, tenantCode);
        wrappers.eq(TenantMenuPO::getSystemCode, bo.getSystemCode());
        baseMapper.delete(wrappers);
        if (CollectionUtils.isNotEmpty(menuIds)) {
            List<TenantMenuPO> list = menuIds.stream().map(menuId -> {
                TenantMenuPO po = new TenantMenuPO();
                po.setCreateBy(loginUserId);
                po.setTenantCode(tenantCode);
                po.setMenuId(menuId);
                po.setSystemCode(systemCode);
                return po;
            }).collect(Collectors.toList());
            saveBatch(list);
        }
    }

    private void restTenantFunction(TenantMenuSaveParamBO bo) {
        String tenantCode = bo.getTenantCode();
        Long loginUserId = bo.getLoginUserId();
        List<Long> functionIds = bo.getFunctionIds();
        //1、处理角色-功能
        LambdaQueryWrapper<TenantFunctionPO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(TenantFunctionPO::getTenantCode, tenantCode);
        wrappers.eq(TenantFunctionPO::getSystemCode, bo.getSystemCode());
        tenantFunctionManager.remove(wrappers);
        if (CollectionUtils.isEmpty(functionIds)) {

        }

        List<TenantFunctionPO> list = functionIds.stream().map(f -> {
            TenantFunctionPO po = new TenantFunctionPO();
            po.setCreateBy(loginUserId);
            po.setTenantCode(tenantCode);
            po.setSystemCode(bo.getSystemCode());
            po.setFunctionId(f);
            return po;
        }).collect(Collectors.toList());

        tenantFunctionManager.saveBatch(list);
    }
}
