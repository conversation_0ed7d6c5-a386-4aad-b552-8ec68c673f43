package com.dl.basicservice.biz.manager.sys.tenant.enums;

/**
 * 租户初始化数据状态枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-30 19:08
 */
public enum TenantInitDataStatusEnum {
    //状态，0-未处理，1-处理成功，2-处理失败
    UN(0, "未处理"),
    SUCCESS(1, "处理成功"),
    FAIL(2, "处理失败");

    private Integer status;

    private String desc;

    TenantInitDataStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
