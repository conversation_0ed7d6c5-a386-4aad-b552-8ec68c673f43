package com.dl.basicservice.biz.mq;

import org.springframework.cloud.stream.annotation.Input;
import org.springframework.cloud.stream.annotation.Output;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.SubscribableChannel;

/**
 * @describe: DlChannels
 * @author: zhousx
 * @date: 2022/5/11 19:58
 */
public interface DlChannels {

    /*****************消费者***************/


    /*****************生产者***************/

    @Output("tenantstatuschange")
    MessageChannel tenantstatuschange();

    /**
     * 初始化租户部门
     * @return
     */
    @Output("tenantdepartmentinit")
    MessageChannel tenantdepartmentinit();

    /**
     * 更新租户code
     * @return
     */
    @Output("wechatupdatetenantcode")
    MessageChannel wechatupdatetenantcode();

    /**
     * 更新租户域名
     * @return
     */
    @Output("wechatupdatetenantdomain")
    MessageChannel wechatupdatetenantdomain();

}
