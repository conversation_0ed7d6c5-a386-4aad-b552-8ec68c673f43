package com.dl.basicservice.web.controller.internal.adm.sys.menu;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.manager.sys.function.dto.FunctionDTO;
import com.dl.basicservice.biz.manager.sys.menu.PrivilegeMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.TenantMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.TenantRoleMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleIdsMenuParamBO;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleMenuParamBO;
import com.dl.basicservice.biz.manager.sys.menu.bo.TenantMenuSaveParamBO;
import com.dl.basicservice.biz.manager.sys.menu.dto.MenuDTO;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.biz.manager.sys.role.enums.RoleTypeEnum;
import com.dl.basicservice.web.controller.internal.adm.base.InternalAbstractController;
import com.dl.basicservice.web.controller.internal.adm.sys.menu.request.InternalSysMenuParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.menu.request.InternalTenantMenuSaveParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.menu.resp.InternalFunctionDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.menu.resp.InternalMenuDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.menu.resp.InternalMenuObjectDTO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 14:09
 */
@RestController
@RequestMapping("/internal/menu")
@Api("菜单功能")
public class InternalMenuController extends InternalAbstractController {

    @Autowired
    private PrivilegeMenuManager privilegeMenuManager;
    @Autowired
    private TenantRoleMenuManager tenantRoleMenuManager;
    @Autowired
    private TenantMenuManager tenantMenuManager;
    @Autowired
    private TenantRoleManager tenantRoleManager;

    @PostMapping("/tenant/list")
    @ApiOperation("查询可分配给租户的系统菜单列表")
    public ResultModel<InternalMenuObjectDTO> tenantMenuList(@RequestBody @Validated InternalSysMenuParamDTO paramDTO) {
        Assert.isTrue(StringUtils.isNotBlank(paramDTO.getTenantCode()), "租户编码为空");
        //获取系统菜单列表
        List<InternalMenuDTO> all = this.getsyslist(paramDTO.getTenantCode(), getSystemCode());
        InternalMenuObjectDTO vo = new InternalMenuObjectDTO();
        vo.setAll(all);
        if (CollectionUtils.isNotEmpty(all)) {
            List<String> owners = new ArrayList<>();
            this.getOwners(owners, all);
            vo.setOwners(owners);
        }
        return ResultModel.success(vo);
    }

    @PostMapping("/tenant/save")
    @ApiOperation("保存指定租户的菜单和功能项")
    public ResultModel<Boolean> saveTenantMenu(@Validated @RequestBody InternalTenantMenuSaveParamDTO paramDTO) {
        Assert.isTrue(StringUtils.isNotBlank(paramDTO.getTenantCode()), "租户code参数为空");

        List<Long> menuIds = paramDTO.getMenuIds().stream().map(Long::parseLong).collect(Collectors.toList());
        List<Long> functionIds = paramDTO.getFunctionIds().stream().map(Long::parseLong).collect(Collectors.toList());

        // 给租户赋权
        TenantMenuSaveParamBO bo = new TenantMenuSaveParamBO();
        bo.setTenantCode(paramDTO.getTenantCode());
        bo.setMenuIds(menuIds);
        bo.setLoginUserId(paramDTO.getOperatorId());
        bo.setFunctionIds(functionIds);
        bo.setSystemCode(getSystemCode());
        tenantMenuManager.saveTenantMenu(bo);

        //获取租户下的角色列表
        LambdaQueryWrapper<TenantRolePO> tmquery = new LambdaQueryWrapper<>();
        tmquery.eq(StringUtils.isNotBlank(paramDTO.getTenantCode()), TenantRolePO::getTenantCode, paramDTO.getTenantCode());
        tmquery.eq(TenantRolePO::getSystemCode, getSystemCode());
        List<TenantRolePO> roles = tenantRoleManager.list(tmquery);
        if (CollectionUtils.isNotEmpty(roles)) {
            // 给超级管理员角色赋权
            Optional<TenantRolePO> opt = roles.stream()
                    .filter(f -> RoleTypeEnum.SUPER_ADMIN.getCode().equals(f.getRoleType())).findFirst();
            if (opt.isPresent()) {
                TenantRolePO po = opt.get();
                RoleMenuParamBO roleMenuParamBO = new RoleMenuParamBO();
                roleMenuParamBO.setRoleId(po.getRoleId());
                roleMenuParamBO.setMenuIds(menuIds);
                roleMenuParamBO.setLoginUserId(paramDTO.getOperatorId());
                roleMenuParamBO.setFunctionIds(functionIds);
                roleMenuParamBO.setSystemCode(getSystemCode());
                tenantRoleMenuManager.saveRoleMenu(roleMenuParamBO);
            }

            //非超级管理员角色过滤已去掉的权限
            List<Long> roleIds = roles.stream().filter(f -> !RoleTypeEnum.SUPER_ADMIN.getCode().equals(f.getRoleType()))
                    .map(r -> r.getRoleId()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(roleIds)) {
                RoleIdsMenuParamBO roleIdsMenuParamBO = new RoleIdsMenuParamBO();
                roleIdsMenuParamBO.setRoleIds(roleIds);
                roleIdsMenuParamBO.setMenuIds(menuIds);
                roleIdsMenuParamBO.setLoginUserId(paramDTO.getOperatorId());
                roleIdsMenuParamBO.setFunctionIds(functionIds);
                roleIdsMenuParamBO.setSystemCode(getSystemCode());
                tenantRoleMenuManager.delRoleMenu(roleIdsMenuParamBO);
            }
        }

        return ResultModel.success(Boolean.TRUE);
    }

    //获取有分配权限的菜单id列表
    private void getOwners(List<String> owners, List<InternalMenuDTO> list) {
        list.stream().filter(entity -> entity.getOwner() == 1).forEach(menu -> {
            owners.add(String.valueOf(menu.getMenuId()));
            List<InternalFunctionDTO> functions = menu.getFunctions();
            if (CollectionUtils.isNotEmpty(functions)) {
                functions.stream().filter(f -> f.getOwner() == 1).forEach(function -> {
                    //为了前端处理方便，跟菜单id区分，加F_字母前缀
                    owners.add("F_" + function.getFunctionId());
                });
            }
            this.getOwners(owners, menu.getChildren());
        });
    }

    /**
     * 获取租户系统菜单列表
     * @param tenantCode
     * @param systemCode
     * @return
     */
    private List<InternalMenuDTO> getsyslist(String tenantCode, String systemCode) {
        //获取系统菜单列表
        List<MenuDTO> list = privilegeMenuManager.listMenusAndFunctions(tenantCode, systemCode);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 组装成父子的树形结构
        return this.getChildrens(list);
    }

    private InternalMenuDTO buildInternalMenuDTO(MenuDTO menu) {
        InternalMenuDTO dto = this.cnvMenuDTO2InternalDTO(menu);
        dto.setChildren(getChildrens(menu.getChildren()));
        return dto;
    }

    /**
     * 递归构建所有菜单的子菜单
     * @param list
     * @return
     */
    private List<InternalMenuDTO> getChildrens(List<MenuDTO> list) {
        return list.stream().map(this::buildInternalMenuDTO).collect(Collectors.toList());
    }

    private InternalMenuDTO cnvMenuDTO2InternalDTO(MenuDTO m) {
        InternalMenuDTO vo = new InternalMenuDTO();
        vo.setDisable(m.getDisable());
        vo.setMenuId(String.valueOf(m.getMenuId()));
        vo.setIcon(m.getIcon());
        vo.setLevel(m.getLevel());
        vo.setName(m.getName());
        vo.setOwner(m.getOwner());
        vo.setSort(m.getSort());
        vo.setParentId(String.valueOf(m.getParentId()));
        vo.setUrl(m.getUrl());

        List<FunctionDTO> functions = m.getFunctions();
        if (CollectionUtils.isNotEmpty(functions)) {
            functions.forEach(f -> {
                InternalFunctionDTO internalFunctionDTO = new InternalFunctionDTO();
                internalFunctionDTO.setFunctionId(String.valueOf(f.getFunctionId()));
                internalFunctionDTO.setOwner(f.getOwner());
                internalFunctionDTO.setIcon(f.getIcon());
                internalFunctionDTO.setName(f.getName());
                internalFunctionDTO.setSort(f.getSort());
                vo.getFunctions().add(internalFunctionDTO);
            });
        }
        return vo;
    }

}
