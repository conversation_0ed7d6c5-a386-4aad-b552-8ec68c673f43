package com.dl.basicservice.biz.manager.storepolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class BaseTempCredentialDTO {
    @ApiModelProperty("存储方案：cos 、minio")
    String storage;

    @ApiModelProperty("资源路径前缀")
    String resourcePathPrefix;
}
