package com.dl.basicservice.web.util;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.manager.sys.role.TenantUserRoleManager;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysBasicUserDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Set;

@Component
public class AccountCompent {

    @Autowired
    private TenantUserRoleManager tenantUserRoleManager;

    @Autowired
    private SysAdmUserService sysAdmUserService;

    /**
     * @description 获取当前子账户Id
     * <AUTHOR>
     * @createTime 2020/12/28 15:39
     */
    public SysBasicUserDTO getUser() {
        return sysAdmUserService.parseJwtToken(getToken());
    }

    public Set<Long> getRoleIds(String systemCode) {
        return tenantUserRoleManager.findUserRoleByUserId(systemCode,getUser().getUserId());
    }

    private String getToken() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
            String token = request.getHeader(Const.TOKEN_HEADER_NAME);
            return token.substring(token.indexOf(" ") + 1);
        }
        return null;
    }

    public SysAdmUserDTO getCurrentDetail() {
        return sysAdmUserService.findUserDetailFromCache(getUser().getUserId());
    }

}