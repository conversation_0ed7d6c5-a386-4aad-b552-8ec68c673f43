package com.dl.basicservice.web.controller.internal.adm.sys.oplog.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 14:52
 */
@Data
@ApiModel("添加操作日志请求类")
public class OpLogAddReq {

    @NotBlank(message = "接入业务的编码不能为空")
    @ApiModelProperty(value = "接入业务的编码", required = true)
    private String bizCode;

    @NotBlank(message = "操作对象不能为空")
    @ApiModelProperty(value = "操作对象", required = true)
    private String opObject;

    @NotBlank(message = "操作对象主键不能为空")
    @ApiModelProperty(value = "操作对象主键", required = true)
    private String opKey;

    @NotBlank(message = "操作类型不能为空")
    @ApiModelProperty(value = "操作类型 自定义（add、update、delete）", required = true)
    private String opType;

    @NotBlank(message = "租户编码不能为空")
    @ApiModelProperty(value = "租户编码", required = true)
    private String tenantCode;

    @ApiModelProperty("操作前数据 格式自定义")
    private String opBefore;

    @ApiModelProperty("操作后数据 格式自定义")
    private String opAfter;

    @ApiModelProperty("操作说明")
    private String remark;

    @NotBlank(message = "操作人不能为空")
    @ApiModelProperty(value = "操作人", required = true)
    private String opUserId;

    @NotBlank(message = "操作人姓名不能为空")
    @ApiModelProperty(value = "操作人姓名", required = true)
    private String opUserName;

    @NotNull(message = "操作时间不能为空")
    @ApiModelProperty(value = "操作时间", required = true)
    private Date opDt;

    @ApiModelProperty("扩展信息")
    private String extData;

}
