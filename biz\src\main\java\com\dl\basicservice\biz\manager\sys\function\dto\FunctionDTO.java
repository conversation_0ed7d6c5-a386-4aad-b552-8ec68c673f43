package com.dl.basicservice.biz.manager.sys.function.dto;

import com.dl.basicservice.biz.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 功能权限
 */
@Data
@ApiModel("功能权限")
public class FunctionDTO extends BaseDTO {
    @ApiModelProperty("功能id")
    private Long functionId;

    @ApiModelProperty("功能名称")
    private String name;

    @ApiModelProperty("编码")
    private String functionCode;

    @ApiModelProperty("功能图标")
    private String icon;

    @ApiModelProperty("显示顺序")
    private Integer sort;

    @ApiModelProperty("是否已分配权限，0否1是")
    private Integer owner = 0;

}
