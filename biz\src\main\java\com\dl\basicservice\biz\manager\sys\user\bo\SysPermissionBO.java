package com.dl.basicservice.biz.manager.sys.user.bo;

import com.dl.basicservice.biz.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 功能
 */
@Data
@ApiModel("权限代码")
@NoArgsConstructor
@AllArgsConstructor
public class SysPermissionBO extends BaseDTO {

    /**
     * 权限编码
     */
    @ApiModelProperty("权限代码list")
    @Size(min = 1, max = 200)
    private List<String> functionCodes;

}
