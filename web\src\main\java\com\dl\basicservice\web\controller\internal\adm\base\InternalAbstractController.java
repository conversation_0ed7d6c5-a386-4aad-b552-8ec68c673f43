package com.dl.basicservice.web.controller.internal.adm.base;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.common.model.ResultPageModel;
import com.dl.framework.core.controller.BaseController;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.function.Function;

import static com.dl.basicservice.biz.common.constant.Const.DEFAULT_SYSTEM_CODE;
import static com.dl.basicservice.biz.common.constant.Const.SYSTEM_CODE;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-17 15:30
 */
public class InternalAbstractController extends BaseController {

    @Resource
    private HttpServletRequest request;

    public String getSystemCode() {
        String systemCode = request.getHeader(SYSTEM_CODE);
        if (StringUtils.isBlank(systemCode)) {
            systemCode = DEFAULT_SYSTEM_CODE;
        }
        return systemCode;
    }

    protected <T> ResultPageModel<T> pageQueryModel(IPage<T> page) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(page.getRecords());
        return model;
    }

    protected <T, R> ResultPageModel<R> pageQueryModel(IPage<T> page, Function<T, R> function) {
        ResultPageModel<R> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(page.convert(t -> function.apply(t)).getRecords());
        return model;
    }

    protected <T> ResultPageModel<T> pageQueryModel(ResponsePageQueryDO page, Collection<T> records) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getPageIndex());
        model.setPageSize(page.getPageSize());
        model.setTotalPage(page.getTotalPage());
        model.setTotal(page.getTotal());
        model.setDataResult(records);
        return model;
    }

    protected <T> ResultPageModel<T> pageQueryModel(IPage page, Collection<T> records) {
        ResultPageModel<T> model = new ResultPageModel<>();
        model.setPageIndex(page.getCurrent());
        model.setPageSize(page.getSize());
        model.setTotalPage(page.getPages());
        model.setTotal(page.getTotal());
        model.setDataResult(records);
        return model;
    }

}
