package com.dl.basicservice.web.controller.adm.sys.role.convert;

import com.dl.basicservice.biz.manager.sys.role.dto.SysRoleDTO;
import com.dl.basicservice.web.controller.adm.sys.role.vo.RoleVO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-03 14:01
 */
public class RoleConvert {

    public static RoleVO cnvRoleDTO2VO(SysRoleDTO dto) {
        RoleVO roleVO = new RoleVO();
        roleVO.setTenantCode(dto.getTenantCode());
        roleVO.setRoleId(String.valueOf(dto.getRoleId()));
        roleVO.setRoleName(dto.getRoleName());
        roleVO.setRoleType(dto.getRoleType());
        roleVO.setCreateBy(dto.getCreateBy());
        roleVO.setCreateDt(dto.getCreateDt());
        roleVO.setModifyBy(dto.getModifyBy());
        roleVO.setModifyDt(dto.getModifyDt());
        return roleVO;
    }

}
