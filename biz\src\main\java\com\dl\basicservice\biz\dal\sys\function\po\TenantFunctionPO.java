package com.dl.basicservice.biz.dal.sys.function.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.Data;

/**
 * 功能，对应业务功能，包含单个或多个requestmapping
 */
@Data
@TableName("sys_tenant_function")
public class TenantFunctionPO extends BasePO {
    @TableId("id")
    public Long id;

    /**
     * 权限ID
     */
    @TableField("function_id")
    private Long functionId;

    @TableField("tenant_code")
    private String tenantCode;

    /**
     * 系统code，如dl-wealth-center
     */
    @TableField("system_code")
    private String systemCode;

}
