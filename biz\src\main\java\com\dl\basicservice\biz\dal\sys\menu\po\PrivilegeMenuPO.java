package com.dl.basicservice.biz.dal.sys.menu.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("sys_privilege_menu")
public class PrivilegeMenuPO extends BasePO {
    @TableId("id")
    private Long id;

    /**
     * 菜单id
     */
    @TableField("menu_id")
    private Long menuId;

    /**
     * 上级菜单,0表示一级菜单
     */
    @TableField("parent_id")
    private Long parentId;
    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 菜单地址(静态url)
     */
    @TableField("url")
    private String url;

    /**
     * 菜单级别 1：一级 2：二级 3：三级
     */
    @TableField("menu_level")
    private Integer menuLevel;

    /**
     * 显示顺序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 是否禁用【0-开启1-禁用】
     */
    @TableField("disable")
    private Integer disable;

    /**
     * 系统code，如dl-wealth-center
     */
    @TableField("system_code")
    private String systemCode;

    /**
     * 菜单场景：0-通用，1-企微，2-小程序
     */
    @TableField("scene_type")
    private Integer sceneType;

}
