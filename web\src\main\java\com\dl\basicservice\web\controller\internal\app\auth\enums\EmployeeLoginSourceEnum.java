package com.dl.basicservice.web.controller.internal.app.auth.enums;

/**
 * 员工登录来源枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-04-03 11:44
 */
public enum EmployeeLoginSourceEnum {

    QW_APP("qw_app", "企业微信手机端"),
    QW_PC("qw_pc", "企业微信pc端"),
    WX_MINIGRAM("wx_miniapps", "微信小程序"),
    WX_WOA_H5("wx_woa_h5","微信公众号H5");

    private String code;

    private String desc;

    EmployeeLoginSourceEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
