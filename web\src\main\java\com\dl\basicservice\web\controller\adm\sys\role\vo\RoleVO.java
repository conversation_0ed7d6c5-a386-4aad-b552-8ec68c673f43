package com.dl.basicservice.web.controller.adm.sys.role.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel("租户角色信息")
public class RoleVO {
    @ApiModelProperty("角色id")
    private String roleId;
    @ApiModelProperty("角色名称")
    private String roleName;

    /**
     * @see com.dl.basicservice.biz.manager.sys.role.enums.RoleTypeEnum
     */
    @ApiModelProperty("角色类型")
    private String roleType;

    @ApiModelProperty("租户id")
    private String tenantCode;

    @ApiModelProperty("创建时间")
    private Long createBy;

    @ApiModelProperty("修改时间")
    private Long modifyBy;

    @ApiModelProperty("创建人")
    private Date createDt;

    @ApiModelProperty("修改人")
    private Date modifyDt;

}
