package com.dl.basicservice.biz.common.tencentcloud.cos;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

/**
 * 获取资源路径BO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("cos临时凭证请求")
public class CosGetTempCredentialBO {

    @ApiModelProperty("模块")
    @Size(min = 2, max = 20)
    private String module;

    @ApiModelProperty("目标")
    @Size(min = 0, max = 50)
    private String target;

    @ApiModelProperty("是否上传：0-否 1-是")
    @Size(min = 1, max = 1)
    private String isUpload;

}
