package com.dl.basicservice.biz.manager.sys.tenant.enums;

/**
 * 租户初始化数据类型枚举
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-11-29 14:02
 */
public enum TenantInitDataTypeEnum {

    //初始化数据类型，1-员工信息，2-客户标签，3-客户信息，4-客户群信息，5-基础素材信息初始化，6-话术信息初始化，7-SOP信息初始化,8-服务包信息初始化
    EMPLOYEE_INFO(1, "员工信息初始化", 1, 1),
    CONTACT_TAG(2, "客户标签初始化", 2, 1),
    CONTACT_INFO(3, "客户信息初始化", 3, 1),
    ROOM_INFO(4, "客户群信息初始化", 4, 1),
    MATERIAL(5, "基础素材信息初始化", 5, 0),
    SCRIPT(6, "话术信息初始化", 6, 0),
    SOP(7, "SOP信息初始化", 7, 0),
    PACK(8, "服务包信息初始化", 8, 0);

    private Integer type;

    private String name;

    private Integer sort;

    /**
     * 是否需要企微入驻
     */
    private Integer needWeWork;

    TenantInitDataTypeEnum(Integer type, String name, Integer sort, Integer needWeWork) {
        this.type = type;
        this.name = name;
        this.sort = sort;
        this.needWeWork = needWeWork;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public Integer getSort() {
        return sort;
    }

    public Integer getNeedWeWork() {
        return needWeWork;
    }

}
