package com.dl.basicservice.biz.dal.sys.user.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 管理端用户
 */
@Data
@TableName("sys_tenant_admuser")
@AllArgsConstructor
@NoArgsConstructor
public class TenantAdmUserPO extends BasePO {
    @TableId("id")
    public Long id;

    /**
     * userid
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 登录账号
     */
    @TableField("account")
    private String account;

    /**
     * 密码
     */
    @TableField("password")
    private String password;

    /**
     * 账号状态
     * 0-启用
     * 1-锁定
     * 2-禁用
     * <p>
     * ADM_USER_STATUS
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否超管
     * 1-是
     * 0-否
     */
    @TableField("is_super_admin")
    private Integer isSuperAdm;

    @TableField("tenant_code")
    private String tenantCode;

    /**
     * 0 B端用户 1 小程序端理财师 2. 小程序端意向理财师
     */
    @TableField("account_type")
    private Integer accountType;
}
