package com.dl.basicservice.biz.manager.sys.tenant.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-03-28 14:58
 */
public enum TenantAuthModeEnum {

    //1-微信全托管模式，2-微信第三方平台
    WX_FULL_TRUSTEESHIP(1, "微信全托管模式"),
    WX_THRID_ACCOUNT(2, "微信第三方平台");

    private Integer code;

    private String desc;

    TenantAuthModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
