package com.dl.basicservice.biz.manager.mino.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@ConfigurationProperties(prefix = "dl.minio")
@Configuration
@Data
public class MinioProperties {

    /**
     * minio服务器ip
     */
    private String minioHost;

    /**
     * minio服务器port
     */
    private Integer minioPort;

    /**
     * minio服务器客户端用户名
     */
    private String mcUserName;

    /**
     * minio服务器客户端密码
     */
    private String mcPwd;

    /**
     * minio可用存储桶
     */
    private String bucketId;

    /**
     * 是否使用https协议,默认 false
     */
    private boolean tslSecure;

    /**
     * 指定租户开启 minio
     */
    private String assignTenantCode;

}
