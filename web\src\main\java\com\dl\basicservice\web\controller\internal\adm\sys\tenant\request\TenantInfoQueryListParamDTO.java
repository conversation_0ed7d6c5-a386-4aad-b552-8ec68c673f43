package com.dl.basicservice.web.controller.internal.adm.sys.tenant.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-09-22 13:52
 */
@Data
public class TenantInfoQueryListParamDTO implements Serializable {

    @NotEmpty(message = "租户编码列表不能为空")
    @ApiModelProperty(value = "租户编码列表", required = true)
    private List<String> tenantCodeList;
}
