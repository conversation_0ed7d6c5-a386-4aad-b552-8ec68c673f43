package com.dl.basicservice.web.controller.internal.adm.sys.admuser.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 14:17
 */
@Data
public class AdmUserBatchAddParamDTO implements Serializable {

    @NotBlank(message = "租户编号不能为空")
    private String tenantCode;

    @NotEmpty(message = "参数列表不能为空")
    private List<AdmUserAddParamDTO> params;
}
