package com.dl.basicservice.biz.config;

import com.dl.basicservice.biz.common.enums.SymbolE;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class TenantDomainConfig {
    /**
     * 定力二级根域名后缀
     */
    @Value("${wealth.workbench.domainSuffix}")
    private String dlDomainSuffix;

    @Value("${wealth.workbench.domain}")
    private String dlDomain;

    @Value("#{${wealth.magic.tenantspecial.loginurl}}")
    private Map<String, String> tenantSpecialMagicLoginURLMap;

    public String getTenantThirdLevelDomain(String tenantCode) {
        return "https://" + tenantCode.toLowerCase() + "." + dlDomainSuffix;
    }

    public String getDlDomain() {
        return dlDomain;
    }

    public String getTenantMagicLoginUrl(String tenantCode) {
        if(MapUtils.isEmpty(tenantSpecialMagicLoginURLMap)){
            return SymbolE.BLANK.getValue();
        }

        return tenantSpecialMagicLoginURLMap.get(tenantCode);
    }

}
