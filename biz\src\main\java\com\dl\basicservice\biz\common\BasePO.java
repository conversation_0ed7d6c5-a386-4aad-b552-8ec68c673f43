package com.dl.basicservice.biz.common;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public abstract class BasePO implements Serializable {

    @TableField(value = "create_dt", fill = FieldFill.INSERT)
    public Date createDt;

    @TableField(value="create_by", fill = FieldFill.INSERT)
    public Long createBy;

    @TableField(value = "modify_dt", fill = FieldFill.INSERT_UPDATE)
    public Date modifyDt;

    @TableField(value="modify_by",fill = FieldFill.INSERT_UPDATE)
    public Long modifyBy;

}
