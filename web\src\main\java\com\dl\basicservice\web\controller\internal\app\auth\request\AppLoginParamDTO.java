package com.dl.basicservice.web.controller.internal.app.auth.request;

import lombok.Data;

import java.io.Serializable;

/**
 * A端登录参数
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-09 16:21
 */
@Data
public class AppLoginParamDTO implements Serializable {
    private static final long serialVersionUID = -6640913675965622009L;

    private  String userId;

    private String wxUserId;

    private String tenantCode;

    private String name;

    /**
     * @see com.dl.basicservice.web.controller.internal.app.auth.enums.EmployeeLoginSourceEnum
     */
    private String source;
}
