package com.dl.basicservice.biz.manager.sys.userbind.utils;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.dl.framework.common.utils.JsonUtils;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESedeKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class CommonSecretUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(CommonSecretUtil.class);

    /**
     * 对签名进行解密
     *
     * @param strSign
     * @return
     */
    public static Map<String, Object> unSecurity(String strSign, String secretKey) {
        if (StringUtils.isNotBlank(strSign) && StringUtils.isNotBlank(secretKey)) {
            String baseString = decrypt(strSign, secretKey);
            return getParams(baseString);
        }
        return new HashMap<>();
    }

    public static Map<String, Object> getParams(String baseString) {
        Map<String, Object> map = jsonParser(baseString);
        if (map.isEmpty()) {
            map = urlParser(baseString);
        }
        return map;
    }

    private static Map<String, Object> jsonParser(String json) {
        Map<String, Object> tempMap = new HashMap<>();
        if (StringUtils.isBlank(json)) {
            return tempMap;
        }
        try {
            Map<String, Object> m = JsonUtils.getMap4Json(json);
            if (!m.isEmpty()) {
                for (Map.Entry<String, Object> entry : m.entrySet()) {
                    tempMap.put(entry.getKey(), entry.getValue().toString());
                }
            }
        } catch (Exception e) {
            LOGGER.warn("jsonParser error:{}", json, e);
            return tempMap;
        }

        return tempMap;
    }

    private static Map<String, Object> urlParser(String baseString) {
        Map<String, Object> map = new HashMap<>();
        if (StringUtils.isBlank(baseString)) {
            return map;
        }
        try {
            for (String param : baseString.split("&")) {
                String[] keyValue = param.split("=");
                if (keyValue.length == 1) {
                    map.put(keyValue[0], "");
                } else {
                    map.put(keyValue[0], keyValue[1]);
                }
            }
        } catch (Exception e) {
            LOGGER.warn("urlParser error:{}", baseString, e);
            return map;
        }
        return map;

    }

    /**
     * DES 解密
     *
     * @param encryptData 加密串
     * @param secret      加密/解密 secret
     * @return
     */
    private static String decrypt(String encryptData, String secret) {
        try {
            // --通过base64,将字符串转成byte数组
            byte[] bytesrc = Base64.decodeBase64(encryptData.getBytes());
            // --解密的key
            DESedeKeySpec dks = new DESedeKeySpec(secret.getBytes(StandardCharsets.UTF_8));
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");
            SecretKey securekey = keyFactory.generateSecret(dks);

            // --Chipher对象解密
            Cipher cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, securekey);
            byte[] retByte = cipher.doFinal(bytesrc);

            return new String(retByte);
        } catch (Exception e) {
            LOGGER.warn("解密异常:", e);
            return "";
        }
    }

    /**
     * DES 加密
     *
     * @param originData 原始数据,未加密
     * @param secret     加密秘钥
     * @return
     */
    public static String encrypt(String originData, String secret) {
        try {
            DESedeKeySpec dks = new DESedeKeySpec(secret.getBytes(StandardCharsets.UTF_8));
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DESede");
            SecretKey securekey = keyFactory.generateSecret(dks);

            Cipher cipher = Cipher.getInstance("DESede/ECB/PKCS5Padding");
            cipher.init(Cipher.ENCRYPT_MODE, securekey);
            byte[] b = cipher.doFinal(originData.getBytes());
            return new String(Base64.encodeBase64(b), StandardCharsets.UTF_8).replaceAll("\r", "").replaceAll("\n", "");

        } catch (Exception e) {
            LOGGER.warn("加密异常:", e);
            return "";
        }

    }

    private static void encodeTest() throws UnsupportedEncodingException {
        Map<String, String> map = new HashMap<>();
        map.put("ext_user_id", "e4dfb860241f45f1aa431f1ee2b79697");
        map.put("mobile", "18211098352");
        map.put("target", "%2Fprom%2F31%2Fdrug%2Fremind");
        map.put("idcard", "220502199107274557");
        map.put("name", "陈云");
        map.put("open_id", "ot6281UzbTu4LFjWydTOk3ynGtG0");
        String ori = JSONObject.toJSONString(map);
        System.out.println(ori);
        String key = "54321^8765swerxyuiop883654789025";
        System.out.println(key);
        String result = encrypt(ori, key);
        String encodeData = URLEncoder.encode(result, "UTF-8");
        System.out.println(encodeData);
        System.out.println("http://lsld.o.guahao-test.com/partners/entrance?token=" + encodeData);
    }

    private static void decodeTest() throws UnsupportedEncodingException {
        String token = "gZy7IedZBANDNHmA1lmBLB3FKHGM4Kt8HnwijjFzQxEwhIh93G3wgVqDONNdTkyw77cYBilbX4eFIVSs8P%2FGTQmC%2FWub9S9lAzk5hf0kkHEudlybtu1P8tHhlN9ZiQc6QO7GBKokXzN26Z6Bddaw8bcxTpSZSyznnvC2sM%2Br9xMQHa5monBfFJYQmAh%2FEoCS3cwvePm0GHFBUUYayCZiE6pW6rAx9%2Br3qYrVEQUXBPa9So1AcJGa%2Fgw7QQBJS1%2BG";
        String decryptData = decrypt(URLDecoder.decode(token, "UTF-8"), "aEE/9E3NIqzknRH2pZnJJnUl4hiwqrEI");
        System.out.println(decryptData);
    }

    private static void test1() {
        Map<String, String> map = new HashMap<>();
        map.put("ext_user_id", "test20231106-2");
        map.put("name", "王二");
        map.put("timestamp", String.valueOf(System.currentTimeMillis()));
        try {
            map.put("target", URLEncoder.encode("https://magictest.dinglitec.com/adm/template/mytemplate", "UTF-8"));

            String data = JsonUtils.toJSON(map);
            String key = "CSXCDZH"
                    + "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCbJ759JkFrWSOWenRA0Wfn4jaUMjSX7EtkuY/9IfASnhkRtdxsK0O+8S2lDbEW3SiQmiiumt47GBbKdU+fmn9mo4DAp7/KzFPjoE+OuDaxr8SatFEdfPxkLUNIt7Uj4XgaYwzZXvkZhDaJr0OuLfa2XBtnpYFuDkZb/AqgnPeQsBK3FJKyKkmtb3M7Ljj8F9hdMckG2GRxoyBrl96IKCEdOa1qpg58zZI2FbZI5CEdpr8oZ1wQv6z39YS4022u3THy1c49gAtEld1cSubA/AusgMequ0oaTjHRbZ+CehHiprbhPWn7Pt8IJGj/0a9hqnbFhVUIWI44xcZXB4VGV2ZzAgMBAAECggEAUJofDWjYR2oxYLUQu7ONpRsRe39xbxMkP5lewgPlceaL43V0owT+1qz4J2yreNM/hY9wXvS5Sj3DaT30NVfPo4SBGJSMwE/RrMjVS7FLSJelLTFLimQpwej5sUcuZQt2l06pmCsMgUL8Ch7wrAXYyveijP+f84qa5qJS6wlgWtstIY7n4uzCL5OzRSKoW0HC54qrUFI/L0JYfsgI6MzJxN72CThvKpgqItyBanAz4/H1csbegVKX0kLC2w+R2rpiXevL8JId+yZ54HKiUmIFEtPDP+8fpOSPbT7FAD/kyxcdGj/3zbFY60e/s/1zH8K15KsG9Uh3wW3e+HzaYLS8IQKBgQDe0CovzXV7KSDGkAa4Qe1t6jeSA80Lq//RRF8jKXCuJT3SnangqqWJBvZZ3/NxcUEvAN0c/YowTCeMA7hES1D7WkTi2TyL+fIdNlUAFltKsz+PqI37Zrod5ZrHCq3y5Mew4Y6OPW1X76qJ33Y9D+P0kNMzoUTYqt21b9NTv0fEawKBgQCyQ8r9eaR7SpcrKnxS3El/x2gYOH6PbPHL/oIqyKZgSyBFZF+3PBuEs9E0NHlLFHbRGaCGFdUYx3S5jBPzjIK+7rhlhDCeqtZcxF89YsGpfgIWMHKT7GhEsUMmrOgR2w7MmgepAROxQj94Q+Jsbid+jmDdiBsV1NiHPPwUEg2oGQKBgQChrYVfKIQ9+UsKKkpl+5jQsCrgrhdkh8taS3WJazGTe/yPTs6M8uapNr8d2i6pO5gkBklsFuHmR+xOYgicrdY2fXsM7LtNivHPlrQ5Gv/lhYnysUlNim408X3NPoeYf8ATLqiluBcWvxcNcnQ2vMgZl9lZVoVBf0LfvCQpWdw2vwKBgEE3XAPvhKU2XKeGG4WU4a7FnOd/g42lJbCjo6tTTMrdsSix1/KJIughgN/Acr9s9Sr6XSewxQ0TqzhWbtYjCZIgc4VwHvltNo8pqE4k2wTO/KRxhPlo+5xl3VNA3oXpxjhEAZlqs3Gd8upkq2lPw1Mhc36YVJBgFfcj8HTHRgfBAoGAGkWuthKaxojsmzzah/MNo8IvrzLP9c0SLzLds8773TioOtG13OVL/HekNWvyUQxbX/txEOtqQgjJdui0PJs5gFFvlfU8EDuiLofxLNx2nCnUiTVvQ8P+lvZ0yAbNIYKnyGU50leMJ6/zTQ0YxzxGaQfQFtqDp31KCw/SkZJcgTs=";
            key = key.substring(0, 24);
            System.out.println("key:" + key);
            String encryptResult = encrypt(data, key);
            System.out.println("encryptResult:" + encryptResult);

            //System.out.println();

            String urlEncodeStr = URLEncoder.encode(encryptResult, "UTF-8");
            System.out.println("encryptResult2:" + urlEncodeStr);

            String urlDecodeStr = URLDecoder.decode(urlEncodeStr, "UTF-8");
            System.out.println("decodeStr:" + urlDecodeStr);

            Map<String, Object> resultMap = CommonSecretUtil.unSecurity(urlDecodeStr, key);
            System.out.println("resultMap:" + JSONUtil.toJsonStr(resultMap));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) throws Exception {
        /*String encryptStr = "d3ql8fUXJhqY9b%2F0tK2RTO97X43ZNmV1V1daX0WjI4NG%2BtF1KrZJ1iXQEYmTgm3QmpzGxEMTeDL1hYkSPH%2F1ry8A6nrM0vLrDE00Yf6GhPI%2FIqN2CM5cvFpMxVUHUIWQUmCAILDohO8%3D";
        //        //将base64字符串进行DES加密
        String key = "7da6e15e1e674bf987ea6983de5e289f";
        //        //将DES加密后的字符串URLEncode
        String decodeStr = URLDecoder.decode(encryptStr, "UTF-8");

        Map<String, Object> stringStringMap = unSecurity(decodeStr, key);

        System.out.println(stringStringMap);*/
        test1();
    }

}
