package com.dl.basicservice.web.controller.internal.adm.sys.tenant.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-05-27 13:55
 */
@Data
public class UpdateTenantTrialParamDTO {

    @NotBlank(message = "租户编码不能为空")
    private String tenantCode;

    /**
     * 是否试用，0-否，1-是
     */
    @NotNull(message = "是否试用不能为空")
    private Integer trialStatus;

    @ApiModelProperty("操作人id")
    @NotNull(message = "操作人id不能为空")
    private Long operatorId;

}
