package com.dl.basicservice.biz.common.mybatis;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.dl.basicservice.biz.common.util.OperatorUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.function.Supplier;

/**
 * 自定义的创建时间、人员和更新时间、人员的mybatisplus赋值过滤器
 * 强制赋值
 */
@Slf4j
@Component
public class CUMetaObjectHandler implements MetaObjectHandler {

    @Autowired
    private OperatorUtil operatorUtil;

    @Override
    public void insertFill(MetaObject metaObject) {
        Long operId=operatorUtil.getOperator();
        Date now=new Date();
        fillValue(metaObject, "createBy", () ->operId);
        fillValue(metaObject, "createDt", () -> now);
        fillValue(metaObject, "modifyDt", () -> now);
        fillValue(metaObject, "modifyBy", () -> operId);
    }

    @Override
    public void updateFill(MetaObject metaObject) {
        Long operaId=operatorUtil.getOperator();
        Date now=new Date();
        fillValue(metaObject, "modifyDt", () -> now,true);
        fillValue(metaObject, "modifyBy", () -> operaId,true);
    }

    private void fillValue(MetaObject metaObject, String fieldName, Supplier<Object> valueSupplier) {
        fillValue(metaObject, fieldName, valueSupplier,false);
    }
    private void fillValue(MetaObject metaObject, String fieldName, Supplier<Object> valueSupplier,boolean override) {
        if (!metaObject.hasGetter(fieldName)) {
            return;
        }
        Object sidObj = metaObject.getValue(fieldName);
        if ((override||sidObj == null) && metaObject.hasSetter(fieldName) && valueSupplier != null) {
            setFieldValByName(fieldName, valueSupplier.get(), metaObject);
        }
    }

}
