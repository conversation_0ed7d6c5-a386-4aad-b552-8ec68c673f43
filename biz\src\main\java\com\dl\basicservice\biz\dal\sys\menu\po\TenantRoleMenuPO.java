package com.dl.basicservice.biz.dal.sys.menu.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.Data;

/**
 * <p>
 * 角色-菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Data
@TableName("sys_tenant_role_menu")
public class TenantRoleMenuPO extends BasePO {

    @TableId
    private Long id;

    @TableField("role_id")
    private Long roleId;

    @TableField("menu_id")
    private Long menuId;

    /**
     * 系统code，如dl-wealth-center
     */
    @TableField("system_code")
    private String systemCode;

}
