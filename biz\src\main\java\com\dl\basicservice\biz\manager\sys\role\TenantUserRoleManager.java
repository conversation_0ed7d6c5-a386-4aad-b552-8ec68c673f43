package com.dl.basicservice.biz.manager.sys.role;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.basicservice.biz.common.annotation.Permission;
import com.dl.basicservice.biz.common.service.CommonService;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserRolePO;
import com.dl.basicservice.biz.manager.sys.role.bo.PermissionBO;
import com.dl.basicservice.biz.manager.sys.role.dto.SysRoleIdDTO;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserRolesParamBO;

import java.util.List;
import java.util.Set;

public interface TenantUserRoleManager extends IService<TenantUserRolePO>, CommonService {

    /**
     * 保存用户-角色配置信息
     *
     * @param bo
     */
    void saveUserRoles(SysUserRolesParamBO bo);

    List<SysRoleIdDTO> findByUserId(Long userId);

    /**
     * 查询员工拥有的角色id
     *
     * @param userId
     * @return
     */
    Set<Long> findUserRoleByUserId(String systemCode, Long userId);

    /**
     * 权限判断
     *
     * @param permission
     * @param systemCode
     * @param userId
     * @return
     */
    boolean permissionLogic(PermissionBO permission,String systemCode, Long userId);

}
