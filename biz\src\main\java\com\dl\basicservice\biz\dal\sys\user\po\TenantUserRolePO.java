package com.dl.basicservice.biz.dal.sys.user.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.Data;

/**
 * <p>
 * 用户-角色表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-23
 */
@Data
@TableName("sys_tenant_user_role")
public class TenantUserRolePO extends BasePO {

    @TableId("id")
    public Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("role_id")
    private Long roleId;

    /**
     * 系统code，如dl-wealth-center
     */
    @TableField("system_code")
    private String systemCode;

}
