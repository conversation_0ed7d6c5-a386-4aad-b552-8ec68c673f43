package com.dl.basicservice.web.controller.internal.adm.sys.admuser.convert;

import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserProfilePO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.AdmUserDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.AdmUserExtDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.UserProfileDTO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 10:42
 */
public class AdmUserConvert {

    public static AdmUserDTO cnvAdmUserPO2DTO(TenantAdmUserPO input){
        if(Objects.isNull(input)){
            return null;
        }
        AdmUserDTO result = new AdmUserDTO();
        fillAdmUserDTO(input,result);
        return result;
    }

    public static AdmUserExtDTO buildAdmUserExtDTO(TenantAdmUserPO input,String tenantName,
            TenantUserProfilePO userProfilePO){
        AdmUserExtDTO result = new AdmUserExtDTO();
        if(Objects.nonNull(userProfilePO)){
            result.setUserName(userProfilePO.getName());
        }
        result.setTenantName(tenantName);
        fillAdmUserDTO(input,result);
        return result;
    }

    public static void fillAdmUserDTO(TenantAdmUserPO input, AdmUserDTO result) {
        result.setUserId(input.getUserId());
        result.setIsSuperAdm(input.getIsSuperAdm());
        result.setAccount(input.getAccount());
        result.setPassword(input.getPassword());
        result.setAccountType(input.getAccountType());
        result.setId(input.getId());
        result.setStatus(input.getStatus());
        result.setTenantCode(input.getTenantCode());
        result.setCreateDt(input.createDt);
        result.setModifyDt(input.modifyDt);
        result.setCreateBy(input.createBy);
        result.setModifyBy(input.modifyBy);
    }

    public static UserProfileDTO cnvUserProfilePO2DTO(TenantUserProfilePO input){
        if(Objects.isNull(input)){
            return null;
        }
        UserProfileDTO result = new UserProfileDTO();
        result.setUserId(input.getUserId());
        result.setProfileId(input.getProfileId());
        result.setAvatar(input.getAvatar());
        result.setGender(input.getGender());
        result.setName(input.getName());
        result.setMobile(input.getMobile());
        result.setNickName(input.getNickName());
        result.setTenantCode(input.getTenantCode());
        result.setThumbAvatar(input.getThumbAvatar());
        return result;
    }
}
