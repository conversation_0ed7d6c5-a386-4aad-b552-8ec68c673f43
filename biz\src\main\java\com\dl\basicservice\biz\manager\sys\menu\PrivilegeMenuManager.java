package com.dl.basicservice.biz.manager.sys.menu;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.basicservice.biz.common.service.CommonService;
import com.dl.basicservice.biz.dal.sys.menu.po.PrivilegeMenuPO;
import com.dl.basicservice.biz.manager.sys.menu.dto.MenuDTO;

import java.util.List;

public interface PrivilegeMenuManager extends IService<PrivilegeMenuPO>, CommonService {

    /**
     * 获取所有系统菜单列表,包含菜单对应的Functions列表
     *
     * @return
     */
    List<MenuDTO> listMenusAndFunctions(String systemCode,List<Integer> sceneTypeList,Boolean isProvider);

    /**
     * 获取所有系统菜单列表和当前租户已分配的菜单和功能列表,包含菜单对应的Functions列表
     *
     * @return
     */
    List<MenuDTO> listMenusAndFunctions(String tenantCode,String systemCode);

}
