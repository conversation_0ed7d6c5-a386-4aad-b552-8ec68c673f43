package com.dl.basicservice.web.controller.internal.adm.sys.tenant;

import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.web.controller.internal.adm.base.InternalAbstractController;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.resp.TenantRoleDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.TenantRoleQueryParamDTO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-17 10:50
 */
@Api("内部调用-租户角色控制器")
@RestController
@RequestMapping("/internal/tenantrole")
public class InternalTenantRoleController extends InternalAbstractController {

    @Resource
    private TenantRoleManager tenantRoleManager;

    @PostMapping("/list")
    public ResultModel<List<TenantRoleDTO>> list(@RequestBody @Validated TenantRoleQueryParamDTO paramDTO) {
        List<TenantRolePO> roles = tenantRoleManager.lambdaQuery().in(TenantRolePO::getRoleId, paramDTO.getTenantCode())
                .eq(TenantRolePO::getTenantCode, paramDTO.getTenantCode())
                .eq(TenantRolePO::getSystemCode, getSystemCode()).
                        list();

        if (CollectionUtils.isEmpty(roles)) {
            return ResultModel.success(Collections.emptyList());
        }
        List<TenantRoleDTO> tenantRoleDTOList = roles.stream().map(po -> {
            TenantRoleDTO dto = new TenantRoleDTO();
            dto.setId(po.getId());
            dto.setName(po.getName());
            dto.setRoleId(po.getRoleId());
            dto.setRoleType(po.getRoleType());
            dto.setTenantCode(po.getTenantCode());
            dto.setSystemCode(po.getSystemCode());
            return dto;
        }).collect(Collectors.toList());
        return ResultModel.success(tenantRoleDTOList);
    }

}
