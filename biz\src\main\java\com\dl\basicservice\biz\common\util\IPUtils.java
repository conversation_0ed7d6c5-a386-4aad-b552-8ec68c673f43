package com.dl.basicservice.biz.common.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.net.InetAddress;
import java.net.UnknownHostException;

public class IPUtils {

    private static final Logger logger = LoggerFactory.getLogger(IPUtils.class);

    /**
     * unknown常量值
     */
    private static final String UNKNOWN = "unknown";

    /**
     * 本机IPV4地址
     */
    private static final String LOCAL_IP_IPV4 = "127.0.0.1";

    /**
     * 本机IPV6地址
     */
    private static final String LOCAL_IP_IPV6 = "0:0:0:0:0:0:0:1";

    /**
     * localhost
     */
    private static final String LOCAL_HOST = "localhost";

    /**
     * 从request里取得真是ip
     *
     * @param request HttpServletRequest
     * @return IP地址
     */
    public static String getIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || UNKNOWN.equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (ip == null) {
            ip = "";
        }
        if (LOCAL_IP_IPV4.equals(ip) || LOCAL_HOST.equalsIgnoreCase(ip) || LOCAL_IP_IPV6.equals(ip)) {

            InetAddress addr;
            try {
                addr = InetAddress.getLocalHost();
                ip = addr.getHostAddress();
            } catch (UnknownHostException e) {
                logger.warn(e.getMessage(), e);
                return "";
            }
        }

        // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        String[] ips = StringUtils.split(ip, ",");
        if (ip != null && ip.length() > 15 && ips.length > 0) {
            // = 15
            ip = ips[0];
        }
        return ip;
    }

    public static void main(String[] args) {
        String ip = "http://***************,http://***************";
        String[] ips = StringUtils.split(ip, ",");
        if (ip != null && ip.length() > 15 && ips.length > 0) {
            // = 15
            ip = ips[0];
        }
        System.out.println(ip);
    }

}
