package com.dl.basicservice.biz.manager.sys.role.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@ApiModel("角色-功能")
@Data
public class RoleFunctionParamBO {

    @NotNull
    @Size(min = 1, max = 5000)
    @ApiModelProperty("权限编码")
    private List<Long> functionIds;

    @NotNull
    @ApiModelProperty("角色主键")
    private Long roleId;

    @ApiModelProperty(hidden = true)
    private Long loginUserId;


    @ApiModelProperty("系统code")
    private String systemCode;
}
