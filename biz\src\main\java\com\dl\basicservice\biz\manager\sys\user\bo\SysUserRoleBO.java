package com.dl.basicservice.biz.manager.sys.user.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("用户-角色")
public class SysUserRoleBO {

    @ApiModelProperty("用户id")
    @Size(min = 1)
    private Integer userId;

    @Size(min = 1)
    @ApiModelProperty("角色id")
    private Integer roleId;

}
