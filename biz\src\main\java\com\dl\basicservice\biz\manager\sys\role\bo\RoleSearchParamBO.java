package com.dl.basicservice.biz.manager.sys.role.bo;

import com.dl.basicservice.biz.common.SearchBO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RoleSearchParamBO extends SearchBO {

    @Size(max = 20)
    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty(hidden = true)
    private String tenantCode;

    @ApiModelProperty("用户id,指定ID后其他条件无效")
    private Long userId;

    private String systemCode;
}

