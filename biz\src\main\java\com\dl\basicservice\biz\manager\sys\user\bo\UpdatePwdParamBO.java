package com.dl.basicservice.biz.manager.sys.user.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@ApiModel
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePwdParamBO {

    @NotNull(message = "旧密码不能为空")
    @Length(min = 4, max = 18, message = "密码长度必须在4-18位之间")
    @ApiModelProperty("旧密码")
    private String password;

    @NotNull(message = "新密码不能为空")
    @Length(min = 4, max = 18, message = "密码长度必须在4-18位之间")
    @ApiModelProperty("新密码")
    private String newPwd;

}
