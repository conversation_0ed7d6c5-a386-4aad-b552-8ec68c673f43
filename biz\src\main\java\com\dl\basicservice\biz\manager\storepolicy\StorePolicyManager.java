package com.dl.basicservice.biz.manager.storepolicy;

import com.dl.basicservice.biz.common.tencentcloud.cos.CosGetTempCredentialBO;
import com.dl.basicservice.biz.manager.storepolicy.dto.BaseTempCredentialDTO;

import java.io.File;

/**
 * @ClassName StorePolicyManager
 * @Description
 * <AUTHOR>
 * @Date 2022/10/10 9:16
 * @Version 1.0
 **/
public interface StorePolicyManager {

    /**
     * 获取临时访问token
     *
     * @param param
     * @return
     */
    BaseTempCredentialDTO getTempCredential(CosGetTempCredentialBO param);
}
