package com.dl.basicservice.web.controller.internal.adm.sys.admuser.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 14:12
 */
@Data
public class AdmUserAddParamDTO implements Serializable {

    @ApiModelProperty("userid，支持业务侧生成")
    private Long userId;

    private String tenantCode;

    /**
     * 登录账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 账号状态
     * 0-启用
     * 1-锁定
     * 2-禁用
     * <p>
     * ADM_USER_STATUS
     */
    private Integer status;

    /**
     * 是否超管
     * 1-是
     * 0-否
     */
    private Integer isSuperAdm;

    @ApiModelProperty("用户账号类型 0 B端用户 1 小程序端用户 默认为B端用户")
    private Integer accountType;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("性别 0表示未定义，1表示男性，2表示女性")
    private Integer gender;

    @ApiModelProperty("手机")
    private String mobile;

    @NotNull(message = "创建人不能为空")
    private Long createBy;
}
