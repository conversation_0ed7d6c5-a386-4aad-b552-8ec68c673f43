package com.dl.basicservice.biz.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class RedisUtil {

    private final Logger LOGGER = LoggerFactory.getLogger(RedisUtil.class);
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return 是否设置成功
     */
    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("获取key的失效时间异常" + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取锁（timeout）
     *
     * @param key     缓存KEY
     * @param timeout 锁的固定过期时间(秒)
     * @return
     */
    public boolean tryLockAndSetTimeout(String key, long timeout) {
        Assert.isTrue(timeout > 0, "锁设置必须指定timeout");
        boolean res = false;
        res = redisTemplate.opsForValue().setIfAbsent(key, "currentLock");
        if (res) {
            // 设置锁成功
            LOGGER.info("setCurrentLock, key:{}, expire in:{} seconds.", key, timeout);
            redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
        } else {
            // 锁被占用
            LOGGER.info("key:{},locked by another business", key);
            long expireTime = redisTemplate.getExpire(key);
            if (-1 == expireTime) {
                // 如果没有过期时间
                LOGGER.info("This locked has not expire,now reset timeout:", timeout);
                redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
            }
        }
        return res;
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false不存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            LOGGER.error("判断key是否存在异常" + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(Arrays.asList(key));

            }
        }
    }

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    public <V> V get(String key) {
        return key == null ? null : (V) redisTemplate.opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            LOGGER.error("设置String类型异常" + e.getMessage(), e);
            return false;
        }
    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("设置String类型及有效期异常" + e.getMessage(), e);
            return false;
        }
    }

    public void addSet(String key, Object value) {
        try {
            redisTemplate.opsForSet().add(key, value);
        } catch (Exception e) {
            LOGGER.error("设置set异常" + e.getMessage(), e);
        }
    }

    public <T> void addSetAll(String key, T[] arrays) {
        try {
            redisTemplate.opsForSet().add(key, arrays);
        } catch (Exception e) {
            LOGGER.error("设置set异常" + e.getMessage(), e);
        }
    }

    public <T> Set<T> union(Collection<String> keys) {
        try {
            return (Set<T>) redisTemplate.opsForSet().union(keys);
        } catch (Exception e) {
            LOGGER.error("unionAndStore异常" + e.getMessage(), e);
            return Collections.emptySet();
        }
    }

    public void unionAndStore(Collection<String> keys, String destKey) {
        try {
            redisTemplate.opsForSet().unionAndStore(keys, destKey);
        } catch (Exception e) {
            LOGGER.error("unionAndStore异常" + e.getMessage(), e);
        }
    }

    public void addZSet(String key, Object value, double score) {
        try {
            redisTemplate.opsForZSet().add(key, value, score);
        } catch (Exception e) {
            LOGGER.error("设置set异常" + e.getMessage(), e);
        }
    }

    public void addZSet(String key, Set<ZSetOperations.TypedTuple<Object>> tuples) {
        try {
            redisTemplate.opsForZSet().add(key, tuples);
        } catch (Exception e) {
            LOGGER.error("设置set异常" + e.getMessage(), e);
        }
    }

    public Set<Object> getZSetByScroe(String key, double minScore, double maxScore) {
        try {
            return redisTemplate.opsForZSet().rangeByScore(key, minScore, maxScore);
        } catch (Exception e) {
            LOGGER.error("设置set异常" + e.getMessage(), e);
            return null;
        }
    }

    public <T> Set<T> getSet(String key) {
        try {
            return (Set<T>) redisTemplate.opsForSet().members(key);
        } catch (Exception e) {
            LOGGER.error("设置set异常" + e.getMessage(), e);
            return null;
        }
    }

    public <T> Set<T> getSet(Collection<String> keys) {
        try {
            return (Set<T>) redisTemplate.opsForSet().union(keys);
        } catch (Exception e) {
            LOGGER.error("设置set异常" + e.getMessage(), e);
            return null;
        }
    }

    public <HK, HV> void hset(String key, Map<HK, HV> map) {
        try {
            redisTemplate.opsForHash().putAll(key, map);
        } catch (Exception e) {
            LOGGER.error("设置hset异常" + e.getMessage(), e);
        }
    }

    public <HK, HV> void hset(String key, HK hk, HV hv) {
        try {
            redisTemplate.opsForHash().put(key, hk, hv);
        } catch (Exception e) {
            LOGGER.error("设置hset异常" + e.getMessage(), e);
        }
    }

    public <HK, HV> HV hget(String key, HK hk) {
        try {
            return redisTemplate.<HK, HV>opsForHash().get(key, hk);
        } catch (Exception e) {
            LOGGER.error("设置hget异常" + e.getMessage(), e);
            return null;
        }
    }
    public <HK, HV> Map<HK,HV> hget(String key) {
        try {
            return redisTemplate.<HK, HV>opsForHash().entries(key);
        } catch (Exception e) {
            LOGGER.error("设置hget异常" + e.getMessage(), e);
            return null;
        }
    }

    public <HK, HV> List<HV> hget(String key, Collection<HK> hk) {
        try {
            return redisTemplate.<HK, HV>opsForHash().multiGet(key, hk);
        } catch (Exception e) {
            LOGGER.error("设置hget异常" + e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public <HK,HV> List<HV> hgetValues(String key) {
        try {
            return redisTemplate.<HK,HV>opsForHash().values(key);
        } catch (Exception e) {
            LOGGER.error("设置hget异常" + e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    public boolean isMember(String key, Object value) {
        try {
            return redisTemplate.opsForSet().isMember(key, value);
        } catch (Exception e) {
            LOGGER.error("isMember 异常" + e.getMessage(), e);
            return false;
        }
    }

    public <HK, HV> boolean hexists(String key, HK hk) {
        try {
            return redisTemplate.hasKey(key) && redisTemplate.opsForHash().hasKey(key, hk);
        } catch (Exception e) {
            LOGGER.error("hexists异常" + e.getMessage(), e);
            return false;
        }
    }

    public void flushall() {
        try {
            redisTemplate.execute((RedisCallback) conn -> {
                conn.execute("flushall");
                return null;
            });
        } catch (Exception e) {
            LOGGER.error("flushall" + e.getMessage(), e);
        }
    }

    /**
     * key对应的value在一定时间内自增
     *
     * @param key
     * @param time
     */
    public boolean increment(String key, long time) {
        try {
            if (redisTemplate.hasKey(key)) {
                redisTemplate.opsForValue().increment(key, 1);
            } else {
                redisTemplate.opsForValue().set(key, 1);
                redisTemplate.expire(key, time, TimeUnit.MILLISECONDS);
            }
            return true;
        } catch (Exception e) {
            LOGGER.error("increment异常" + e.getMessage(), e);
            return false;
        }
    }
}
