package com.dl.basicservice.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.mq.DlChannels;
import com.dl.basicservice.biz.mq.producer.dto.TenantDepartmentInitDTO;
import com.dl.basicservice.biz.mq.producer.dto.WechatUpdateCallBackDTO;
import com.dl.basicservice.biz.mq.producer.dto.WechatUpdateTenantCodeDTO;
import com.dl.basicservice.biz.mq.producer.enums.DelayLevelEnum;
import org.apache.rocketmq.common.message.MessageConst;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 租户部门初始化
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-30 09:36
 */
@Component
public class WeChatProducer {
    private static final Logger LOGGER = LoggerFactory.getLogger(WeChatProducer.class);

    @Resource
    private DlChannels dlChannels;

    public void sendUpdateTenantCodeMQ(String newTenantCode, String originTenantCode) {
        WechatUpdateTenantCodeDTO msgDTO = new WechatUpdateTenantCodeDTO();
        msgDTO.setNewTenantCode(newTenantCode);
        msgDTO.setOriginTenantCode(originTenantCode);

        try {
            this.doUpdateTenantCodeMQ(msgDTO);
        } catch (Exception e) {
            LOGGER.error("更新租户Code的mq 发送异常，msgDTO:{},,,,e:{}", JSONUtil.toJsonStr(msgDTO), e);
        }
    }

    private void doUpdateTenantCodeMQ(WechatUpdateTenantCodeDTO msgDTO) {
        Message message = MessageBuilder.withPayload(msgDTO)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, DelayLevelEnum.ONE_SECEND.getValue()).build();
        boolean sendResult = dlChannels.wechatupdatetenantcode().send(message, 1000L);
        LOGGER.info("更新租户Code的mq ,message:{},sendResult:{}", JSONUtil.toJsonStr(message), sendResult);
    }

    public void sendUpdateTenantDomainMQ(String tenantCode, String tenantDomain) {
        WechatUpdateCallBackDTO msgDTO = new WechatUpdateCallBackDTO();
        msgDTO.setTenantCode(tenantCode);
        msgDTO.setTenantDomain(tenantDomain);

        try {
            this.doUpdateTenantDomainMQ(msgDTO);
        } catch (Exception e) {
            LOGGER.error("更新租户domain的mq 发送异常，msgDTO:{},,,,e:{}", JSONUtil.toJsonStr(msgDTO), e);
        }
    }

    private void doUpdateTenantDomainMQ(WechatUpdateCallBackDTO msgDTO) {
        Message message = MessageBuilder.withPayload(msgDTO)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, DelayLevelEnum.ONE_SECEND.getValue()).build();
        boolean sendResult = dlChannels.wechatupdatetenantdomain().send(message, 1000L);
        LOGGER.info("更新租户domain的mq ,message:{},sendResult:{}", JSONUtil.toJsonStr(message), sendResult);
    }
}
