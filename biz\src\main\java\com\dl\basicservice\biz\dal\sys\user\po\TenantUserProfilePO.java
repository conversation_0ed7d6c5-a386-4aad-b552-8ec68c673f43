package com.dl.basicservice.biz.dal.sys.user.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户信息表
 */
@Data
@TableName("sys_tenant_user_profile")
@AllArgsConstructor
@NoArgsConstructor
public class TenantUserProfilePO extends BasePO {

    @TableId("id")
    Long id;

    /**
     * userid
     */
    @TableField("profile_id")
    Long profileId;

    /**
     * userid
     */
    @TableField("user_id")
    Long userId;

    @TableField("tenant_code")
    private String tenantCode;

    @TableField("name")
    String name;

    @TableField("nick_name")
    String nickName;

    @TableField("mobile")
    String mobile;

    /**
     * 0表示未定义，1表示男性，2表示女性
     */
    @TableField("gender")
    Integer gender;

    @TableField("is_deleted")
    private Integer isDeleted;

    @TableField("avatar")
    private String avatar;

    @TableField("thumb_avatar")
    private String thumbAvatar;
}
