package com.dl.basicservice.web.controller.adm.sys.admuser;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.service.ObjectStoragePathService;
import com.dl.basicservice.biz.dal.sys.function.po.PrivilegeFunctionPO;
import com.dl.basicservice.biz.dal.sys.menu.dto.PrivilegeMenuDTO;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserProfilePO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserRolePO;
import com.dl.basicservice.biz.manager.sys.menu.impl.UserRoleMenuRedisCache;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.biz.manager.sys.role.TenantUserRoleManager;
import com.dl.basicservice.biz.manager.sys.role.dto.SysRoleIdDTO;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.TenantUserProfileManager;
import com.dl.basicservice.biz.manager.sys.user.bo.AddUserBO;
import com.dl.basicservice.biz.manager.sys.user.bo.ResetUserPasswordBO;
import com.dl.basicservice.biz.manager.sys.user.bo.SysAdmUserBO;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserRolesParamBO;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserSearchParamBO;
import com.dl.basicservice.biz.manager.sys.user.bo.UpdateUserSelfBO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserWithProfileDTO;
import com.dl.basicservice.biz.manager.sys.user.enums.SysUserStatusEnum;
import com.dl.basicservice.web.controller.adm.sys.admuser.param.AddUserEmployeeParam;
import com.dl.basicservice.web.controller.adm.sys.admuser.param.ResetUserPasswordParam;
import com.dl.basicservice.web.controller.adm.sys.admuser.param.SysUserPageParam;
import com.dl.basicservice.web.controller.adm.sys.admuser.param.SysUserRolesParam;
import com.dl.basicservice.web.controller.adm.sys.admuser.param.UpdateSelfInfoParam;
import com.dl.basicservice.web.controller.adm.sys.admuser.param.UserIdParam;
import com.dl.basicservice.web.controller.adm.sys.admuser.vo.AdmUserVO;
import com.dl.basicservice.web.controller.adm.sys.menu.vo.FuncVO;
import com.dl.basicservice.web.controller.adm.sys.role.vo.RoleVO;
import com.dl.basicservice.web.controller.adm.sys.tenant.convert.TenantConvert;
import com.dl.basicservice.web.controller.adm.sys.tenant.vo.TenantBaseVO;
import com.dl.basicservice.web.controller.adm.sys.tenant.vo.UserMenuVO;
import com.dl.basicservice.web.controller.adm.base.AbstractController;
import com.dl.basicservice.web.util.AccountCompent;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/admusers")
@Api("员工信息")
@Validated
public class AdmUserController extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(AdmUserController.class);
    private static final String ADMIN = "admin";

    @Autowired
    private AccountCompent accountCompent;
    @Autowired
    private SysAdmUserService sysAdmUserService;
    @Autowired
    private TenantUserProfileManager tenantUserProfileManager;
    @Autowired
    private TenantUserRoleManager iSysUserRoleService;
    @Autowired
    private TenantRoleManager tenantRoleManager;
    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;
    @Autowired
    private TenantInfoManager tenantInfoManager;
    @Autowired
    private ObjectStoragePathService objectStoragePathService;

    @GetMapping("/getAdmUserInfo")
    @ApiOperation("当前用户详情")
    public ResultModel<AdmUserVO> getAdmUserInfo() {
        SysAdmUserDTO dto = accountCompent.getCurrentDetail();
        Assert.isTrue(dto != null, "当前用户异常，请重新登录");

        AdmUserVO admUserVO = new AdmUserVO();
        admUserVO.setUserId(dto.getUserId() + "");
        admUserVO.setAccount(dto.getAccount());

        admUserVO.setIsSuperAdm(dto.getIsSuperAdm() + "");

        admUserVO.setToken(dto.getToken());
        admUserVO.setTenantCode(dto.getTenantCode());
        admUserVO.setStatus(dto.getStatus() + "");
        admUserVO.setRcToken(sysAdmUserService.getRcToken(dto.getUserId(),dto.getUserName(),dto.getTenantCode()));

        //如果是admin用户，userName设置为admin(PS:admin账户在user_profile不会创建信息)
        if (Objects.equals(ADMIN, dto.getAccount())) {
            admUserVO.setUserName(ADMIN);
        }

        TenantUserProfilePO userProfilePO = tenantUserProfileManager.lambdaQuery()
                .eq(TenantUserProfilePO::getUserId, dto.getUserId()).eq(TenantUserProfilePO::getIsDeleted, Const.ZERO)
                .one();
        if (Objects.nonNull(userProfilePO)) {
            admUserVO.setUserName(userProfilePO.getName());
            admUserVO.setGender(userProfilePO.getGender().toString());
            admUserVO.setMobile(userProfilePO.getMobile());
            admUserVO.setThumbAvatar(objectStoragePathService.compositeObjectStoragePath(userProfilePO.getThumbAvatar()));
        }

        String systemCode = getSystemCode();
        Set<PrivilegeFunctionPO> set = sysAdmUserService.getUserFunctions(systemCode, dto.getUserId());
        if (CollectionUtils.isNotEmpty(set)) {
            admUserVO.setFunctions(set.stream().map(f -> {
                if (Objects.isNull(f)) {
                    return null;
                }
                FuncVO fv = new FuncVO();
                fv.setFunctionId(f.getFunctionId() + "");
                fv.setFunctionCode(f.getFunctionCode());
                return fv;
            }).filter(Objects::nonNull).collect(Collectors.toSet()));
        }

        List<PrivilegeMenuDTO> list = userRoleMenuRedisCache.getMenus(systemCode, getUserRoleids());
        if (CollectionUtils.isNotEmpty(list)) {
            admUserVO.setMenus(this.listWithTree(list));
        }

        SysTenantInfoPO tenantInfoPO = tenantInfoManager.getTenantInfoFromCache(dto.getTenantCode());
        String smallLogo = tenantInfoPO.getSmallLogo();
        String logoImg = tenantInfoPO.getLogoImg();
        if (StringUtils.isNotBlank(smallLogo)) {
            tenantInfoPO.setSmallLogo(objectStoragePathService.compositeObjectStoragePath(smallLogo));
        }
        if (StringUtils.isNotBlank(logoImg)) {
            tenantInfoPO.setLogoImg(objectStoragePathService.compositeObjectStoragePath(logoImg));
        }
        TenantBaseVO tenantBaseVO = new TenantBaseVO();
        TenantConvert.fillTenantBaseVO(tenantInfoPO, tenantBaseVO);
        admUserVO.setTenantInfo(tenantBaseVO);

        return ResultModel.success(admUserVO);
    }

    //@Permission(value = { "adm:user:query", "adm:minniapp:user:query" }, logical = Logical.OR)
    @PostMapping("/page")
    @ApiOperation("查询用户列表")
    public ResultPageModel<AdmUserVO> page(@RequestBody @Validated SysUserPageParam param) {
        SysUserSearchParamBO bo = new SysUserSearchParamBO();
        bo.setTenantCode(getTenantCode());
        bo.setUserName(param.getUserName());
        bo.setStatus(param.getStatus());
        bo.setAccount(param.getAccount());
        bo.setPageIndex(param.getPageIndex());
        bo.setPageSize(param.getPageSize());
        //默认只取B端
        if (Objects.nonNull(param.getAccountType())) {
            bo.setAccountType(param.getAccountType());
        }
        IPage<SysAdmUserWithProfileDTO> page = sysAdmUserService.findUsers(bo);
        if(Objects.isNull(page) || CollectionUtils.isEmpty(page.getRecords())) {
            return new ResultPageModel<>();
        }

        List<Long> userIds = page.getRecords().stream().map(SysAdmUserWithProfileDTO::getUserId).collect(Collectors.toList());

        List<TenantUserRolePO> userRoles = iSysUserRoleService.lambdaQuery().in(TenantUserRolePO::getUserId, userIds).eq(TenantUserRolePO::getSystemCode, getSystemCode()).list();
        Map<Long, List<TenantRolePO>> userRoleMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(userRoles)) {
            List<TenantRolePO> roles = tenantRoleManager.lambdaQuery().in(TenantRolePO::getRoleId, userRoles.stream().map(TenantUserRolePO::getRoleId).distinct().collect(
                    Collectors.toList())).list();
            Map<Long, TenantRolePO> roleMap = roles.stream().collect(Collectors.toMap(TenantRolePO::getRoleId, Function.identity()));
            userRoleMap.putAll(userRoles.stream().collect(Collectors.groupingBy(TenantUserRolePO::getUserId, Collectors.mapping(userRole -> roleMap.get(userRole.getRoleId()), Collectors.toList()))));
        }
        return pageQueryModel(page.convert(dto -> {
            AdmUserVO vo = new AdmUserVO();
            vo.setUserId(dto.getUserId() + "");
            vo.setUserName(dto.getUserName());
            vo.setAccount(dto.getAccount());
            vo.setTenantCode(dto.getTenantCode());
            vo.setIsSuperAdm(dto.getIsSuperAdm() + "");
            vo.setGender(dto.getGender());
            vo.setStatus(dto.getStatus() + "");
            vo.setStatusStr(SysUserStatusEnum.getByCode(dto.getStatus()).getDesc());
            List<TenantRolePO> roles = userRoleMap.get(dto.getUserId());
            if(CollectionUtils.isNotEmpty(roles)) {
                vo.setRoles(roles.stream().map(r -> {
                    RoleVO roleVO = new RoleVO();
                    roleVO.setRoleId(r.getRoleId() + "");
                    roleVO.setRoleName(r.getName());
                    return roleVO;
                }).collect(Collectors.toList()));
            }
            return vo;
        }));
    }

    /**
     * 此接口为管理员在用户管理列表对用户信息进行修改
     */
    //@Permission(value = { "adm:user:update", "adm:minniapp:user:update" }, logical = Logical.OR)
    @PostMapping("/update")
    @ApiOperation("更新用户信息")
    public ResultModel<Void> update(@RequestBody @Validated SysAdmUserBO bo) {
        //禁止非超管修改用户为超管
        if (Objects.equals(getCurrentDetail().getIsSuperAdm(), 0) && "1".equals(bo.getIsSuperAdm())) {
            return ResultModel.error("2", "权限不足");
        }
        TenantAdmUserPO po = new TenantAdmUserPO();
        po.setUserId(bo.getUserId());
        po.setAccount(bo.getAccount());
        po.setTenantCode(getTenantCode());
        po.setIsSuperAdm(Objects.nonNull(bo.getIsSuperAdm()) ? Integer.valueOf(bo.getIsSuperAdm()) : null);
        po.setStatus(Objects.nonNull(bo.getStatus()) ? Integer.valueOf(bo.getStatus()) : null);
        sysAdmUserService.updateUserDetail(po, bo.getUserName());
        return ResultModel.success(null);
    }

    /**
     * 此接口为用户修改自己的用户信息，故无需增加权限点
     */
    @PostMapping("/updateselfinfo")
    @ApiOperation("用户修改个人信息")
    public ResultModel<Boolean> updateSelfInfo(@RequestBody @Validated UpdateSelfInfoParam param) {
        UpdateUserSelfBO bo = new UpdateUserSelfBO();
        bo.setUserId(getUserId());
        bo.setTenantCode(getTenantCode());
        bo.setAccount(param.getAccount());
        bo.setUserName(param.getUserName());
        if (StringUtils.isNotBlank(param.getGender())){
            bo.setGender(Integer.valueOf(param.getGender()));
        }
        bo.setMobile(param.getMobile());
        bo.setModifyBy(getUserId());
        if(StringUtils.isNotBlank(param.getThumbAvatar())){
            bo.setThumbAvatar(objectStoragePathService.getPathFromPossibleUrl(param.getThumbAvatar()));
        }
        boolean request = sysAdmUserService.updateUserSelfDetail(bo);
        //修改了账号则退出登录
        if (!StringUtils.isBlank(param.getAccount())) {
            sysAdmUserService.logoutJwtToken(getToken());
        }
        return ResultModel.success(request);
    }

    //@Permission(value = { "adm:user:resetpassword", "adm:minniapp:user:resetpassword" }, logical = Logical.OR)
    @PostMapping("/resetuserpassword")
    @ApiOperation("重置用户密码")
    public ResultModel<Boolean> resetUserPassword(@RequestBody @Validated ResetUserPasswordParam param) {
        ResetUserPasswordBO BO = new ResetUserPasswordBO();
        BO.setUserId(param.getUserId());
        BO.setPassword(param.getPassword());
        BO.setTenantCode(getTenantCode());
        BO.setOldPassword(param.getOldPassword());
        return ResultModel.success(sysAdmUserService.resetUserPassword(BO));
    }

    //@Permission(value = { "adm:user_employee:add", "adm:minniapp:user_employee:add" }, logical = Logical.OR)
    @PostMapping("/useremployee/add")
    @ApiOperation("新增用户+角色")
    public ResultModel<AdmUserVO> addUserEmployee(@RequestBody @Validated AddUserEmployeeParam param) {
        LambdaQueryWrapper<TenantAdmUserPO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
                .eq(TenantAdmUserPO::getTenantCode,getTenantCode())
                .eq(TenantAdmUserPO::getAccount,param.getAccount().trim());
        TenantAdmUserPO userPO = sysAdmUserService.getOne(queryWrapper);
        Assert.isNull(userPO,"登录账号已存在");
        AddUserBO bo = AddUserBO.builder()
                .account(param.getAccount().trim())
                .mobile(param.getMobile())
                .password(param.getPassword().trim())
                .name(param.getName())
                .tenantCode(getTenantCode())
                .systemCode(getSystemCode())
                .build();
        //进入此步代表小程序管理后台添加用户，不需要员工id(包括magic)
        Long userId = sysAdmUserService.addUser(bo, param.getRoleIds());
        AdmUserVO vo = new AdmUserVO();
        vo.setUserId(String.valueOf(userId));
        return ResultModel.success(vo);
    }

    //@Permission(value = { "adm:user_role:save", "adm:minniapp:user_role:save" }, logical = Logical.OR)
    @PostMapping("/userrole/save")
    @ApiOperation("保存用户-角色配置")
    public ResultModel<Void> saveUserRole(@RequestBody @Validated SysUserRolesParam param) {
        SysUserRolesParamBO bo = new SysUserRolesParamBO();
        if(StringUtils.isNumeric(param.getUserId())) {
            bo.setUserId(Long.valueOf(param.getUserId()));
        }
        bo.setRoleIds(param.getRoleIds());
        bo.setTenantCode(getTenantCode());
        bo.setSystemCode(getSystemCode());

        iSysUserRoleService.saveUserRoles(bo);
        return ResultModel.success(null);
    }

    //@Permission("adm:user_role:query")
    @PostMapping("/role/query")
    @ApiOperation("查询用户-角色配置")
    public ResultModel<List<RoleVO>> queryUserRoles(@RequestBody @Validated UserIdParam param){
        List<SysRoleIdDTO> list = iSysUserRoleService.findByUserId(Long.valueOf(param.getUserId()));
        if(CollectionUtils.isEmpty(list)) {
            return new ResultModel<>();
        }
        return  ResultModel.success(list.stream().map(dto -> {
            RoleVO vo = new RoleVO();
            vo.setRoleId(String.valueOf(dto.getRoleId()));
            return vo;
        }).collect(Collectors.toList()));
    }

    // 组装微树形
    private List<UserMenuVO> listWithTree(List<PrivilegeMenuDTO> entities) {
        // 2 组装成父子的树形结构
        List<UserMenuVO> level1Menus = entities.stream().filter(entity -> entity.getParentId() == 0).map(m -> {
            UserMenuVO vo = new UserMenuVO();
            vo.setMenuId(String.valueOf(m.getMenuId()));
            vo.setIcon(m.getIcon());
            vo.setLevel(m.getMenuLevel());
            vo.setName(m.getName());
            vo.setSort(m.getSort());
            vo.setParentId(String.valueOf(m.getParentId()));
            vo.setUrl(m.getUrl());
            vo.setChildren(getChildrens(m, entities));
            return vo;
        }).sorted(Comparator.comparingInt(menu -> (menu.getSort() == null ? 0 : menu.getSort()))).collect(Collectors.toList());
        return level1Menus;
    }

    // 递归查找所有菜单的子菜单
    private List<UserMenuVO> getChildrens(PrivilegeMenuDTO root, List<PrivilegeMenuDTO> all) {
        List<UserMenuVO> children = all.stream().filter(entity -> {
            return entity.getParentId().longValue() == root.getMenuId()
                    .longValue();  // 注意此处应该用longValue()来比较，否则会出先bug，因为parentCid和catId是long类型
        }).map(m -> {
            // 1 找到子菜单
            UserMenuVO vo = new UserMenuVO();
            vo.setMenuId(String.valueOf(m.getMenuId()));
            vo.setIcon(m.getIcon());
            vo.setLevel(m.getMenuLevel());
            vo.setName(m.getName());
            vo.setSort(m.getSort());
            vo.setParentId(String.valueOf(m.getParentId()));
            vo.setUrl(m.getUrl());
            vo.setChildren(getChildrens(m, all));
            return vo;
        }).sorted(Comparator.comparingInt(menu -> (menu.getSort() == null ? 0 : menu.getSort())))
                .collect(Collectors.toList());
        return children;
    }

}
