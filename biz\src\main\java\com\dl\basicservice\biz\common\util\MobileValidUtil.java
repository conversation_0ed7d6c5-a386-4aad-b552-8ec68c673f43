package com.dl.basicservice.biz.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @ClassName MobileValidUtil
 * @Description
 * <AUTHOR>
 * @Date 2022/5/17 18:47
 * @Version 1.0
 **/
public class MobileValidUtil {

    /**
     * 简易验证手机号
     */
    private static Pattern p = Pattern.compile("^1[0-9]{10}$");

    public static boolean isMobile(String str) {
        if (StringUtils.isBlank(str)) {
            return Boolean.FALSE;
        }
        Matcher m = p.matcher(str);
        return m.matches();
    }
}
