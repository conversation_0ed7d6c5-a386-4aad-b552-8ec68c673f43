package com.dl.basicservice.web.controller.internal.adm.sys.admuser;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.enums.CommonCode;
import com.dl.basicservice.biz.common.service.ObjectStoragePathService;
import com.dl.basicservice.biz.dal.sys.function.po.PrivilegeFunctionPO;
import com.dl.basicservice.biz.dal.sys.menu.dto.PrivilegeMenuDTO;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserProfilePO;
import com.dl.basicservice.biz.manager.sys.menu.impl.UserRoleMenuRedisCache;
import com.dl.basicservice.biz.manager.sys.role.TenantUserRoleManager;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.TenantUserProfileManager;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.web.controller.internal.adm.base.InternalAbstractController;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.convert.AdmUserConvert;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.request.*;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.*;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.request.AdmUserAddParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.request.AdmUserBatchAddParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.request.AdmUserInfoQueryDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.request.AdmUserListQueryDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.request.AdmUserUptParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.request.UserProfileQueryDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.AdmUserDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.AdmUserDetailDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.FuncDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.UserMenuDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.UserProfileDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.convert.InternalTenantConvert;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.resp.TenantInfoDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import io.swagger.annotations.Api;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-17 11:01
 */
@Api("内部调用-账号控制器")
@RestController
@RequestMapping("/internal/admusers")
public class InternalAdmUserController extends InternalAbstractController {

    @Resource
    private SysAdmUserService sysAdmUserService;
    @Resource
    private HostTimeIdg hostTimeIdg;
    @Resource
    private TenantUserProfileManager tenantUserProfileManager;
    @Autowired
    private TenantUserRoleManager tenantUserRoleManager;
    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;
    @Autowired
    private TenantInfoManager tenantInfoManager;
    @Autowired
    private ObjectStoragePathService objectStoragePathService;

    @PostMapping("/info")
    public ResultModel<AdmUserDTO> info(@RequestBody AdmUserInfoQueryDTO queryDTO) {
        TenantAdmUserPO po = sysAdmUserService.getOne(Wrappers.lambdaQuery(TenantAdmUserPO.class)
                .eq(Objects.nonNull(queryDTO.getUserId()), TenantAdmUserPO::getUserId, queryDTO.getUserId())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantCode()), TenantAdmUserPO::getTenantCode,
                        queryDTO.getTenantCode())
                .eq(StringUtils.isNotBlank(queryDTO.getAccount()), TenantAdmUserPO::getAccount, queryDTO.getAccount()));

        return ResultModel.success(AdmUserConvert.cnvAdmUserPO2DTO(po));
    }

    @PostMapping("/list")
    public ResultModel<List<AdmUserDTO>> list(@RequestBody AdmUserListQueryDTO queryDTO) {
        List<TenantAdmUserPO> poList = sysAdmUserService.list(Wrappers.lambdaQuery(TenantAdmUserPO.class)
                .in(Objects.nonNull(queryDTO.getUserIdList()), TenantAdmUserPO::getUserId, queryDTO.getUserIdList())
                .eq(StringUtils.isNotBlank(queryDTO.getTenantCode()), TenantAdmUserPO::getTenantCode,
                        queryDTO.getTenantCode()));
        return ResultModel.success(poList.stream().map(AdmUserConvert::cnvAdmUserPO2DTO).collect(Collectors.toList()));
    }

    @PostMapping("/add")
    public ResultModel<AdmUserDTO> add(@RequestBody @Validated AdmUserAddParamDTO paramDTO) {
        //校验是否已存在
        if (Objects.nonNull(paramDTO.getUserId())) {
            TenantAdmUserPO exist = sysAdmUserService.getOne(Wrappers.lambdaQuery(TenantAdmUserPO.class)
                    .eq(TenantAdmUserPO::getTenantCode, paramDTO.getTenantCode())
                    .eq(TenantAdmUserPO::getUserId, paramDTO.getUserId()));
            if (Objects.nonNull(exist)) {
                return ResultModel.error(CommonCode.RECORD_EXISTED.getCode(), "userId已有记录");
            }
        }

        //校验是否已存在
        TenantAdmUserPO exist = sysAdmUserService.getOne(Wrappers.lambdaQuery(TenantAdmUserPO.class)
                .eq(TenantAdmUserPO::getTenantCode, paramDTO.getTenantCode())
                .eq(TenantAdmUserPO::getAccount, paramDTO.getAccount()));
        if (Objects.nonNull(exist)) {
            return ResultModel.error(CommonCode.RECORD_EXISTED.getCode(), "用户名已有记录");
        }

        TenantAdmUserPO insertAdmUserPO = new TenantAdmUserPO();
        insertAdmUserPO.setAccount(paramDTO.getAccount());
        insertAdmUserPO.setPassword(paramDTO.getPassword());
        insertAdmUserPO.setTenantCode(paramDTO.getTenantCode());
        insertAdmUserPO.setUserId(
                Objects.nonNull(paramDTO.getUserId()) ? paramDTO.getUserId() : hostTimeIdg.generateId().longValue());
        insertAdmUserPO.setIsSuperAdm(paramDTO.getIsSuperAdm());
        insertAdmUserPO.setCreateBy(paramDTO.getCreateBy());
        insertAdmUserPO.setModifyBy(paramDTO.getCreateBy());
        if (Objects.nonNull(paramDTO.getAccountType())) {
            insertAdmUserPO.setAccountType(paramDTO.getAccountType());
        }
        insertAdmUserPO.setStatus(paramDTO.getStatus());
        sysAdmUserService.save(insertAdmUserPO);

        TenantUserProfilePO insertProfilePO = new TenantUserProfilePO();
        insertProfilePO.setProfileId(hostTimeIdg.generateId().longValue());
        insertProfilePO.setUserId(insertAdmUserPO.getUserId());
        insertProfilePO.setName(paramDTO.getUserName());
        insertProfilePO.setGender(paramDTO.getGender());
        insertProfilePO.setMobile(paramDTO.getMobile());
        insertProfilePO.setNickName(paramDTO.getNickName());
        insertProfilePO.setTenantCode(paramDTO.getTenantCode());
        insertProfilePO.setCreateBy(paramDTO.getCreateBy());
        insertProfilePO.setModifyBy(paramDTO.getCreateBy());
        tenantUserProfileManager.save(insertProfilePO);

        return ResultModel.success(AdmUserConvert.cnvAdmUserPO2DTO(insertAdmUserPO));
    }

    @PostMapping("/batchadd")
    public ResultModel<List<AdmUserDTO>> batchAdd(@RequestBody @Validated AdmUserBatchAddParamDTO paramDTO) {
        List<AdmUserAddParamDTO> params = paramDTO.getParams();

        //校验是否已存在
        List<Long> userIds = params.stream().filter(dto -> Objects.nonNull(dto.getUserId()))
                .map(AdmUserAddParamDTO::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(userIds)) {
            List<TenantAdmUserPO> exists = sysAdmUserService.list(Wrappers.lambdaQuery(TenantAdmUserPO.class)
                    .eq(TenantAdmUserPO::getTenantCode, paramDTO.getTenantCode())
                    .in(TenantAdmUserPO::getUserId, userIds));
            if (CollectionUtils.isNotEmpty(exists)) {
                return ResultModel.error(CommonCode.RECORD_EXISTED.getCode(), "userId已有记录");
            }
        }
        //校验是否已存在
        List<String> accountList = params.stream().map(AdmUserAddParamDTO::getAccount).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(accountList)) {
            List<TenantAdmUserPO> exists = sysAdmUserService.list(Wrappers.lambdaQuery(TenantAdmUserPO.class)
                    .eq(TenantAdmUserPO::getTenantCode, paramDTO.getTenantCode())
                    .in(TenantAdmUserPO::getAccount, accountList));
            if (CollectionUtils.isNotEmpty(exists)) {
                return ResultModel.error(CommonCode.RECORD_EXISTED.getCode(), "用户名已有记录");
            }
        }

        Map<Long, AdmUserAddParamDTO> userIdParamMap = new HashMap<>();
        List<TenantAdmUserPO> insertList = params.stream().map(param -> {
            TenantAdmUserPO po = new TenantAdmUserPO();
            po.setAccount(param.getAccount());
            po.setPassword(param.getPassword());
            po.setTenantCode(param.getTenantCode());
            po.setUserId(Objects.nonNull(param.getUserId()) ? param.getUserId() : hostTimeIdg.generateId().longValue());
            po.setIsSuperAdm(param.getIsSuperAdm());
            if (Objects.nonNull(param.getAccountType())) {
                po.setAccountType(param.getAccountType());
            }
            po.setStatus(param.getStatus());
            po.setCreateBy(param.getCreateBy());
            po.setModifyBy(param.getCreateBy());
            userIdParamMap.put(po.getUserId(), param);
            return po;
        }).collect(Collectors.toList());
        sysAdmUserService.saveBatch(insertList);

        //批量创建用户信息
        List<TenantUserProfilePO> insertProfileList = new ArrayList<>();
        userIdParamMap.forEach((userId, param) -> {
            TenantUserProfilePO insertProfilePO = new TenantUserProfilePO();
            insertProfilePO.setProfileId(hostTimeIdg.generateId().longValue());
            insertProfilePO.setUserId(userId);
            insertProfilePO.setName(param.getUserName());
            insertProfilePO.setGender(param.getGender());
            insertProfilePO.setMobile(param.getMobile());
            insertProfilePO.setTenantCode(paramDTO.getTenantCode());
            insertProfilePO.setCreateBy(param.getCreateBy());
            insertProfilePO.setModifyBy(param.getCreateBy());
            insertProfilePO.setCreateDt(new Date());
            insertProfilePO.setModifyDt(new Date());
            insertProfileList.add(insertProfilePO);
        });
        tenantUserProfileManager.saveBatch(insertProfileList);

        List<AdmUserDTO> resultList = insertList.stream().map(AdmUserConvert::cnvAdmUserPO2DTO)
                .collect(Collectors.toList());
        return ResultModel.success(resultList);
    }


    @PostMapping("/update")
    public ResultModel update(@RequestBody AdmUserUptParamDTO paramDTO) {
        TenantAdmUserPO currentPO = sysAdmUserService
                .getOne(Wrappers.lambdaQuery(TenantAdmUserPO.class).eq(TenantAdmUserPO::getUserId, paramDTO.getUserId()));
        if (Objects.isNull(currentPO)) {
            throw BusinessServiceException.getInstance("当前用户不存在");
        }

        if (StringUtils.isNotBlank(paramDTO.getAccount()) && StringUtils.isNotBlank(paramDTO.getAccount().trim())) {
            //判断用户名是否存在
            TenantAdmUserPO existAccount = sysAdmUserService.getOne(Wrappers.lambdaQuery(TenantAdmUserPO.class)
                    .eq(TenantAdmUserPO::getAccount, paramDTO.getAccount().trim())
                    .eq(TenantAdmUserPO::getTenantCode, paramDTO.getTenantCode()));
            if (Objects.nonNull(existAccount) && !paramDTO.getUserId().equals(existAccount.getUserId())) {
                throw BusinessServiceException.getInstance("登录账号已存在，请重新输入");
            }
            currentPO.setAccount(paramDTO.getAccount().trim());
        }
        if (Objects.nonNull(paramDTO.getIsSuperAdm())) {
            currentPO.setIsSuperAdm(paramDTO.getIsSuperAdm());
        }
        if (Objects.nonNull(paramDTO.getStatus())) {
            currentPO.setStatus(paramDTO.getStatus());
        }
        if(Objects.nonNull(paramDTO.getModifyBy())){
            currentPO.setModifyBy(paramDTO.getModifyBy());
        }
        if(Objects.nonNull(paramDTO.getAccountType())){
            currentPO.setAccountType(paramDTO.getAccountType());
        }
        sysAdmUserService.updateById(currentPO);

        //查询用户信息
        TenantUserProfilePO userProfilePO = tenantUserProfileManager.lambdaQuery()
                .eq(TenantUserProfilePO::getUserId, currentPO.getUserId()).one();
        if (Objects.nonNull(userProfilePO)) {
            tenantUserProfileManager.lambdaUpdate().eq(TenantUserProfilePO::getUserId, currentPO.getUserId())
                    .set(StringUtils.isNotBlank(paramDTO.getUserName()), TenantUserProfilePO::getName,
                            paramDTO.getUserName())
                    .set(Objects.nonNull(paramDTO.getGender()), TenantUserProfilePO::getGender, paramDTO.getGender())
                    .set(StringUtils.isNotBlank(paramDTO.getMobile()), TenantUserProfilePO::getMobile,
                            paramDTO.getMobile())
                    .set(StringUtils.isNotBlank(paramDTO.getNickName()), TenantUserProfilePO::getNickName,
                            paramDTO.getNickName())
                    .set(TenantUserProfilePO::getModifyDt, new Date())
                    .set(TenantUserProfilePO::getModifyBy, paramDTO.getModifyBy()).update();
        } else {
            TenantUserProfilePO insertProfilePO = new TenantUserProfilePO();
            insertProfilePO.setProfileId(hostTimeIdg.generateId().longValue());
            insertProfilePO.setUserId(currentPO.getUserId());
            insertProfilePO.setName(paramDTO.getUserName());
            insertProfilePO.setMobile(paramDTO.getMobile());
            insertProfilePO.setGender(paramDTO.getGender());
            insertProfilePO.setNickName(paramDTO.getNickName());
            insertProfilePO.setTenantCode(currentPO.getTenantCode());
            insertProfilePO.setCreateBy(paramDTO.getModifyBy());
            insertProfilePO.setModifyDt(new Date());
            insertProfilePO.setModifyBy(paramDTO.getModifyBy());
            tenantUserProfileManager.save(insertProfilePO);
        }

        //删除用户详情缓存
        sysAdmUserService.delUserDetailCache(paramDTO.getUserId());

        return ResultModel.success(currentPO);
    }

    @GetMapping("/getadmuserdetail")
    public ResultModel<AdmUserDetailDTO> getAdmUserDetail(@RequestParam Long userId) {
        SysAdmUserDTO dto = sysAdmUserService.findUserDetailFromCache(userId);
        Assert.isTrue(dto != null, "当前用户异常，请重新登录");

        AdmUserDetailDTO userDetailDTO = new AdmUserDetailDTO();
        userDetailDTO.setUserId(dto.getUserId() + "");
        userDetailDTO.setAccount(dto.getAccount());
        userDetailDTO.setIsSuperAdm(dto.getIsSuperAdm() + "");
        userDetailDTO.setToken(dto.getToken());
        userDetailDTO.setTenantCode(dto.getTenantCode());
        userDetailDTO.setStatus(dto.getStatus() + "");
        userDetailDTO.setRcToken(sysAdmUserService.getRcToken(dto.getUserId(),dto.getUserName(),dto.getTenantCode()));

        TenantUserProfilePO userProfilePO = tenantUserProfileManager.lambdaQuery()
                .eq(TenantUserProfilePO::getUserId, dto.getUserId()).eq(TenantUserProfilePO::getIsDeleted, Const.ZERO)
                .one();
        if (Objects.nonNull(userProfilePO)) {
            userDetailDTO.setUserName(userProfilePO.getName());
            userDetailDTO.setGender(userProfilePO.getGender().toString());
            userDetailDTO.setMobile(userProfilePO.getMobile());
        }

        String systemCode = getSystemCode();
        Set<PrivilegeFunctionPO> set = sysAdmUserService.getUserFunctions(systemCode, dto.getUserId());
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(set)) {
            userDetailDTO.setFunctions(set.stream().map(f -> {
                if (Objects.isNull(f)) {
                    return null;
                }
                FuncDTO fv = new FuncDTO();
                fv.setFunctionId(f.getFunctionId() + "");
                fv.setFunctionCode(f.getFunctionCode());
                return fv;
            }).filter(Objects::nonNull).collect(Collectors.toSet()));
        }

        List<PrivilegeMenuDTO> list = userRoleMenuRedisCache
                .getMenus(systemCode, tenantUserRoleManager.findUserRoleByUserId(systemCode, userId));
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
            userDetailDTO.setMenus(this.listWithTree(list));
        }

        SysTenantInfoPO tenantInfoPO = tenantInfoManager.getTenantInfoFromCache(dto.getTenantCode());
        String smallLogo = tenantInfoPO.getSmallLogo();
        String logoImg = tenantInfoPO.getLogoImg();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(smallLogo)) {
            tenantInfoPO.setSmallLogo(objectStoragePathService.compositeObjectStoragePath(smallLogo));
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(logoImg)) {
            tenantInfoPO.setLogoImg(objectStoragePathService.compositeObjectStoragePath(logoImg));
        }
        TenantInfoDTO tenantDTO = new TenantInfoDTO();
        InternalTenantConvert.fillTenantDTO(tenantInfoPO, tenantDTO);
        userDetailDTO.setTenantInfo(tenantDTO);

        return ResultModel.success(userDetailDTO);
    }

    @PostMapping("/userprofile")
    public ResultModel<UserProfileDTO> getUserProfile(@RequestBody UserProfileQueryDTO queryDTO) {
        TenantUserProfilePO userProfilePO = tenantUserProfileManager
                .getOne(Wrappers.lambdaQuery(TenantUserProfilePO.class)
                        .eq(TenantUserProfilePO::getUserId, queryDTO.getUserId())
                        .eq(Objects.nonNull(queryDTO.getTenantCode()), TenantUserProfilePO::getTenantCode,
                                queryDTO.getTenantCode()));
        return ResultModel.success(AdmUserConvert.cnvUserProfilePO2DTO(userProfilePO));
    }

    @PostMapping("/userprofile/list")
    public ResultModel<List<UserProfileDTO>> userProfileList(@RequestBody AdmUserProfileListParam param){
        if (CollectionUtils.isEmpty(param.getUserIdList())){
            return ResultModel.success(null);
        }
        List<Long> userIds = param.getUserIdList().stream().map(Long::valueOf).collect(Collectors.toList());
        List<TenantUserProfilePO> userProfilePOList = tenantUserProfileManager.list(
                Wrappers.lambdaQuery(TenantUserProfilePO.class).in(TenantUserProfilePO::getUserId, userIds).eq(TenantUserProfilePO::getTenantCode,param.getTenantCode())
                        .eq(TenantUserProfilePO::getIsDeleted, Const.ZERO));
        List<UserProfileDTO> data = userProfilePOList.stream().map(AdmUserConvert::cnvUserProfilePO2DTO)
                .collect(Collectors.toList());
        return ResultModel.success(data);
    }

    // 组装微树形
    private List<UserMenuDTO> listWithTree(List<PrivilegeMenuDTO> entities) {
        // 2 组装成父子的树形结构
        List<UserMenuDTO> level1Menus = entities.stream().filter(entity -> entity.getParentId() == 0).map(m -> {
            UserMenuDTO vo = new UserMenuDTO();
            vo.setMenuId(String.valueOf(m.getMenuId()));
            vo.setIcon(m.getIcon());
            vo.setLevel(m.getMenuLevel());
            vo.setName(m.getName());
            vo.setSort(m.getSort());
            vo.setParentId(String.valueOf(m.getParentId()));
            vo.setUrl(m.getUrl());
            vo.setChildren(getChildrens(m, entities));
            return vo;
        }).sorted(Comparator.comparingInt(menu -> (menu.getSort() == null ? 0 : menu.getSort())))
                .collect(Collectors.toList());
        return level1Menus;
    }

    // 递归查找所有菜单的子菜单
    private List<UserMenuDTO> getChildrens(PrivilegeMenuDTO root, List<PrivilegeMenuDTO> all) {
        List<UserMenuDTO> children = all.stream().filter(entity -> {
            return entity.getParentId().longValue() == root.getMenuId()
                    .longValue();  // 注意此处应该用longValue()来比较，否则会出先bug，因为parentCid和catId是long类型
        }).map(m -> {
            // 1 找到子菜单
            UserMenuDTO vo = new UserMenuDTO();
            vo.setMenuId(String.valueOf(m.getMenuId()));
            vo.setIcon(m.getIcon());
            vo.setLevel(m.getMenuLevel());
            vo.setName(m.getName());
            vo.setSort(m.getSort());
            vo.setParentId(String.valueOf(m.getParentId()));
            vo.setUrl(m.getUrl());
            vo.setChildren(getChildrens(m, all));
            return vo;
        }).sorted(Comparator.comparingInt(menu -> (menu.getSort() == null ? 0 : menu.getSort())))
                .collect(Collectors.toList());
        return children;
    }
}
