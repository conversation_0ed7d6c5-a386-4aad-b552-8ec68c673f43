package com.dl.basicservice.web.controller.internal.app.auth;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.enums.CommonCode;
import com.dl.basicservice.biz.common.enums.SymbolE;
import com.dl.basicservice.biz.common.properties.session.SessionProperty;
import com.dl.basicservice.biz.common.util.JwtUtil;
import com.dl.basicservice.biz.common.util.RedisUtil;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.web.controller.internal.adm.auth.request.TokenParamDTO;
import com.dl.basicservice.web.controller.internal.app.auth.request.AppLoginParamDTO;
import com.dl.basicservice.web.controller.internal.app.auth.resp.AppAuthInfoDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.controller.BaseController;
import com.dl.framework.core.interceptor.expdto.ForceExitException;
import com.dl.framework.core.interceptor.expdto.SessionTimeoutException;
import io.jsonwebtoken.Claims;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-08-09 16:11
 */
@RestController
@RequestMapping("/internal/app/auth")
public class InternalAppAuthController extends BaseController {

    private static final String APP_TOKEN_KEY_PREFIX = "dl.appuser.token.";

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private SysAdmUserService sysAdmUserService;

    @Autowired
    private SessionProperty sessionProperty;

    @PostMapping("/directlogin")
    public ResultModel<String> directLogin(@RequestBody @Validated AppLoginParamDTO paramDTO) {
        TenantAdmUserPO admUserPO = sysAdmUserService
                .getOne(Wrappers.<TenantAdmUserPO>lambdaQuery().eq(TenantAdmUserPO::getUserId, paramDTO.getUserId()));
        Assert.notNull(admUserPO, "您没有系统账号，请联系系统管理员开通");

        String token = createJwtToken(paramDTO);

        if (StringUtils.isBlank(token)) {
            return ResultModel.error(CommonCode.ILLEGAL_STATE.getCode(), "登录失败");
        }

        //设置缓存
        this.setLoginSession(paramDTO.getWxUserId(), paramDTO.getSource(), token);

        return ResultModel.success(token);
    }

    @PostMapping("/checktoken")
    public ResultModel<AppAuthInfoDTO> checkToken(@RequestBody @Validated TokenParamDTO paramDTO) throws Exception {
        //1、解析token
        String token = formatToken(paramDTO.getToken());
        Claims claims = JwtUtil.parseJWT(token);
        String wxUserId = claims.get("wxUserId", String.class);
        String source = claims.get("source", String.class);
        String sessionToken = getTokenBySession(wxUserId, source);
        //2、redis中登录是否超时
        if (org.apache.commons.lang3.StringUtils.isEmpty(sessionToken)) {
            throw new SessionTimeoutException();
        }
        //3、判断是否被其他登录踢掉
        if (!token.equals(sessionToken)) {
            throw new ForceExitException();
        }

        String tenantCode = claims.get("tenantCode", String.class);
        AppAuthInfoDTO dto = new AppAuthInfoDTO();
        dto.setToken(token);
        dto.setWxUserId(wxUserId);
        dto.setTenantCode(tenantCode);
        dto.setUserId(claims.get("userId", String.class));
        dto.setName(claims.get("name", String.class));

        //4、刷新token过期时间
        refreshToken(dto.getWxUserId(), source);

        return ResultModel.success(dto);
    }

    private String createJwtToken(AppLoginParamDTO dto) {
        Map<String, Object> claims = new HashMap<String, Object>();
        claims.put("wxUserId", dto.getWxUserId());
        claims.put("tenantCode", dto.getTenantCode());
        claims.put("userId", dto.getUserId());
        claims.put("name", dto.getName());
        claims.put("source", dto.getSource());
        return JwtUtil.createJWT(claims, "dl.app");
    }


    private String formatToken(String token) {
        return token.substring(token.indexOf(" ") + 1);
    }

    private String getSessionKey(String wxUserId, String source) {
        return APP_TOKEN_KEY_PREFIX + source + SymbolE.DOT.getValue() + wxUserId;
    }

    private String getTokenBySession(String wxUserId, String source) {
        return redisUtil.get(getSessionKey(wxUserId, source));
    }

    private void setLoginSession(String wxUserId, String source, String token) {
        redisUtil.set(getSessionKey(wxUserId, source), token, sessionProperty.getApp().getExpire());
    }

    public void refreshToken(String wxUserId, String source) {
        redisUtil.expire(getSessionKey(wxUserId, source), sessionProperty.getApp().getExpire());
    }

    public static void main(String[] args) {
        Map<String, Object> claims = new HashMap<String, Object>();
        //claims.put("wxUserId", "wortZEbgAA9wBsi1eaeDxFB_FquzQWIQ");
        claims.put("unionId", "wortZEbgAA9wBsi1eaeDxFB_FquzQWIQ");
        claims.put("tenantCode", "DL-DEV");
        claims.put("userId", "1193453901254045326");
        claims.put("name", "王天崇");
        claims.put("source", "qw_app");
        System.out.println(JwtUtil.createJWT(claims, "dl.app"));
    }

}
