package com.dl.basicservice.web.controller.adm.sys.menu.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel("角色菜单权限配置更新")
public class TenantMenuSaveParam {

    @NotNull
    @ApiModelProperty("租户code")
    private String tenantCode;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("菜单主键数组")
    private List<String> menuIds;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("功能编码数组")
    private List<String> functionIds;
}

