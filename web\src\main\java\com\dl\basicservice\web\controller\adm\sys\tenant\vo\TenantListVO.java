package com.dl.basicservice.web.controller.adm.sys.tenant.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TenantListVO {

    @ApiModelProperty("id")
    private String id;

    @ApiModelProperty("租户名称")
    private String name;

    @ApiModelProperty("租户code")
    private String tenantCode;

    @ApiModelProperty("租户状态:0未启用 1正常 2停用")
    private Integer status;

    @ApiModelProperty("是否服务商:0否 1是")
    private Integer isProvider;

    @ApiModelProperty("隐藏二维码登录:0 否 1 是")
    private Integer isHideQrLogin;

    @ApiModelProperty("logo图片")
    private String logoImg;

    @ApiModelProperty("超级管理员账号列表")
    private List<String> adminAccounts;

    @ApiModelProperty("租户访问域名，包含scheme")
    private String tenantDomain;

    @ApiModelProperty("租户访问路径片段，常用于本地化部署")
    private String pathPart;

    @ApiModelProperty("是否企微入驻租户：0 否； 1 是 默认")
    private Integer isWeWork;

    @ApiModelProperty("租户信息描述")
    private String tenantDesc;

    @ApiModelProperty("授权模式，1-微信全托管模式，2-微信第三方平台")
    private Integer authMode;

    @ApiModelProperty("小logo")
    private String smallLogo;

    @ApiModelProperty("是否试用租户 0否 1是")
    Integer isTrial;


    private Date createDt;

    private Long createBy;

    private Date modifyDt;

    private Long modifyBy;

    @ApiModelProperty("租户访问地址")
    private String tenantUrl;
}
