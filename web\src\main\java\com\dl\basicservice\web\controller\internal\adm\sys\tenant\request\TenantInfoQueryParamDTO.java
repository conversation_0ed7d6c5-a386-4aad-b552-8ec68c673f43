package com.dl.basicservice.web.controller.internal.adm.sys.tenant.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-16 15:02
 */
@Data
public class TenantInfoQueryParamDTO implements Serializable {
    private static final long serialVersionUID = -6269535803547912095L;

    @NotBlank
    @ApiModelProperty(value = "租户编码", required = true)
    private String tenantCode;

}
