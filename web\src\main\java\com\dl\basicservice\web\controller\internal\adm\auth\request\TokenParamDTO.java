package com.dl.basicservice.web.controller.internal.adm.auth.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 18:55
 */
@Data
public class TokenParamDTO implements Serializable {
    private static final long serialVersionUID = 2447377768602166534L;

    @NotBlank(message = "token不能为空")
    private String token;
}
