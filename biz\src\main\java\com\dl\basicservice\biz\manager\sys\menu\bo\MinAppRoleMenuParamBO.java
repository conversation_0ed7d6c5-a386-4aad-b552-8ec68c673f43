package com.dl.basicservice.biz.manager.sys.menu.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

@Data
public class MinAppRoleMenuParamBO {

    @ApiModelProperty("角色主键")
    private String roleId;

    @ApiModelProperty("角色名称")
    private String name;

    @ApiModelProperty("租户号")
    private String tenantCode;

    private String systemCode;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("菜单主键数组")
    private List<String> menuIds;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("功能编码数组")
    private List<String> functionIds;
}
