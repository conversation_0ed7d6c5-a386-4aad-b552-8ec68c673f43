package com.dl.basicservice.biz.manager.sys.menu.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@ApiModel("角色菜单权限配置更新")
public class TenantMenuSaveParamBO {

    @NotNull
    @ApiModelProperty("租户id")
    private String tenantCode;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("菜单主键数组")
    private List<Long> menuIds;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("功能编码数组")
    private List<Long> functionIds;

    /**
     * 操作人id
     */
    @ApiModelProperty(hidden = true)
    private Long loginUserId;

    @ApiModelProperty("系统code")
    private String systemCode;
}

