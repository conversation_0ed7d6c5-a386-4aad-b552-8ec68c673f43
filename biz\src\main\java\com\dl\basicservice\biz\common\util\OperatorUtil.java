package com.dl.basicservice.biz.common.util;

import org.springframework.stereotype.Component;

/**
 * biz端的操作人holder
 */
@Component
public class OperatorUtil {

    private static ThreadLocal<Long> userIdHolder=new ThreadLocal<>();
    private static ThreadLocal<String> tenantCodeHolder=new ThreadLocal<>();

    public void init(Long userId,String tenantCode){
        userIdHolder.set(userId);
        tenantCodeHolder.set(tenantCode);
    }

    public Long getOperator(){
        return userIdHolder.get();
    }
    public String getOpTenantCode(){
        return tenantCodeHolder.get();
    }
    public void remove(){
        userIdHolder.remove();
        tenantCodeHolder.remove();
    }
}
