package com.dl.basicservice.web.controller.internal.adm.auth;

import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.enums.CommonCode;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserProfilePO;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.TenantUserProfileManager;
import com.dl.basicservice.biz.manager.sys.user.dto.SysBasicUserDTO;
import com.dl.basicservice.web.controller.internal.adm.auth.request.LoginParamDTO;
import com.dl.basicservice.web.controller.internal.adm.auth.request.LogoutParamDTO;
import com.dl.basicservice.web.controller.internal.adm.auth.request.TokenParamDTO;
import com.dl.basicservice.web.controller.internal.adm.auth.resp.UserLoginDTO;
import com.dl.basicservice.web.controller.internal.adm.base.InternalAbstractController;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.convert.AdmUserConvert;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.AdmUserExtDTO;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.dl.framework.core.interceptor.expdto.CertificateException;
import com.dl.framework.core.interceptor.expdto.ForceExitException;
import com.dl.framework.core.interceptor.expdto.SessionTimeoutException;
import io.jsonwebtoken.MalformedJwtException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 09:57
 */
@Slf4j
@RestController
@RequestMapping("/internal/auth")
public class InternalAuthController extends InternalAbstractController {

    @Resource
    private TenantInfoManager tenantInfoManager;

    @Resource
    private TenantUserProfileManager tenantUserProfileManager;

    @Resource
    private SysAdmUserService sysAdmUserService;

    @PostMapping("/directlogin")
    public ResultModel<UserLoginDTO> directLogin(@RequestBody @Validated LoginParamDTO paramDTO) {
        TenantAdmUserPO admUserPO = sysAdmUserService
                .getOne(Wrappers.<TenantAdmUserPO>lambdaQuery().eq(TenantAdmUserPO::getUserId, paramDTO.getUserId()));
        Assert.notNull(admUserPO, "您没有系统账号，请联系系统管理员开通");
        Assert.isTrue(Objects.equals(admUserPO.getStatus(), Const.ONE), "账号已停用，请联系管理员");

        SysBasicUserDTO sysBasicUserDTO = sysAdmUserService.
                directLogin(paramDTO.getAccount(), paramDTO.getTenantCode(), paramDTO.getUserId());

        if (Objects.isNull(sysBasicUserDTO)) {
            return ResultModel.error(CommonCode.ILLEGAL_STATE.getCode(), "登录失败");
        }

        UserLoginDTO userLoginDTO = new UserLoginDTO();
        userLoginDTO.setUserId(sysBasicUserDTO.getUserId());
        userLoginDTO.setAccount(sysBasicUserDTO.getAccount());
        userLoginDTO.setTenantCode(sysBasicUserDTO.getTenantCode());
        userLoginDTO.setToken(sysBasicUserDTO.getToken());
        return ResultModel.success(userLoginDTO);
    }

    @PostMapping("/logout")
    public ResultModel<Long> logout(@RequestBody LogoutParamDTO paramDTO) {
        Assert.isTrue(StringUtils.isNotBlank(paramDTO.getToken()), "token不能为空");
        String token = paramDTO.getToken();
        log.info("删除用户的token" + token);
        SysBasicUserDTO dto;
        try {
            dto = sysAdmUserService.parseJwtToken(token);
            sysAdmUserService.logoutJwtToken(token);
        } catch (MalformedJwtException e) {
            log.warn("JWT token is illegal");
            throw BusinessServiceException.getInstance("token illegal");
        }
        log.info("用户" + "id为" + dto.getUserId() + ">>>>>>>>>>>>>>>>>>>已登出");
        return ResultModel.success(dto.getUserId());
    }

    @PostMapping("/logoutbyuserid")
    public ResultModel<Long> logoutByUserId(@RequestBody LogoutParamDTO paramDTO) {
        Assert.notNull(paramDTO.getUserId(), "userId不能为空");
        sysAdmUserService.removeLoginSession(paramDTO.getUserId());
        return ResultModel.success(paramDTO.getUserId());
    }

    @PostMapping("/checktoken")
    public ResultModel<AdmUserExtDTO> checkToken(@RequestBody @Validated TokenParamDTO paramDTO) throws Exception {
        SysBasicUserDTO dto;
        String token = paramDTO.getToken();
        try {
            //1.解析token
            token = sysAdmUserService.formatToken(token);
            dto = sysAdmUserService.parseJwtToken(token);
        } catch (Exception e) {
            throw new CertificateException("非法请求");
        }

        //2、redis中登录是否超时
        String sessionToken = sysAdmUserService.getSessionToken(dto.getUserId());
        if (StringUtils.isEmpty(sessionToken)) {
            throw new SessionTimeoutException();
        }
        //3、判断是否被其他登录踢掉
        if (!token.equals(sessionToken)) {
            throw new ForceExitException();
        }
        TenantAdmUserPO userPO = sysAdmUserService.lambdaQuery().eq(TenantAdmUserPO::getUserId, dto.getUserId()).one();
        if (Objects.isNull(userPO)) {
            throw BusinessServiceException.getInstance("不存在该用户");
        }

        TenantUserProfilePO userProfilePO = tenantUserProfileManager
                .getOne(Wrappers.lambdaQuery(TenantUserProfilePO.class)
                        .eq(TenantUserProfilePO::getUserId, dto.getUserId()));

        SysTenantInfoPO tenantInfoPO = tenantInfoManager.getTenantInfoFromCache(userPO.getTenantCode());

        //4、刷新token过期时间
        sysAdmUserService.refreshToken(dto.getUserId());

        return ResultModel.success(AdmUserConvert.buildAdmUserExtDTO(userPO, tenantInfoPO.getName(),userProfilePO));
    }
}
