package com.dl.basicservice.biz.manager.sys.role.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("角色ID")
public class RoleIdParamBO {

    @ApiModelProperty("角色ID")
    @NotNull
    private Long roleId;

}

