package com.dl.basicservice.web.controller.internal.adm.sys.tenant.convert;

import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserProfilePO;
import com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp.UserProfileDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.resp.TenantInfoDTO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-16 14:44
 */
public class InternalTenantConvert {

    public static void fillTenantDTO(SysTenantInfoPO input, TenantInfoDTO result) {
        if (Objects.isNull(input)) {
            return;
        }
        result.setId(input.getId());
        result.setTenantCode(input.getTenantCode());
        result.setName(input.getName());
        result.setLogoImg(input.getLogoImg());
        result.setCreateBy(input.getCreateBy());
        result.setCreateDt(input.getCreateDt());
        result.setModifyBy(input.getModifyBy());
        result.setModifyDt(input.getModifyDt());
        result.setStatus(input.getStatus());
        result.setIsProvider(input.getIsProvider());
        result.setIsWeWork(input.getIsWeWork());
        result.setAuthMode(input.getAuthMode());
        result.setSmallLogo(input.getSmallLogo());
        result.setTenantDomain(input.getTenantDomain());
        result.setPathPart(input.getPathPart());
        result.setTenantDesc(input.getTenantDesc());
        result.setPrivateKey(input.getPrivateKey());
        result.setProduceType(input.getProduceType());
        result.setVideoCallbackUrl(input.getVideoCallbackUrl());
        result.setIsTrial(input.getIsTrial());
        if (Objects.equals(Const.ZERO, input.getIsWeWork())) {
            result.setIsHideQrLogin(Const.ONE);
        } else {
            result.setIsHideQrLogin(input.getIsHideQrLogin());
        }
        //租户三级域名访问地址判断
        String tenantUrl = input.getTenantDomain().endsWith("/") ? input.getTenantDomain()+ "adm" : input.getTenantDomain() + "/adm";
        if(!input.getTenantDomain().contains(input.getTenantCode().toLowerCase())){
            tenantUrl = tenantUrl + "/login?agentId=" + input.getTenantCode();
        }
        result.setTenantUrl(tenantUrl);
        result.setDmProduceMode(input.getDmProduceMode());
        result.setSimulateLocalPreview(input.getSimulateLocalPreview());
    }

}
