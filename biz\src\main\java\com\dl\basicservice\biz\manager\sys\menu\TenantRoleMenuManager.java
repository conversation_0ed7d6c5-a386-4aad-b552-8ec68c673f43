package com.dl.basicservice.biz.manager.sys.menu;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.basicservice.biz.common.service.CommonService;
import com.dl.basicservice.biz.dal.sys.menu.po.TenantRoleMenuPO;
import com.dl.basicservice.biz.manager.sys.menu.bo.MinAppRoleMenuParamBO;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleIdsMenuParamBO;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleMenuParamBO;

public interface TenantRoleMenuManager extends IService<TenantRoleMenuPO>, CommonService {

    boolean hasMenu(Long roleId);

    /**
     * 保存角色权限信息
     *
     * @param bo
     */
    Boolean saveRoleMenu(RoleMenuParamBO bo);

    /**
     * 删除角色权限信息
     *
     * @param bo
     */
    Boolean delRoleMenu(RoleIdsMenuParamBO bo);

    /**
     * 新增小程序角色与菜单
     * @param bo
     * @return
     */
    String addMiniAppRoleMenu(MinAppRoleMenuParamBO bo);

    String editMiniAppRoleMenu(MinAppRoleMenuParamBO bo);

}
