package com.dl.basicservice.biz.common.enums;

/**
 * @ClassName WeComAppNameEnum
 * @Description 企业微信内应用名称枚举类
 * <AUTHOR>
 * @Date 2022/4/11 9:50
 * @Version 1.0
 **/
public enum WeComAppNameEnum {

    WEALTH_PARTNER("dl_wealthpartner", "财赢助攻手"),

    VIDEO_ASSISTANT("dl_videoassistant", "助你拍"),
    //    @Deprecated
    //    ADDRESS_BOOK("dl_wealthpartner", "通讯录"),
    @Deprecated CUSTOM_CONTACT("dl_wealthpartner", "客户联系");

    private String nameCode;

    private String desc;

    WeComAppNameEnum(String nameCode, String desc) {
        this.nameCode = nameCode;
        this.desc = desc;
    }

    public String getNameCode() {
        return nameCode;
    }

    public String getDesc() {
        return desc;
    }
}
