package com.dl.basicservice.biz.manager.sys.userbind;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.basicservice.biz.dal.sys.userbind.po.TenantAdmUserBindPO;
import com.dl.basicservice.biz.manager.sys.userbind.bo.TenantAdmUserBindBO;
import com.dl.basicservice.biz.manager.sys.userbind.dto.TenantAdmUserBindDTO;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-01 17:23
 */
public interface TenantAdmUserBindManager extends IService<TenantAdmUserBindPO> {

    /**
     * 绑定
     *
     * @param bo
     * @return
     */
    void bind(TenantAdmUserBindBO bo);

    /**
     * 绑定
     *
     * @param bo
     * @return
     */
    void doBind(TenantAdmUserBindBO bo);

    /**
     * 根据外部用户id查询绑定信息
     *
     * @param tenantCode
     * @param extUserId
     * @return
     */
    TenantAdmUserBindDTO queryByExtUserId(String tenantCode,String extUserId);

    /**
     * 根据用户id查询绑定信息
     *
     * @param tenantCode
     * @param userId
     * @return
     */
    TenantAdmUserBindDTO queryByUserId(String tenantCode,Long userId);

    /**
     * 根据用户id集合查询外部用户id
     *
     * @param tenantCode
     * @param userIds
     * @return key-userId value-extUserId
     */
    Map<Long,String> queryByUserIds(String tenantCode, Set<Long> userIds);
}
