package com.dl.basicservice.web.controller.adm.auth;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.json.JSONUtil;
import com.dl.basicservice.biz.common.annotation.NotLogin;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.util.IPUtils;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.manager.sys.oplog.OpLogManager;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogAddBO;
import com.dl.basicservice.biz.manager.sys.oplog.consts.OpLogConsts;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.bo.LoginParamBO;
import com.dl.basicservice.biz.manager.sys.user.bo.UpdatePwdParamBO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysBasicUserDTO;
import com.dl.basicservice.web.controller.adm.base.AbstractController;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import io.jsonwebtoken.MalformedJwtException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import static com.dl.basicservice.biz.common.constant.Const.DEFAULT_SYSTEM_CODE;
import static com.dl.basicservice.biz.common.constant.Const.SYSTEM_CODE;

@Slf4j
@RestController
@RequestMapping("/auth")
@Api("登入登出")
public class AuthController extends AbstractController {

    static SM4 SM4 = SmUtil.sm4(Const.SM4_INIT.getBytes());

    @Autowired
    private HttpServletResponse response;

    @Autowired
    private SysAdmUserService sysAdmUserService;

    @Resource
    private OpLogManager opLogManager;

    private static String generateState(String tenantCode) {
        String text = tenantCode + "#" + System.currentTimeMillis();
        return SM4.encryptHex(text);
    }

    private static boolean validateState(String state, String tenantCode) {
        String text = SM4.decryptStr(state);
        return text.startsWith(tenantCode);
    }

    @PostMapping("/login")
    @ApiOperation("账号密码登录")
    @NotLogin
    public ResultModel<SysBasicUserDTO> login(@Validated @RequestBody LoginParamBO paramBO,
            HttpServletRequest request) {
        TenantAdmUserPO po = new TenantAdmUserPO();
        po.setAccount(paramBO.getAccount().trim());
        po.setPassword(paramBO.getPassword().trim());
        po.setTenantCode(paramBO.getTenantCode());
        SysBasicUserDTO dto = sysAdmUserService.login(po);
        if (dto == null) {
            throw BusinessServiceException.getInstance("密码错误请重新输入");
        }
        //保存登录的日志
        this.saveLoginOpLog(dto.getUserId(),getSystemCode(request),dto.getTenantCode(), IPUtils.getIp(request));

        dto.setUserId(-1L);
        return ResultModel.success(dto);
    }

    @PostMapping("/logout")
    @ApiOperation("登出")
    public ResultModel<String> logout(@RequestHeader(Const.TOKEN_HEADER_NAME) String token) {
        log.info("删除用户的token" + token);
        SysBasicUserDTO dto;
        try {
            dto = sysAdmUserService.parseJwtToken(token);
            sysAdmUserService.logoutJwtToken(token);
        } catch (MalformedJwtException e) {
            log.warn("JWT token is illegal");
            throw BusinessServiceException.getInstance("token illegal");
        }
        log.info("用户" + "id为" + dto.getUserId() + ">>>>>>>>>>>>>>>>>>>已登出");
        return ResultModel.success("成功退出");
    }

    /**
     * 修改当前账号的密码
     *
     * @param bo
     * @return
     */
    @PostMapping("/updpwd")
    @ApiOperation("用户密码修改")
    public ResultModel updpwd(@RequestBody @Validated UpdatePwdParamBO bo) {
        //修改密码
        if (sysAdmUserService.updatePassword(bo)) {
            //退出登录
            sysAdmUserService.logoutJwtToken(getToken());
        }
        return ResultModel.success("修改密码成功");
    }

    /**
     * 保存登录的操作日志
     *
     * @param userId
     * @param systemCode
     * @param tenantCode
     * @param ip
     */
    private void saveLoginOpLog(Long userId, String systemCode, String tenantCode, String ip) {
        OpLogAddBO opLog = new OpLogAddBO();
        // 当前只记录数影登录的操作日志
        if (Const.MAGIC_SYSTEM_CODE.equals(systemCode)) {
            opLog.setBizCode(OpLogConsts.OP_LOG_MAGIC_BIZ_CODE);
        } else {
            return;
        }
        opLog.setBizCode(OpLogConsts.OP_LOG_MAGIC_BIZ_CODE);
        //查询用户信息
        SysAdmUserDTO sysAdmUserDTO = sysAdmUserService.findUserDetailFromCache(userId);

        //保存登录日志
        opLog.setTenantCode(tenantCode);
        opLog.setOpObject("user");
        opLog.setOpType("login");
        opLog.setOpKey(String.valueOf(userId));
        opLog.setOpUserId(String.valueOf(userId));
        opLog.setOpUserName(sysAdmUserDTO.getUserName());
        opLog.setOpDt(new Date());
        Map<String,String> extDataMap = new HashMap<>(4);
        extDataMap.put(OpLogConsts.IP,ip);
        opLog.setExtData(JSONUtil.toJsonStr(extDataMap));
        try{
            opLogManager.newOpLog(opLog);
        }catch (Exception e){
            //抛异常就吃掉，不能影响主流程
            log.error("保存日志发生异常,opLog:{},,,e:{}",JSONUtil.toJsonStr(opLog),e);
        }
    }

    public String getSystemCode(HttpServletRequest request) {
        String systemCode = request.getHeader(SYSTEM_CODE);
        if(StringUtils.isBlank(systemCode)){
            systemCode = DEFAULT_SYSTEM_CODE;
        }
        return systemCode;
    }
}
