package com.dl.basicservice.biz.common.util;

import java.math.BigDecimal;

/**
 * @describe: AmountUtil
 * @author: zhousx
 * @date: 2022/5/21 10:38
 */
public class AmountUtil {
    /**
     * 功能描述: 分转元
     * @Param: [amount]
     * @Return: java.lang.String
     * @Author: zhousx
     * @Date: 2022/5/21 10:39
     */
    public static Double changeF2Y(Long amount){
        return new BigDecimal(amount).divide(new BigDecimal(100)).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
}
