package com.dl.basicservice.biz.manager.sys.oplog;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.basicservice.biz.dal.sys.oplog.po.OpLogPO;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogAddBO;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogPageBO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 14:43
 */
public interface OpLogManager extends IService<OpLogPO> {

    /**
     * 新增操作日志
     *
     * @param addBO
     */
    Long newOpLog(OpLogAddBO addBO);

    /**
     * 分页查询操作日志
     *
     * @param pageBO
     * @return
     */
    Page<OpLogPO> page(OpLogPageBO pageBO);
}
