package com.dl.basicservice.biz.manager.sys.userbind.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.dal.sys.userbind.TenantAdmUserBindMapper;
import com.dl.basicservice.biz.dal.sys.userbind.po.TenantAdmUserBindPO;
import com.dl.basicservice.biz.manager.sys.userbind.TenantAdmUserBindManager;
import com.dl.basicservice.biz.manager.sys.userbind.bo.TenantAdmUserBindBO;
import com.dl.basicservice.biz.manager.sys.userbind.dto.TenantAdmUserBindDTO;
import com.dl.basicservice.biz.manager.sys.userbind.helper.TenantAdmUserBindHelper;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-01 17:23
 */
@Component
public class TenantAdmUserBindManagerImpl extends ServiceImpl<TenantAdmUserBindMapper, TenantAdmUserBindPO>
        implements TenantAdmUserBindManager {

    @Override
    public void bind(TenantAdmUserBindBO bo) {
        //校验
        TenantAdmUserBindPO exisitByExtUserId = this.getOne(Wrappers.lambdaQuery(TenantAdmUserBindPO.class)
                .eq(TenantAdmUserBindPO::getTenantCode, bo.getTenantCode())
                .eq(TenantAdmUserBindPO::getExtUserId, bo.getExtUserId())
                .eq(TenantAdmUserBindPO::getIsDeleted, Const.ZERO));
        if (Objects.nonNull(exisitByExtUserId)) {
            if (exisitByExtUserId.getUserId().equals(bo.getUserId())) {
                return;
            }
            throw BusinessServiceException.getInstance("该外部用户已绑定其他用户");
        }

        TenantAdmUserBindPO exisitByUserId = this.getOne(Wrappers.lambdaQuery(TenantAdmUserBindPO.class)
                .eq(TenantAdmUserBindPO::getTenantCode, bo.getTenantCode())
                .eq(TenantAdmUserBindPO::getUserId, bo.getUserId()).eq(TenantAdmUserBindPO::getIsDeleted, Const.ZERO));
        if (Objects.nonNull(exisitByUserId)) {
            if (exisitByUserId.getExtUserId().equals(bo.getExtUserId())) {
                return;
            }
            throw BusinessServiceException.getInstance("该用户已绑定其他外部用户");
        }

        this.doBind(bo);
    }

    @Override
    public void doBind(TenantAdmUserBindBO bo) {
        TenantAdmUserBindPO insertPO = new TenantAdmUserBindPO();
        insertPO.setUserId(bo.getUserId());
        insertPO.setTenantCode(bo.getTenantCode());
        insertPO.setExtUserId(bo.getExtUserId());
        this.save(insertPO);
    }

    @Override
    public TenantAdmUserBindDTO queryByExtUserId(String tenantCode, String extUserId) {

        TenantAdmUserBindPO po = this.getOne(Wrappers.lambdaQuery(TenantAdmUserBindPO.class)
                .eq(TenantAdmUserBindPO::getTenantCode, tenantCode).eq(TenantAdmUserBindPO::getExtUserId, extUserId)
                .eq(TenantAdmUserBindPO::getIsDeleted, Const.ZERO));
        return TenantAdmUserBindHelper.cnvTenantAdmUserBindPO2DTO(po);
    }

    @Override
    public TenantAdmUserBindDTO queryByUserId(String tenantCode, Long userId) {
        TenantAdmUserBindPO po = this.getOne(Wrappers.lambdaQuery(TenantAdmUserBindPO.class)
                .eq(TenantAdmUserBindPO::getTenantCode, tenantCode).eq(TenantAdmUserBindPO::getUserId, userId)
                .eq(TenantAdmUserBindPO::getIsDeleted, Const.ZERO));
        return TenantAdmUserBindHelper.cnvTenantAdmUserBindPO2DTO(po);
    }

    @Override
    public Map<Long, String> queryByUserIds(String tenantCode, Set<Long> userIds) {
        List<TenantAdmUserBindPO> poList = this
                .list(Wrappers.lambdaQuery(TenantAdmUserBindPO.class).eq(TenantAdmUserBindPO::getTenantCode, tenantCode)
                        .in(TenantAdmUserBindPO::getUserId, userIds).eq(TenantAdmUserBindPO::getIsDeleted, Const.ZERO));
        //key-userId
        Map<Long, TenantAdmUserBindPO> poMap = poList.stream()
                .collect(Collectors.toMap(TenantAdmUserBindPO::getUserId, Function.identity(), (u1, u2) -> u1));
        //key-userId,value-extUserId
        Map<Long, String> resultMap = new HashMap<>();
        userIds.forEach(userId -> {
            TenantAdmUserBindPO bindPO = poMap.get(userId);
            if (Objects.isNull(bindPO)) {
                resultMap.put(userId, null);
            } else {
                resultMap.put(userId, bindPO.getExtUserId());
            }
        });
        return resultMap;
    }
}
