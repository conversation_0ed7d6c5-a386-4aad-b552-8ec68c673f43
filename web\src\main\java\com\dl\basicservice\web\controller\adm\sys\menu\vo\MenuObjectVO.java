package com.dl.basicservice.web.controller.adm.sys.menu.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("系统菜单信息")
public class MenuObjectVO {

    @ApiModelProperty("菜单列表")
    private List<MenuVO> all = new ArrayList<>();

    @ApiModelProperty("已分配权限的菜单列表")
    private List<String> owners = new ArrayList<>();
}
