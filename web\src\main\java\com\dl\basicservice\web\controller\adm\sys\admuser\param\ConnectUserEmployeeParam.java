package com.dl.basicservice.web.controller.adm.sys.admuser.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("通讯录员工-后台账号关联信息")
public class ConnectUserEmployeeParam {

    @NotNull
    @ApiModelProperty("后台账号id")
    Long userId;

    @NotNull @Size(min = 1, max = 100)
    @ApiModelProperty("通讯录员工企微账号")
    String wxUserId;

}
