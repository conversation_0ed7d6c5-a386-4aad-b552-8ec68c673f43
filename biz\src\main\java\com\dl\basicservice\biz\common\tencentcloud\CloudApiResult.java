package com.dl.basicservice.biz.common.tencentcloud;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.Data;

/**
 * 腾讯云 java sdk返回结果
 */
@Data
public class CloudApiResult {

    private int code;

    private String message;

    private String codeDesc;

    private JSONObject data;

    public static CloudApiResult result(String jsonStr) {
        CloudApiResult result = new CloudApiResult();
        JSONObject jsonObject = JSONUtil.parseObj(jsonStr);
        Integer code = jsonObject.get("code", Integer.class);
        String codeDesc = jsonObject.get("codeDesc", String.class);
        result.setCode(code);
        result.setCodeDesc(codeDesc);
        if (code == 0) {
            result.setData(jsonObject.getJSONObject("data"));
        }
        return result;
    }
}
