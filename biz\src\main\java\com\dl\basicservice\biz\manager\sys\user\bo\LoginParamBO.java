package com.dl.basicservice.biz.manager.sys.user.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;

@ApiModel
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginParamBO {
    @ApiModelProperty("账号")
    @NotBlank(message = "账号不能为空")
    @Length(min = 4, max = 36, message = "账号长度必须在4-36位之间")
    private String account;

    @Length(min = 4, max = 12, message = "密码长度必须在4-12位之间")
    @ApiModelProperty("密码")
    private String password;

    @Length(min = 1, max = 50, message = "租户id不能为空")
    @ApiModelProperty("租户id")
    private String tenantCode;

}
