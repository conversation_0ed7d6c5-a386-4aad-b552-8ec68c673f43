package com.dl.basicservice.biz.manager.mino.dto;

import com.dl.basicservice.biz.common.BaseDTO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MinioTempCredentialDTO extends BaseDTO {

    /**
     * minio服务器ip
     */
    private String minioHost;

    /**
     * minio服务器port
     */
    private Integer minioPort;

    String accessKey;

    String secretKey;

    String schemePrefix;

    boolean useSSL;

    String bucketId;

    String sessionToken;
}
