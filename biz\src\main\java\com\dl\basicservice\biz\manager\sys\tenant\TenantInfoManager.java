package com.dl.basicservice.biz.manager.sys.tenant;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dl.basicservice.biz.common.service.CommonService;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;

/**
 * @ClassName ISysTenantInfoService
 * @Description
 * <AUTHOR>
 * @Date 2022/4/9 11:06
 * @Version 1.0
 **/
public interface TenantInfoManager extends IService<SysTenantInfoPO>, CommonService {

    /**
     * 从缓存中查询租户信息
     *
     * @param tenantCode
     * @return
     */
    SysTenantInfoPO getTenantInfoFromCache(String tenantCode);

    /**
     * 刷新单个租户的缓存
     *
     * @param tenantCode
     */
    void refreshSingleTenantCache(String tenantCode);
}
