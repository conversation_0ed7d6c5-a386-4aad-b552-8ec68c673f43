package com.dl.basicservice.web.controller.adm.sys.admuser.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-05 16:17
 */
@Data
@ApiModel("修改员工个人信息")
public class UpdateSelfInfoParam {
    @ApiModelProperty("账号")
    @Size(min = 4, max = 32, message = "账号长度必须在4-36位之间")
    private String account;

    @ApiModelProperty("手机号")
    @Size(min = 0, max = 30, message = "手机号最长不超过30位")
    private String mobile;

    @ApiModelProperty("姓名")
    @Size(min = 0, max = 64, message = "姓名长度最长64位")
    private String userName;

    /**
     * 1-男 2-女
     */
    @ApiModelProperty("性别 1-男 2-女")
    private String gender;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String thumbAvatar;


}
