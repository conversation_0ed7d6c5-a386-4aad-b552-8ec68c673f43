package com.dl.basicservice.biz.common.service;

import cn.easyes.core.biz.PageInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.framework.common.bo.PageQueryDO;
import com.dl.framework.common.bo.ResponsePageQueryDO;
import com.dl.framework.core.controller.param.AbstractPageParam;

import java.util.List;

public interface CommonService {

    /**
     * PageQueryDO请求参数转换成mybatis page
     *
     * @param queryDO
     * @param <T>
     * @return
     */
    default <T> Page<T> convert(PageQueryDO queryDO) {
        return new Page<>(queryDO.getPageIndex(), queryDO.getPageSize());
    }

    /**
     * AbstractPageParam请求参数转换成mybatis page
     *
     * @param param
     * @param <T>
     * @return
     */
    default <T> Page<T> convert(AbstractPageParam param) {
        return new Page<>(param.getPageIndex(), param.getPageSize());
    }

    default <T> Page<T> convertEsPage(PageInfo<T> pageInfo) {
        Page page = new Page<>(pageInfo.getPageNum(), pageInfo.getPageSize());
        page.setTotal(pageInfo.getTotal());
        page.setRecords(pageInfo.getList());
        return page;
    }

    default <T> ResponsePageQueryDO<List<T>> convert(IPage<T> page) {
        ResponsePageQueryDO<List<T>> qd = new ResponsePageQueryDO<>();
        qd.setPageIndex(page.getCurrent());
        qd.setPageSize(page.getSize());
        qd.setTotal(page.getTotal());
        qd.setDataResult(page.getRecords());
        return qd;
    }
}
