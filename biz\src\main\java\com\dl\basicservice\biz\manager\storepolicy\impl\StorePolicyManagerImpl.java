package com.dl.basicservice.biz.manager.storepolicy.impl;

import com.dl.basicservice.biz.common.tencentcloud.ApiManager;
import com.dl.basicservice.biz.common.tencentcloud.cos.CosGetTempCredentialBO;
import com.dl.basicservice.biz.common.tencentcloud.cos.CosTempCredentialDTO;
import com.dl.basicservice.biz.common.util.OperatorUtil;
import com.dl.basicservice.biz.manager.mino.MinioApiManager;
import com.dl.basicservice.biz.manager.mino.dto.MinioTempCredentialDTO;
import com.dl.basicservice.biz.manager.storepolicy.StorePolicyManager;
import com.dl.basicservice.biz.manager.storepolicy.dto.BaseTempCredentialDTO;
import com.dl.basicservice.biz.manager.storepolicy.dto.CosResultDTO;
import com.dl.basicservice.biz.manager.storepolicy.dto.MinioResultDTO;
import com.dl.basicservice.biz.manager.storepolicy.enums.StorePolicyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName StorePolicyManagerImpl
 * @Description 存储策略类，根据storePolicy确定使用腾讯云存储还是其他存储
 * <AUTHOR>
 * @Date 2022/10/10 9:16
 * @Version 1.0
 **/
@Slf4j
@Service
public class StorePolicyManagerImpl implements StorePolicyManager {

    @Resource
    private ApiManager apiManager;
    @Value("${dl.store.policy:}")
    private String storePolicy;
    @Value("${objectStorage.cos.resourcePrefix}")
    private String cosResourcePrefix;

    @Value("${dl.minio.assignTenantCode:}")
    private String assignTenantCode;
    @Resource
    private OperatorUtil operatorUtil;
    @Resource
    private MinioApiManager minioApiManager;


    @Override
    public BaseTempCredentialDTO getTempCredential(CosGetTempCredentialBO param) {
        if (isDefaultStorage()) {
            CosResultDTO cos = convert(apiManager.getCosTempCredential(param));
            cos.setStorage(this.getStorePolicy());
            cos.setResourcePathPrefix(cosResourcePrefix);
            return cos;
        }
        MinioResultDTO minio = convert(minioApiManager.getTempCredential());
        minio.setStorage(this.getStorePolicy());
        return minio;
    }

    public String getStorePolicy() {
        if (StringUtils.equals(StorePolicyEnum.MINIO.getCode(), storePolicy) && StringUtils.equals(assignTenantCode,
                operatorUtil.getOpTenantCode())) {
            return StorePolicyEnum.MINIO.getCode();
        }
        return StorePolicyEnum.DEFAULT.getCode();
    }

    private CosResultDTO convert(CosTempCredentialDTO source) {
        CosResultDTO cosResultDTO = new CosResultDTO();
        cosResultDTO.setTmpSecretId(source.getTmpSecretId());
        cosResultDTO.setTmpSecretKey(source.getTmpSecretKey());
        cosResultDTO.setSessionToken(source.getSessionToken());
        cosResultDTO.setStartTime(source.getStartTime());
        cosResultDTO.setExpiredTime(source.getExpiredTime());
        cosResultDTO.setBucketId(source.getBucketId());
        cosResultDTO.setRegion(source.getRegion());
        return cosResultDTO;
    }

    private MinioResultDTO convert(MinioTempCredentialDTO source) {
        MinioResultDTO minioResultDTO = new MinioResultDTO();
        minioResultDTO.setMinioHost(source.getMinioHost());
        minioResultDTO.setMinioPort(source.getMinioPort());
        minioResultDTO.setAccessKey(source.getAccessKey());
        minioResultDTO.setSecretKey(source.getSecretKey());
        minioResultDTO.setSchemePrefix(source.getSchemePrefix());
        minioResultDTO.setUseSSL(source.isUseSSL());
        minioResultDTO.setBucketId(source.getBucketId());
        minioResultDTO.setSessionToken(source.getSessionToken());
        return minioResultDTO;
    }

    private boolean isDefaultStorage() {
        return StringUtils.equals(getStorePolicy(), StorePolicyEnum.DEFAULT.getCode());
    }
}
