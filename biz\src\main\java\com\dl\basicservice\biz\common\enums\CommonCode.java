package com.dl.basicservice.biz.common.enums;

import static com.dl.basicservice.biz.common.enums.ResultCode.COMMON;

/**
 * 通用响应码
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-07-05 15:04
 */
public enum CommonCode {

    // 通用响应码
    INVALID_ARGS("1", COMMON, "参数无效"),
    DATA_NOT_FOUND("2", COMMON, "当前数据不存在"),
    NO_LOGIN("3", COMMON, "用户未登录"),
    INVALID_AUTH("4", COMMON, "权限无效"),
    RECORD_EXISTED("5", COMMON, "已经存在该记录"),
    ILLEGAL_STATE("6", COMMON, "无效的状态"),
    /**
     * 用于http中，比如服务端未实现POST，但客户端对这个接口发起了POST请求后会报告此类错误
     */
    METHOD_NOT_ALLOWED("7", COMMON, "不支持的请求方法");

    /**
     * 响应码
     */
    private final String code;

    /**
     * 响应消息
     */
    private final String message;

    /**
     * 构造函数
     *
     * @param code     响应码
     * @param codeSign 响应码标识
     * @param message  消息
     */
    CommonCode(String code, String codeSign, String message) {
        this.code = codeSign + "_" + code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
