package com.dl.basicservice.biz.manager.sys.menu.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("菜单搜索")
public class SysMenuSearchParamBO {

    @ApiModelProperty("父级菜单ID")
    @Size(min = 0)
    private Long parentId;

    @ApiModelProperty(hidden = true)
    private Set<Long> roleIds;

    @ApiModelProperty(hidden = true)
    private String tenantCode;

}
