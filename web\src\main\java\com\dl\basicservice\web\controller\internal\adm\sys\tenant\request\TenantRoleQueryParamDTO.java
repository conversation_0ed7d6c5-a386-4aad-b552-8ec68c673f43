package com.dl.basicservice.web.controller.internal.adm.sys.tenant.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 09:30
 */
@Data
public class TenantRoleQueryParamDTO implements Serializable {
    private static final long serialVersionUID = 7962072990254010621L;

    @NotEmpty(message = "角色id列表")
    private List<Long> roleIds;

    @NotBlank(message = "租户号")
    private String tenantCode;

}
