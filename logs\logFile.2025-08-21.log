[32m2025-08-21 11:55:13.468[0;39m [background-preinit] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m [TID: N/A]  HV000001: Hibernate Validator 6.1.7.Final
[32m2025-08-21 11:55:13.495[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mcom.dl.basicservice.war.Application     [0;39m [2m:[0;39m [TID: N/A]  Starting Application on DESKTOP-72KCE9C with PID 26260 (C:\Users\<USER>\IdeaProjects\gitee\szr\dl-basic-service\war\target\classes started by 86150 in C:\Users\<USER>\IdeaProjects\gitee\szr\dl-basic-service)
[32m2025-08-21 11:55:13.496[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mcom.dl.basicservice.war.Application     [0;39m [2m:[0;39m [TID: N/A]  Running with Spring Boot v2.3.12.RELEASE, Spring v5.2.15.RELEASE
[32m2025-08-21 11:55:13.497[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mcom.dl.basicservice.war.Application     [0;39m [2m:[0;39m [TID: N/A]  The following profiles are active: prod
[32m2025-08-21 11:55:17.940[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36morg.apache.ibatis.logging.LogFactory    [0;39m [2m:[0;39m [TID: N/A]  Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
[32m2025-08-21 11:55:18.109[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mptablePropertiesBeanFactoryPostProcessor[0;39m [2m:[0;39m [TID: N/A]  Post-processing PropertySource instances
[32m2025-08-21 11:55:18.198[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[32m2025-08-21 11:55:18.199[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 11:55:18.199[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 11:55:18.199[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 11:55:18.200[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[32m2025-08-21 11:55:18.201[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 11:55:18.201[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/application.yaml] (document #1) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 11:55:18.201[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/config/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 11:55:18.201[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/application.yaml] (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 11:55:18.201[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource moduleDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 11:55:18.272[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m [2m:[0;39m [TID: N/A]  Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[33m2025-08-21 11:55:19.040[0;39m [main] [33m WARN[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.f.scanner.ClassPathClientScanner    [0;39m [2m:[0;39m [TID: N/A]  [Forest] No Forest client is found in package '[com.dl.basicservice.war]'.
[32m2025-08-21 11:55:19.778[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m [TID: N/A]  Initializing ProtocolHandler ["http-nio-8080"]
[32m2025-08-21 11:55:19.779[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m [TID: N/A]  Starting service [Tomcat]
[32m2025-08-21 11:55:19.780[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36morg.apache.catalina.core.StandardEngine [0;39m [2m:[0;39m [TID: N/A]  Starting Servlet engine: [Apache Tomcat/9.0.46]
[32m2025-08-21 11:55:20.118[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m [TID: N/A]  Initializing Spring embedded WebApplicationContext
[32m2025-08-21 11:55:20.712[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.f.common.idg.AbstractHostTimeIdg    [0;39m [2m:[0;39m [TID: N/A]  从网卡中获取HostId。serviceNo:1,IP:*************, HostId:390
[32m2025-08-21 11:55:20.712[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.f.common.idg.AbstractHostTimeIdg    [0;39m [2m:[0;39m [TID: N/A]  初始化Id生成器。HostId:390,ShiftHostId:1756403854674493440
[32m2025-08-21 11:55:20.712[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.f.common.idg.AbstractHostTimeIdg    [0;39m [2m:[0;39m [TID: N/A]  时间底线TimeBaseLine:0，时间跨度TimeGap:1000
[32m2025-08-21 11:55:20.713[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.f.common.idg.AbstractHostTimeIdg    [0;39m [2m:[0;39m [TID: N/A]  设置随机IncNo成功。IncNo:true
[32m2025-08-21 11:55:20.774[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.r.DefaultLazyPropertyResolver     [0;39m [2m:[0;39m [TID: N/A]  Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[32m2025-08-21 11:55:20.777[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mc.u.j.d.DefaultLazyPropertyDetector     [0;39m [2m:[0;39m [TID: N/A]  Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[32m2025-08-21 11:55:21.221[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m [TID: N/A]  Redisson 3.16.0
[32m2025-08-21 11:55:37.797[0;39m [redisson-netty-2-16] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mo.r.c.pool.MasterPubSubConnectionPool   [0;39m [2m:[0;39m [TID: N/A]  1 connections initialized for r-bp1rgz6nqi3c07bxfnpd.redis.rds.aliyuncs.com/************:6379
[32m2025-08-21 11:55:37.887[0;39m [redisson-netty-2-20] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mo.r.c.pool.MasterConnectionPool         [0;39m [2m:[0;39m [TID: N/A]  24 connections initialized for r-bp1rgz6nqi3c07bxfnpd.redis.rds.aliyuncs.com/************:6379
[32m2025-08-21 11:55:42.917[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mpertySourcedRequestMappingHandlerMapping[0;39m [2m:[0;39m [TID: N/A]  Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[32m2025-08-21 11:55:44.416[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m [TID: N/A]  Starting ProtocolHandler ["http-nio-8080"]
[32m2025-08-21 11:55:44.445[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36md.s.w.p.DocumentationPluginsBootstrapper[0;39m [2m:[0;39m [TID: N/A]  Context refreshed
[32m2025-08-21 11:55:44.475[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36md.s.w.p.DocumentationPluginsBootstrapper[0;39m [2m:[0;39m [TID: N/A]  Found 1 custom documentation plugin(s)
[32m2025-08-21 11:55:44.533[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36ms.d.s.w.s.ApiListingReferenceScanner    [0;39m [2m:[0;39m [TID: N/A]  Scanning for api listing references
[32m2025-08-21 11:55:44.782[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: pageUsingPOST_1
[32m2025-08-21 11:55:44.826[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: saveTenantMenuUsingPOST_1
[32m2025-08-21 11:55:44.841[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: addUsingPOST_1
[32m2025-08-21 11:55:44.843[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: listUsingPOST_1
[32m2025-08-21 11:55:44.846[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: updateUsingPOST_1
[32m2025-08-21 11:55:44.851[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: addTenantUsingPOST_1
[32m2025-08-21 11:55:44.854[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: delUsingPOST_1
[32m2025-08-21 11:55:44.862[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: pageTenantUsingPOST_1
[32m2025-08-21 11:55:44.865[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: tenantInfoForNoLoginUsingPOST_1
[32m2025-08-21 11:55:44.866[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: updateStatusUsingPOST_1
[32m2025-08-21 11:55:44.869[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: updateTenantUsingPOST_1
[32m2025-08-21 11:55:44.870[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: updateTrialUsingPOST_1
[32m2025-08-21 11:55:44.894[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mcom.dl.basicservice.war.Application     [0;39m [2m:[0;39m [TID: N/A]  Started Application in 32.428 seconds (JVM running for 35.919)
[32m2025-08-21 11:55:45.075[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m [TID: N/A]  HikariPool-1 - Starting...
[32m2025-08-21 11:55:45.332[0;39m [main] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m [TID: N/A]  HikariPool-1 - Start completed.
[32m2025-08-21 11:55:45.343[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.t.S.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,name,tenant_code,status,logo_img,tenant_domain,path_part,is_provider,is_hide_qr_login,is_we_work,tenant_desc,auth_mode,private_key,is_deleted,produce_type,video_callback_url,small_logo,is_trial,dm_produce_mode,simulate_local_preview,create_dt,create_by,modify_dt,modify_by FROM sys_tenant_info
[32m2025-08-21 11:55:45.364[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.t.S.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 11:55:45.399[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.t.S.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 1
[32m2025-08-21 11:55:45.501[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.P.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,menu_id,parent_id,name,icon,url,menu_level,sort,disable,system_code,scene_type,create_dt,create_by,modify_dt,modify_by FROM sys_privilege_menu WHERE (disable = ?)
[32m2025-08-21 11:55:45.502[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.P.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 0(Integer)
[32m2025-08-21 11:55:45.523[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.P.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 26
[32m2025-08-21 11:55:45.583[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.listFunctions           [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT m.tenant_code AS tenantCode,f.function_id AS functionId,f.function_code AS functionCode,f.name,f.icon,f.sort ,f.system_code FROM sys_tenant_function m,sys_privilege_function f WHERE m.function_id = f.function_id
[32m2025-08-21 11:55:45.584[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.listFunctions           [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 11:55:45.608[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.listFunctions           [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 0
[32m2025-08-21 11:55:45.610[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.u.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,user_id,role_id,system_code,create_dt,create_by,modify_dt,modify_by FROM sys_tenant_user_role
[32m2025-08-21 11:55:45.610[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.u.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 11:55:45.634[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.u.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 0
[32m2025-08-21 11:55:45.642[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,role_id,menu_id,system_code,create_dt,create_by,modify_dt,modify_by FROM sys_tenant_role_menu
[32m2025-08-21 11:55:45.642[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 11:55:45.657[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 0
[32m2025-08-21 11:55:45.659[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,role_id,function_id,system_code,create_dt,create_by,modify_dt,modify_by FROM sys_tenant_role_function
[32m2025-08-21 11:55:45.659[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 11:55:45.671[0;39m [main] [32mDEBUG[0;39m [35m26260[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 0
[32m2025-08-21 11:55:46.152[0;39m [RMI TCP Connection(1)-*************] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m [TID: N/A]  Initializing Spring DispatcherServlet 'dispatcherServlet'
[32m2025-08-21 11:56:06.504[0;39m [SpringContextShutdownHook] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m [TID: N/A]  HikariPool-1 - Shutdown initiated...
[32m2025-08-21 11:56:06.510[0;39m [SpringContextShutdownHook] [32m INFO[0;39m [35m26260[0;39m [2m---[0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m [TID: N/A]  HikariPool-1 - Shutdown completed.
[32m2025-08-21 14:02:05.735[0;39m [background-preinit] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m [TID: N/A]  HV000001: Hibernate Validator 6.1.7.Final
[32m2025-08-21 14:02:06.513[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mptablePropertiesBeanFactoryPostProcessor[0;39m [2m:[0;39m [TID: N/A]  Post-processing PropertySource instances
[32m2025-08-21 14:02:06.664[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[32m2025-08-21 14:02:06.666[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:02:06.666[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:02:06.668[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[32m2025-08-21 14:02:06.670[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:02:06.670[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource moduleDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:02:06.864[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m [2m:[0;39m [TID: N/A]  Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[32m2025-08-21 14:02:06.967[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.r.DefaultLazyPropertyResolver     [0;39m [2m:[0;39m [TID: N/A]  Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[32m2025-08-21 14:02:06.970[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.d.DefaultLazyPropertyDetector     [0;39m [2m:[0;39m [TID: N/A]  Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[32m2025-08-21 14:02:07.238[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mcom.dl.basicservice.war.Application     [0;39m [2m:[0;39m [TID: N/A]  The following profiles are active: prod
[32m2025-08-21 14:02:12.434[0;39m [main] [32mDEBUG[0;39m [35m20820[0;39m [2m---[0;39m [36morg.apache.ibatis.logging.LogFactory    [0;39m [2m:[0;39m [TID: N/A]  Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
[33m2025-08-21 14:02:12.436[0;39m [main] [33m WARN[0;39m [35m20820[0;39m [2m---[0;39m [36mo.m.s.mapper.ClassPathMapperScanner     [0;39m [2m:[0;39m [TID: N/A]  No MyBatis mapper was found in '[com.dl.basicservice.biz.dal]' package. Please check your configuration.
[32m2025-08-21 14:02:12.459[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mptablePropertiesBeanFactoryPostProcessor[0;39m [2m:[0;39m [TID: N/A]  Post-processing PropertySource instances
[32m2025-08-21 14:02:12.466[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[32m2025-08-21 14:02:12.467[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:02:12.467[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:02:12.467[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:02:12.467[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[32m2025-08-21 14:02:12.468[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:02:12.468[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/application.yaml] (document #1) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:02:12.468[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/config/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:02:12.468[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/application.yaml] (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:02:12.468[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:02:12.468[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:02:12.468[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource moduleDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:02:12.482[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m [2m:[0;39m [TID: N/A]  Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[33m2025-08-21 14:02:13.024[0;39m [main] [33m WARN[0;39m [35m20820[0;39m [2m---[0;39m [36mc.d.f.scanner.ClassPathClientScanner    [0;39m [2m:[0;39m [TID: N/A]  [Forest] No Forest client is found in package '[com.dl.basicservice.war]'.
[32m2025-08-21 14:02:13.561[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m [TID: N/A]  Initializing ProtocolHandler ["http-nio-8080"]
[32m2025-08-21 14:02:13.562[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m [TID: N/A]  Starting service [Tomcat]
[32m2025-08-21 14:02:13.562[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36morg.apache.catalina.core.StandardEngine [0;39m [2m:[0;39m [TID: N/A]  Starting Servlet engine: [Apache Tomcat/9.0.46]
[32m2025-08-21 14:02:13.850[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m [TID: N/A]  Initializing Spring embedded WebApplicationContext
[31m2025-08-21 14:02:13.869[0;39m [main] [31mERROR[0;39m [35m20820[0;39m [2m---[0;39m [36mo.s.b.web.embedded.tomcat.TomcatStarter [0;39m [2m:[0;39m [TID: N/A]  Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'webConfig': Unsatisfied dependency expressed through field 'authenticationInterceptor'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'authenticationInterceptor': Lookup method resolution failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.dl.basicservice.web.controller.adm.interceptor.AuthenticationInterceptor] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
[32m2025-08-21 14:02:13.912[0;39m [main] [32m INFO[0;39m [35m20820[0;39m [2m---[0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m [TID: N/A]  Stopping service [Tomcat]
[31m2025-08-21 14:02:13.977[0;39m [main] [31mERROR[0;39m [35m20820[0;39m [2m---[0;39m [36mo.s.boot.SpringApplication              [0;39m [2m:[0;39m [TID: N/A]  Application run failed

org.springframework.context.ApplicationContextException: Unable to start web server; nested exception is org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:161)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:545)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:143)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:755)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:402)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:312)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.dl.basicservice.war.Application.main(Application.java:16)
Caused by: org.springframework.boot.web.server.WebServerException: Unable to start embedded Tomcat
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:142)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.<init>(TomcatWebServer.java:104)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getTomcatWebServer(TomcatServletWebServerFactory.java:440)
	at org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory.getWebServer(TomcatServletWebServerFactory.java:193)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.createWebServer(ServletWebServerApplicationContext.java:178)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.onRefresh(ServletWebServerApplicationContext.java:158)
	... 9 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'webConfig': Unsatisfied dependency expressed through field 'authenticationInterceptor'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'authenticationInterceptor': Lookup method resolution failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.dl.basicservice.web.controller.adm.interceptor.AuthenticationInterceptor] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:660)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1425)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:409)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1341)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1181)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:207)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:211)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.getOrderedBeansOfType(ServletContextInitializerBeans.java:202)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.addServletContextInitializerBeans(ServletContextInitializerBeans.java:96)
	at org.springframework.boot.web.servlet.ServletContextInitializerBeans.<init>(ServletContextInitializerBeans.java:85)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.getServletContextInitializerBeans(ServletWebServerApplicationContext.java:255)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.selfInitialize(ServletWebServerApplicationContext.java:229)
	at org.springframework.boot.web.embedded.tomcat.TomcatStarter.onStartup(TomcatStarter.java:53)
	at org.apache.catalina.core.StandardContext.startInternal(StandardContext.java:5161)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1384)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1374)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:909)
	at org.apache.catalina.core.StandardHost.startInternal(StandardHost.java:829)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1384)
	at org.apache.catalina.core.ContainerBase$StartChild.call(ContainerBase.java:1374)
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266)
	at java.util.concurrent.FutureTask.run(FutureTask.java)
	at org.apache.tomcat.util.threads.InlineExecutorService.execute(InlineExecutorService.java:75)
	at java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:134)
	at org.apache.catalina.core.ContainerBase.startInternal(ContainerBase.java:909)
	at org.apache.catalina.core.StandardEngine.startInternal(StandardEngine.java:262)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardService.startInternal(StandardService.java:433)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.core.StandardServer.startInternal(StandardServer.java:930)
	at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
	at org.apache.catalina.startup.Tomcat.start(Tomcat.java:486)
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.initialize(TomcatWebServer.java:123)
	... 14 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'authenticationInterceptor': Lookup method resolution failed; nested exception is java.lang.IllegalStateException: Failed to introspect Class [com.dl.basicservice.web.controller.adm.interceptor.AuthenticationInterceptor] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:289)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.determineConstructorsFromBeanPostProcessors(AbstractAutowireCapableBeanFactory.java:1290)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1205)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:556)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1307)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	... 66 common frames omitted
Caused by: java.lang.IllegalStateException: Failed to introspect Class [com.dl.basicservice.web.controller.adm.interceptor.AuthenticationInterceptor] from ClassLoader [sun.misc.Launcher$AppClassLoader@18b4aac2]
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:481)
	at org.springframework.util.ReflectionUtils.doWithLocalMethods(ReflectionUtils.java:321)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.determineCandidateConstructors(AutowiredAnnotationBeanPostProcessor.java:267)
	... 78 common frames omitted
Caused by: java.lang.NoClassDefFoundError: com/dl/basicservice/biz/common/annotation/Permission
	at java.lang.Class.getDeclaredMethods0(Native Method)
	at java.lang.Class.privateGetDeclaredMethods(Class.java:2729)
	at java.lang.Class.getDeclaredMethods(Class.java:2003)
	at org.springframework.util.ReflectionUtils.getDeclaredMethods(ReflectionUtils.java:463)
	... 80 common frames omitted
Caused by: java.lang.ClassNotFoundException: com.dl.basicservice.biz.common.annotation.Permission
	at java.net.URLClassLoader.findClass(URLClassLoader.java:387)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:418)
	at sun.misc.Launcher$AppClassLoader.loadClass(Launcher.java:352)
	at java.lang.ClassLoader.loadClass(ClassLoader.java:351)
	... 84 common frames omitted

[32m2025-08-21 14:04:11.373[0;39m [background-preinit] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m [TID: N/A]  HV000001: Hibernate Validator 6.1.7.Final
[32m2025-08-21 14:04:12.142[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mptablePropertiesBeanFactoryPostProcessor[0;39m [2m:[0;39m [TID: N/A]  Post-processing PropertySource instances
[32m2025-08-21 14:04:12.254[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[32m2025-08-21 14:04:12.254[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:04:12.254[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:04:12.255[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[32m2025-08-21 14:04:12.256[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:04:12.257[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource moduleDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:04:12.351[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m [2m:[0;39m [TID: N/A]  Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[32m2025-08-21 14:04:12.413[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.r.DefaultLazyPropertyResolver     [0;39m [2m:[0;39m [TID: N/A]  Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[32m2025-08-21 14:04:12.415[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.d.DefaultLazyPropertyDetector     [0;39m [2m:[0;39m [TID: N/A]  Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[32m2025-08-21 14:04:12.595[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mcom.dl.basicservice.war.Application     [0;39m [2m:[0;39m [TID: N/A]  The following profiles are active: prod
[32m2025-08-21 14:04:20.812[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36morg.apache.ibatis.logging.LogFactory    [0;39m [2m:[0;39m [TID: N/A]  Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
[32m2025-08-21 14:04:20.888[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mptablePropertiesBeanFactoryPostProcessor[0;39m [2m:[0;39m [TID: N/A]  Post-processing PropertySource instances
[32m2025-08-21 14:04:20.899[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[32m2025-08-21 14:04:20.900[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:04:20.900[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:04:20.900[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:04:20.900[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[32m2025-08-21 14:04:20.900[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:04:20.901[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/application.yaml] (document #1) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:04:20.901[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/config/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:04:20.901[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/application.yaml] (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:04:20.901[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:04:20.901[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-21 14:04:20.901[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource moduleDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-21 14:04:21.013[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m [2m:[0;39m [TID: N/A]  Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[33m2025-08-21 14:04:21.683[0;39m [main] [33m WARN[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.f.scanner.ClassPathClientScanner    [0;39m [2m:[0;39m [TID: N/A]  [Forest] No Forest client is found in package '[com.dl.basicservice.war]'.
[32m2025-08-21 14:04:22.472[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m [TID: N/A]  Initializing ProtocolHandler ["http-nio-8080"]
[32m2025-08-21 14:04:22.473[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m [TID: N/A]  Starting service [Tomcat]
[32m2025-08-21 14:04:22.473[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36morg.apache.catalina.core.StandardEngine [0;39m [2m:[0;39m [TID: N/A]  Starting Servlet engine: [Apache Tomcat/9.0.46]
[32m2025-08-21 14:04:22.779[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m [TID: N/A]  Initializing Spring embedded WebApplicationContext
[32m2025-08-21 14:04:23.159[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.r.DefaultLazyPropertyResolver     [0;39m [2m:[0;39m [TID: N/A]  Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[32m2025-08-21 14:04:23.159[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.u.j.d.DefaultLazyPropertyDetector     [0;39m [2m:[0;39m [TID: N/A]  Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[32m2025-08-21 14:04:25.248[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36morg.redisson.Version                    [0;39m [2m:[0;39m [TID: N/A]  Redisson 3.16.0
[32m2025-08-21 14:04:42.002[0;39m [redisson-netty-2-20] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mo.r.c.pool.MasterPubSubConnectionPool   [0;39m [2m:[0;39m [TID: N/A]  1 connections initialized for r-bp1rgz6nqi3c07bxfnpd.redis.rds.aliyuncs.com/************:6379
[32m2025-08-21 14:04:42.211[0;39m [redisson-netty-2-20] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mo.r.c.pool.MasterConnectionPool         [0;39m [2m:[0;39m [TID: N/A]  24 connections initialized for r-bp1rgz6nqi3c07bxfnpd.redis.rds.aliyuncs.com/************:6379
[32m2025-08-21 14:04:43.903[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.f.common.idg.AbstractHostTimeIdg    [0;39m [2m:[0;39m [TID: N/A]  从网卡中获取HostId。serviceNo:1,IP:*************, HostId:390
[32m2025-08-21 14:04:43.903[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.f.common.idg.AbstractHostTimeIdg    [0;39m [2m:[0;39m [TID: N/A]  初始化Id生成器。HostId:390,ShiftHostId:1756403854674493440
[32m2025-08-21 14:04:43.903[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.f.common.idg.AbstractHostTimeIdg    [0;39m [2m:[0;39m [TID: N/A]  时间底线TimeBaseLine:0，时间跨度TimeGap:1000
[32m2025-08-21 14:04:43.904[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.f.common.idg.AbstractHostTimeIdg    [0;39m [2m:[0;39m [TID: N/A]  设置随机IncNo成功。IncNo:true
[32m2025-08-21 14:04:46.649[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mpertySourcedRequestMappingHandlerMapping[0;39m [2m:[0;39m [TID: N/A]  Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]
[32m2025-08-21 14:04:48.880[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m [TID: N/A]  Starting ProtocolHandler ["http-nio-8080"]
[32m2025-08-21 14:04:48.913[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36md.s.w.p.DocumentationPluginsBootstrapper[0;39m [2m:[0;39m [TID: N/A]  Context refreshed
[32m2025-08-21 14:04:48.949[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36md.s.w.p.DocumentationPluginsBootstrapper[0;39m [2m:[0;39m [TID: N/A]  Found 1 custom documentation plugin(s)
[32m2025-08-21 14:04:49.013[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36ms.d.s.w.s.ApiListingReferenceScanner    [0;39m [2m:[0;39m [TID: N/A]  Scanning for api listing references
[32m2025-08-21 14:04:49.338[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: pageUsingPOST_1
[32m2025-08-21 14:04:49.403[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: saveTenantMenuUsingPOST_1
[32m2025-08-21 14:04:49.427[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: addUsingPOST_1
[32m2025-08-21 14:04:49.431[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: listUsingPOST_1
[32m2025-08-21 14:04:49.437[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: updateUsingPOST_1
[32m2025-08-21 14:04:49.449[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: addTenantUsingPOST_1
[32m2025-08-21 14:04:49.451[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: delUsingPOST_1
[32m2025-08-21 14:04:49.464[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: pageTenantUsingPOST_1
[32m2025-08-21 14:04:49.471[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: tenantInfoForNoLoginUsingPOST_1
[32m2025-08-21 14:04:49.472[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: updateStatusUsingPOST_1
[32m2025-08-21 14:04:49.476[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: updateTenantUsingPOST_1
[32m2025-08-21 14:04:49.479[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36m.d.s.w.r.o.CachingOperationNameGenerator[0;39m [2m:[0;39m [TID: N/A]  Generating unique operation named: updateTrialUsingPOST_1
[32m2025-08-21 14:04:49.570[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mcom.dl.basicservice.war.Application     [0;39m [2m:[0;39m [TID: N/A]  Started Application in 39.327 seconds (JVM running for 42.429)
[32m2025-08-21 14:04:49.800[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m [TID: N/A]  HikariPool-1 - Starting...
[32m2025-08-21 14:04:50.225[0;39m [main] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mcom.zaxxer.hikari.HikariDataSource      [0;39m [2m:[0;39m [TID: N/A]  HikariPool-1 - Start completed.
[32m2025-08-21 14:04:50.240[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.t.S.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,name,tenant_code,status,logo_img,tenant_domain,path_part,is_provider,is_hide_qr_login,is_we_work,tenant_desc,auth_mode,private_key,is_deleted,produce_type,video_callback_url,small_logo,is_trial,dm_produce_mode,simulate_local_preview,create_dt,create_by,modify_dt,modify_by FROM sys_tenant_info
[32m2025-08-21 14:04:50.268[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.t.S.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 14:04:50.322[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.t.S.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 1
[32m2025-08-21 14:04:50.432[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.P.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,menu_id,parent_id,name,icon,url,menu_level,sort,disable,system_code,scene_type,create_dt,create_by,modify_dt,modify_by FROM sys_privilege_menu WHERE (disable = ?)
[32m2025-08-21 14:04:50.432[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.P.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 0(Integer)
[32m2025-08-21 14:04:50.459[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.P.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 26
[32m2025-08-21 14:04:50.526[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.listFunctions           [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT m.tenant_code AS tenantCode,f.function_id AS functionId,f.function_code AS functionCode,f.name,f.icon,f.sort ,f.system_code FROM sys_tenant_function m,sys_privilege_function f WHERE m.function_id = f.function_id
[32m2025-08-21 14:04:50.526[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.listFunctions           [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 14:04:50.541[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.listFunctions           [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 0
[32m2025-08-21 14:04:50.542[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.u.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,user_id,role_id,system_code,create_dt,create_by,modify_dt,modify_by FROM sys_tenant_user_role
[32m2025-08-21 14:04:50.543[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.u.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 14:04:50.558[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.u.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 0
[32m2025-08-21 14:04:50.566[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,role_id,menu_id,system_code,create_dt,create_by,modify_dt,modify_by FROM sys_tenant_role_menu
[32m2025-08-21 14:04:50.566[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 14:04:50.581[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.m.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 0
[32m2025-08-21 14:04:50.583[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==>  Preparing: SELECT id,role_id,function_id,system_code,create_dt,create_by,modify_dt,modify_by FROM sys_tenant_role_function
[32m2025-08-21 14:04:50.583[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  ==> Parameters: 
[32m2025-08-21 14:04:50.598[0;39m [main] [32mDEBUG[0;39m [35m26760[0;39m [2m---[0;39m [36mc.d.b.b.d.s.f.T.selectList              [0;39m [2m:[0;39m [TID: N/A]  <==      Total: 0
[32m2025-08-21 14:04:51.052[0;39m [RMI TCP Connection(1)-*************] [32m INFO[0;39m [35m26760[0;39m [2m---[0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m [TID: N/A]  Initializing Spring DispatcherServlet 'dispatcherServlet'
[31m2025-08-21 14:05:31.406[0;39m [redisson-netty-2-6] [31mERROR[0;39m [35m26760[0;39m [2m---[0;39m [36mo.r.client.handler.ErrorsLoggingHandler [0;39m [2m:[0;39m [TID: N/A]  Exception occured. Channel: [id: 0x3dcb9b07, L:/*************:58405 - R:r-bp1rgz6nqi3c07bxfnpd.redis.rds.aliyuncs.com/************:6379]

java.io.IOException: 远程主机强迫关闭了一个现有的连接。
	at sun.nio.ch.SocketDispatcher.read0(Native Method)
	at sun.nio.ch.SocketDispatcher.read(SocketDispatcher.java:43)
	at sun.nio.ch.IOUtil.readIntoNativeBuffer(IOUtil.java:223)
	at sun.nio.ch.IOUtil.read(IOUtil.java:192)
	at sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:379)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:253)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:350)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:719)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:655)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:581)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:493)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:989)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.lang.Thread.run(Thread.java:750)

