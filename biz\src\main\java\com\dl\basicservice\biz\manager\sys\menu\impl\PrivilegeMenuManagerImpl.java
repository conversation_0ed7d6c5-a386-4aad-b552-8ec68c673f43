package com.dl.basicservice.biz.manager.sys.menu.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.service.CommonService;
import com.dl.basicservice.biz.dal.sys.function.po.TenantFunctionPO;
import com.dl.basicservice.biz.dal.sys.menu.PrivilegeMenuFunctionMapper;
import com.dl.basicservice.biz.dal.sys.menu.PrivilegeMenuMapper;
import com.dl.basicservice.biz.dal.sys.menu.po.MenuFunctionPO;
import com.dl.basicservice.biz.dal.sys.menu.po.PrivilegeMenuPO;
import com.dl.basicservice.biz.dal.sys.menu.po.TenantMenuPO;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.manager.sys.function.TenantFunctionManager;
import com.dl.basicservice.biz.manager.sys.function.dto.FunctionDTO;
import com.dl.basicservice.biz.manager.sys.menu.PrivilegeMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.TenantMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.assist.MenuTreeHelp;
import com.dl.basicservice.biz.manager.sys.menu.dto.MenuDTO;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PrivilegeMenuManagerImpl extends ServiceImpl<PrivilegeMenuMapper, PrivilegeMenuPO>
        implements PrivilegeMenuManager, CommonService {

    @Autowired
    private PrivilegeMenuFunctionMapper privilegeMenuFunctionMapper;
    @Autowired
    private TenantMenuManager tenantMenuManager;
    @Autowired
    private TenantFunctionManager tenantFunctionManager;

    @Autowired
    private TenantInfoManager tenantInfoManager;

    @Override
    public List<MenuDTO> listMenusAndFunctions(String systemCode,List<Integer> sceneTypeList,Boolean isProvider) {
        List<PrivilegeMenuPO> list;
        LambdaQueryWrapper<PrivilegeMenuPO> queryWrapper = Wrappers.lambdaQuery(PrivilegeMenuPO.class)
                .eq(PrivilegeMenuPO::getDisable, 0)
                .eq(PrivilegeMenuPO::getSystemCode, systemCode);

        if (!isProvider) {
            queryWrapper.in(PrivilegeMenuPO::getSceneType, sceneTypeList);
        }

        list = baseMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(list)) {
            //获取菜单对应的功能
            List<MenuFunctionPO> mflist= baseMapper.listMenuFunction();
            //把菜单功能转化为map对象，方便后面转换到菜单对象中
            Map<String, Set<MenuFunctionPO>> menuFunctionMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(mflist)) {
                mflist.forEach(mf -> {
                   String menuId = String.valueOf(mf.getMenuId());
                    if(menuFunctionMap.containsKey(menuId)){
                        menuFunctionMap.get(menuId).add(mf);
                    }else{
                        Set<MenuFunctionPO> set = new HashSet<>();
                        set.add(mf);
                        menuFunctionMap.put(menuId,set);
                    }
                });
            }

            //设置菜单对应的Function关系
            List<MenuDTO> dtos = list.stream()
                    .map(t -> {
                                MenuDTO dto = new MenuDTO();
                                dto.setIcon(t.getIcon());
                                dto.setParentId(t.getParentId());
                                dto.setSort(t.getSort());
                                dto.setUrl(t.getUrl());
                                dto.setMenuId(t.getMenuId());
                                dto.setDisable(t.getDisable());
                                dto.setName(t.getName());
                                dto.setLevel(t.getMenuLevel());

                                if (MapUtils.isNotEmpty(menuFunctionMap)) {
                                    Set<MenuFunctionPO> set = menuFunctionMap.get(String.valueOf(t.getMenuId()));
                                    if (CollectionUtils.isNotEmpty(set)) {
                                        List<FunctionDTO> functions = set.stream().map(mf -> {
                                            FunctionDTO privilegeFunctionDTO = new FunctionDTO();
                                            privilegeFunctionDTO.setFunctionId(mf.getFunctionId());
                                            privilegeFunctionDTO.setFunctionCode(mf.getFunctionCode());
                                            privilegeFunctionDTO.setIcon(mf.getIcon());
                                            privilegeFunctionDTO.setName(mf.getName());
                                            privilegeFunctionDTO.setSort(mf.getSort());
                                            return privilegeFunctionDTO;
                                        }).sorted(Comparator.comparing(FunctionDTO::getSort)).collect(Collectors.toList());

                                        dto.setFunctions(functions);
                                    }
                                }
                                return dto;
                            }).collect(Collectors.toList());


            //循环处理菜单对象，设置子菜单
            return MenuTreeHelp.listWithTree(dtos);
        }
        return null;
    }

    @Override
    public List<MenuDTO> listMenusAndFunctions(String tenantCode, String systemCode) {
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户参数为空");
        Assert.isTrue(StringUtils.isNotBlank(systemCode), "系统参数为空");

        List<MenuDTO> slist;
        //第1步 判断分配的对象是否是服务商，如果是服务商则可以获取全部菜单，如果不是服务商，则根据系统以及对应的使用场景获取菜单
        SysTenantInfoPO currentTenant = tenantInfoManager.lambdaQuery()
                .eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .eq(SysTenantInfoPO::getTenantCode, tenantCode)
                .one();

        if (Objects.nonNull(currentTenant)) {
            boolean isProvider = Const.ONE.equals(currentTenant.getIsProvider());
            List<Integer> sceneTypeList = new ArrayList<>();
            sceneTypeList.add(Const.ZERO);
            if (!isProvider) {
                sceneTypeList.add(Const.ONE.equals(currentTenant.getIsWeWork()) ? Const.ONE : Const.TWO);
            }
            slist = this.listMenusAndFunctions(systemCode, sceneTypeList, isProvider);
        } else {
            slist = new ArrayList<>();
        }
        //第2步:获取系统菜单和功能清单列表
        if (CollectionUtils.isEmpty(slist)) {
            return Collections.emptyList();
        }

        //第3步:获取租户的菜单列表
        Map<String, String> menuMap = new HashMap<>();
        LambdaQueryWrapper<TenantMenuPO> tmquery = new LambdaQueryWrapper<>();
        tmquery.eq(StringUtils.isNotBlank(tenantCode),TenantMenuPO::getTenantCode,tenantCode).eq(TenantMenuPO::getSystemCode, systemCode);
        List<TenantMenuPO> tmlist = tenantMenuManager.list(tmquery);
        if (CollectionUtils.isNotEmpty(tmlist)) {
            tmlist.forEach(mf -> {
                String menuId = String.valueOf(mf.getMenuId());
                menuMap.put(menuId,menuId);
            });
        }

        //第4步:获取租户的function列表
        Map<String, String> functionMap = new HashMap<>();
        LambdaQueryWrapper<TenantFunctionPO> tfquery = new LambdaQueryWrapper<>();
        tfquery.eq(StringUtils.isNotBlank(tenantCode), TenantFunctionPO::getTenantCode,tenantCode).eq(TenantFunctionPO::getSystemCode, systemCode);;
        List<TenantFunctionPO> tflist =tenantFunctionManager.list(tfquery);
        if (CollectionUtils.isNotEmpty(tflist)) {
            tflist.forEach(mf -> {
                String functionId = String.valueOf(mf.getFunctionId());
                functionMap.put(functionId,functionId);
            });
        }

        //第5步:打上租户是否有对应菜单和功能权限标识
        MenuTreeHelp.listSetOwnerWithTree(slist,menuMap,functionMap);

        return slist;
    }
}

