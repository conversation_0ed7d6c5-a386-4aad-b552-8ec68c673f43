package com.dl.basicservice.biz.manager.sys.tenant.enums;

import java.util.Objects;

public enum AppTypeEnum {

    WPA(0, "微信公众号"),
    MINI_PRO(1, "微信小程序");

    private Integer code;
    private String desc;

    AppTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AppTypeEnum getByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (AppTypeEnum e : AppTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e;
            }
        }
        return null;
    }

    public static String getDescByCode(Integer code) {
        if (Objects.isNull(code)) {
            return "";
        }
        for (AppTypeEnum e : AppTypeEnum.values()) {
            if (Objects.equals(code, e.getCode())) {
                return e.getDesc();
            }
        }
        return "";
    }
}
