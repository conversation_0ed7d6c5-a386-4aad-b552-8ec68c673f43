package com.dl.basicservice.web.controller.adm.sys.tenant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.basicservice.biz.common.annotation.NotLogin;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.service.ObjectStoragePathService;
import com.dl.basicservice.biz.common.util.DateUtil;
import com.dl.basicservice.biz.common.util.NameInitialsUtil;
import com.dl.basicservice.biz.config.TenantDomainConfig;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.manager.sys.role.enums.RoleTypeEnum;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.biz.manager.sys.role.TenantUserRoleManager;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserRolesParamBO;
import com.dl.basicservice.web.controller.adm.base.AbstractController;
import com.dl.basicservice.web.controller.adm.sys.tenant.convert.TenantConvert;
import com.dl.basicservice.web.controller.adm.sys.tenant.param.*;
import com.dl.basicservice.web.controller.adm.sys.tenant.vo.*;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping(path = "/tenant")
@Validated
public class TenantController extends AbstractController {

    private static final String ADMIN_ROLE_NAME = "超级管理员";
    private static final String ADMIN = "admin";
    @Autowired
    private TenantInfoManager tenantInfoManager;
    @Autowired
    private TenantRoleManager tenantRoleManager;
    @Autowired
    private SysAdmUserService sysAdmUserService;
    @Autowired
    private TenantUserRoleManager tenantUserRoleManager;
    @Autowired
    private HostTimeIdg hostTimeIdg;
    @Autowired
    private ObjectStoragePathService objectStoragePathService;

    @Resource
    private TenantDomainConfig tenantDomainConfig;

    @NotLogin
    @PostMapping("/info")
    @ApiOperation("租户-查询租户详情")
    public ResultModel<TenantInfoVO> tenantInfoForNoLogin(@RequestBody @Validated TenantDetailParam p) {
        SysTenantInfoPO currentTenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .eq(SysTenantInfoPO::getTenantCode, p.getTenantCode()).one();
        Assert.notNull(currentTenant, "租户不存在!");

        TenantInfoVO tenantInfoVo = new TenantInfoVO();
        TenantConvert.fillTenantBaseVO(currentTenant, tenantInfoVo);
        tenantInfoVo.setLogoImg(objectStoragePathService.compositeObjectStoragePath(currentTenant.getLogoImg()));
        tenantInfoVo.setSmallLogo(objectStoragePathService.compositeObjectStoragePath(currentTenant.getSmallLogo()));
        tenantInfoVo.setThirdLevelDomain(tenantDomainConfig.getTenantThirdLevelDomain(p.getTenantCode()));
        tenantInfoVo.setSpecialMagicLoginUrl(tenantDomainConfig.getTenantMagicLoginUrl(p.getTenantCode()));
        return ResultModel.success(tenantInfoVo);
    }

    @PostMapping("/page")
    @ApiOperation("租户-查询分页列表")
    public ResultPageModel<TenantListVO> pageTenant(@RequestBody @Validated TenantParam p) {
        LambdaQueryChainWrapper<SysTenantInfoPO> queryWrapper = tenantInfoManager.lambdaQuery()
                .eq(SysTenantInfoPO::getIsDeleted,Const.ZERO)
                .likeRight(StringUtils.isNotBlank(p.getName()), SysTenantInfoPO::getName, p.getName())
                ;
        queryWrapper.orderByDesc(SysTenantInfoPO::getId);

        SysTenantInfoPO currentTenant =
                tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted,Const.ZERO).eq(SysTenantInfoPO::getTenantCode, getTenantCode()).one();
        if (!Objects.equals(Const.ONE, currentTenant.getIsProvider())) {
            queryWrapper.eq(SysTenantInfoPO::getTenantCode, getTenantCode());
        }
        IPage<SysTenantInfoPO> tenantPage = queryWrapper.orderByDesc(SysTenantInfoPO::getId).page(new Page<>(p.getPageIndex(), p.getPageSize()));
        if (CollectionUtils.isEmpty(tenantPage.getRecords())) {
            return pageQueryModel(tenantPage, Collections.emptyList());
        }
        List<String> tenantCodeList = tenantPage.getRecords()
                .stream()
                .map(SysTenantInfoPO::getTenantCode)
                .distinct()
                .collect(Collectors.toList());
        List<TenantAdmUserPO> superAdminUsers = sysAdmUserService.lambdaQuery()
                .in(TenantAdmUserPO::getTenantCode, tenantCodeList)
                .eq(TenantAdmUserPO::getIsSuperAdm, NumberUtils.INTEGER_ONE)
                .list();
        Map<String, List<String>> superAdminUserMap = new HashMap<>();
        superAdminUserMap.putAll(superAdminUsers.stream()
                .collect(Collectors.groupingBy(TenantAdmUserPO::getTenantCode,
                        Collectors.mapping(TenantAdmUserPO::getAccount, Collectors.toList()))));
        List<TenantListVO> resultList = tenantPage.getRecords().stream().map(po -> {
            TenantListVO tenantVO = new TenantListVO();
            tenantVO.setId(po.getId().toString());
            tenantVO.setTenantCode(po.getTenantCode());
            tenantVO.setName(po.getName());
            tenantVO.setCreateBy(po.getCreateBy());
            tenantVO.setCreateDt(po.getCreateDt());
            tenantVO.setModifyBy(po.getModifyBy());
            tenantVO.setModifyDt(po.getModifyDt());
            tenantVO.setStatus(po.getStatus());
            tenantVO.setIsProvider(po.getIsProvider());
            tenantVO.setIsHideQrLogin(po.getIsHideQrLogin());
            tenantVO.setTenantDomain(po.getTenantDomain());
            tenantVO.setPathPart(po.getPathPart());
            tenantVO.setIsWeWork(po.getIsWeWork());
            tenantVO.setTenantDesc(po.getTenantDesc());
            tenantVO.setIsTrial(po.getIsTrial());
            tenantVO.setAuthMode(po.getAuthMode());
            tenantVO.setLogoImg(objectStoragePathService.compositeObjectStoragePath(po.getLogoImg()));
            tenantVO.setSmallLogo(objectStoragePathService.compositeObjectStoragePath(po.getSmallLogo()));
            tenantVO.setAdminAccounts(superAdminUserMap.get(po.getTenantCode()));
            //租户三级域名访问地址判断
            String tenantUrl = po.getTenantDomain().endsWith("/") ? po.getTenantDomain()+ "adm" : po.getTenantDomain() + "/adm";
            if(!po.getTenantDomain().contains(po.getTenantCode().toLowerCase())){
                tenantUrl = tenantUrl + "/login?agentId=" + po.getTenantCode();
            }
            tenantVO.setTenantUrl(tenantUrl);
            return tenantVO;
        }).collect(Collectors.toList());
        return pageQueryModel(tenantPage, resultList);
    }

    @PostMapping("/detail")
    @ApiOperation("租户-详情")
    public ResultModel<TenantVO> detailTenant(@RequestBody @Validated TenantDetailParam p) {
        Assert.isTrue(StringUtils.isNotBlank(p.getTenantCode()), "租户编码参数为空");
        SysTenantInfoPO currentTenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .eq(SysTenantInfoPO::getTenantCode, getTenantCode()).one();
        Assert.isTrue(Objects.equals(Const.ONE, currentTenant.getIsProvider()) || StringUtils
                .equals(getTenantCode(), p.getTenantCode()), "您不是服务商，无权查看!");
        SysTenantInfoPO po = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .eq(SysTenantInfoPO::getTenantCode, p.getTenantCode()).one();
        TenantVO tenantVO = new TenantVO();
        if (Objects.isNull(po)) {
            return ResultModel.success(tenantVO);
        }
        tenantVO.setLogoImg(objectStoragePathService.compositeObjectStoragePath(po.getLogoImg()));
        tenantVO.setSmallLogo(objectStoragePathService.compositeObjectStoragePath(po.getSmallLogo()));
        TenantConvert.fillTenantBaseVO(po, tenantVO);
        //超级管理员列表
        List<TenantAdmUserPO> superAdminUsers = sysAdmUserService.lambdaQuery()
                .eq(TenantAdmUserPO::getTenantCode, p.getTenantCode())
                .eq(TenantAdmUserPO::getIsSuperAdm, NumberUtils.INTEGER_ONE).list();
        if (CollectionUtils.isNotEmpty(superAdminUsers)) {
            tenantVO.setAdminAccounts(superAdminUsers.stream().map(u -> u.getAccount()).collect(Collectors.toList()));
        }
        return ResultModel.success(tenantVO);
    }

    @PostMapping("/add")
    @ApiOperation("租户-创建")
    @Transactional(rollbackFor = Exception.class)
    public ResultModel addTenant(@RequestBody @Validated AddTenantParam param) {

        Assert.isTrue(StringUtils.length(param.getTenantDesc()) <= Const.ONE_HUNDRED, "租户描述不能超过100个字符");
        checkProvider();
        String tenantCode = NameInitialsUtil.get(param.getName());
        if (StringUtils.isBlank(tenantCode)) {
            tenantCode = hostTimeIdg.generateId().toString();
        } else {
            if (Objects.nonNull(tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getTenantCode, tenantCode).one())) {
                tenantCode = hostTimeIdg.generateId().toString();
            }
        }
        //租户三级域名配置
        String tenantThirdDomain = tenantDomainConfig.getTenantThirdLevelDomain(tenantCode);
        // 新增租户
        SysTenantInfoPO tenant = new SysTenantInfoPO();
        tenant.setTenantCode(tenantCode);
        tenant.setName(param.getName());
        objectStoragePathService.preProcessObjectStorageUrl(param::getLogoImg,tenant::setLogoImg);
        objectStoragePathService.preProcessObjectStorageUrl(param::getSmallLogo,tenant::setSmallLogo);
        tenant.setTenantDomain(tenantThirdDomain);
        tenant.setPathPart(param.getPathPart());
        tenant.setIsWeWork(param.getIsWeWork());
        tenant.setTenantDesc(param.getTenantDesc());
        if (Objects.equals(param.getIsProvider(), Const.ONE)) {
            tenant.setIsProvider(param.getIsProvider());
        }
        tenant.setAuthMode(param.getAuthMode());
        tenant.setSimulateLocalPreview(param.getSimulateLocalPreview());
        tenant.setPrivateKey(Const.TENANT_DEFAULT_PRIVATE_KEY);
        tenantInfoManager.save(tenant);
        //刷新租户缓存
        tenantInfoManager.refreshSingleTenantCache(tenant.getTenantCode());
        return ResultModel.success(null);
    }

    private AddTenantVO initAdminUser(String tenantCode,String systemCode) {
        // 创建超级管理员角色
        Long roleId = hostTimeIdg.generateId().longValue();
        TenantRolePO po = new TenantRolePO();
        po.setRoleType(RoleTypeEnum.SUPER_ADMIN.getCode());
        po.setName(ADMIN_ROLE_NAME);
        po.setTenantCode(tenantCode);
        po.setRoleId(roleId);
        po.setSystemCode(systemCode);
        tenantRoleManager.save(po);

        // 创建超级管理员账号
        Long userId = hostTimeIdg.generateId().longValue();
        String rawPwd = DateUtil.format(new Date(), DateUtil.YMD);
        TenantAdmUserPO sysAdmUserPO = new TenantAdmUserPO();
        sysAdmUserPO.setUserId(userId);
        sysAdmUserPO.setTenantCode(tenantCode);
        sysAdmUserPO.setIsSuperAdm(Const.ONE);
        sysAdmUserPO.setAccount(ADMIN);
        sysAdmUserPO.setStatus(Const.ONE);
        sysAdmUserPO.setPassword(generatePwd(rawPwd));
        sysAdmUserService.save(sysAdmUserPO);

        // 赋予超管角色
        SysUserRolesParamBO bo = new SysUserRolesParamBO();
        bo.setUserId(userId);
        bo.setRoleIds(Arrays.asList(roleId));
        bo.setTenantCode(tenantCode);
        bo.setSystemCode(systemCode);
        tenantUserRoleManager.saveUserRoles(bo);
        AddTenantVO vo = new AddTenantVO();
        vo.setSuperAdminPwd(rawPwd);
        vo.setSuperAdminAccount(ADMIN);
        return vo;
    }

    @PostMapping("/update")
    @ApiOperation("租户-修改")
    @Transactional(rollbackFor = Exception.class)
    public ResultModel updateTenant(@RequestBody @Validated UpdateTenantParam param) {
        checkProvider();
        SysTenantInfoPO tenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted,Const.ZERO).eq(SysTenantInfoPO::getId, param.getId()).one();
        Assert.notNull(tenant, "租户不存在");
        Assert.isTrue(Objects.equals(tenant.getStatus(), Const.ZERO) || StringUtils.equals(param.getTenantCode(),
                tenant.getTenantCode()), "租户编号仅可在初次启用前修改！");
        if (!StringUtils.equals(param.getTenantCode(), tenant.getTenantCode())) {
            Assert.isNull(
                    tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted,Const.ZERO).eq(SysTenantInfoPO::getTenantCode, param.getTenantCode()).one(),
                    "该租户编号已存在，请修改！");
        }
        if (Objects.isNull(param.getIsProvider())) {
            param.setIsProvider(Const.ZERO);
        }
        Assert.isTrue(Objects.equals(tenant.getStatus(), Const.ZERO) || Objects
                .equals(param.getIsProvider(), tenant.getIsProvider()), "是否服务商仅可在初次启用前修改！");
        Assert.isTrue(Objects.equals(tenant.getStatus(), Const.ZERO) || Objects
                .equals(param.getAuthMode(), tenant.getAuthMode()), "授权模式仅可在初次启用前修改！");
        String originTenantCode = tenant.getTenantCode();
        tenant.setName(param.getName());
        tenant.setTenantCode(param.getTenantCode());
        tenant.setTenantDomain(tenantDomainConfig.getTenantThirdLevelDomain(param.getTenantCode()));
        tenant.setPathPart(param.getPathPart());
        tenant.setIsProvider(param.getIsProvider());
        if (StringUtils.isBlank(param.getLogoImg())){
            tenant.setLogoImg("");
        } else {
            objectStoragePathService.preProcessObjectStorageUrl(param::getLogoImg,tenant::setLogoImg);
        }
        objectStoragePathService.preProcessObjectStorageUrl(param::getSmallLogo,tenant::setSmallLogo);
        tenant.setIsHideQrLogin(Objects.isNull(param.getIsHideQrLogin()) ? Const.ZERO : param.getIsHideQrLogin());
        tenant.setTenantDesc(param.getTenantDesc());
        tenant.setAuthMode(param.getAuthMode());
        tenant.setSimulateLocalPreview(param.getSimulateLocalPreview());
        tenantInfoManager.updateById(tenant);
        if (!StringUtils.equals(originTenantCode, param.getTenantCode())) {
            //清理老租户缓存
            tenantInfoManager.refreshSingleTenantCache(originTenantCode);
        }

        //刷新租户缓存
        tenantInfoManager.refreshSingleTenantCache(param.getTenantCode());
        return ResultModel.success(null);
    }

    private String getTenantDomain(String tenantDomain, String pathPart) {
        tenantDomain = StringUtils.endsWith(tenantDomain, Const.SLASH) ? tenantDomain.substring(0,
                tenantDomain.lastIndexOf(Const.SLASH)) : tenantDomain;
        if (StringUtils.isNotBlank(pathPart)) {
            if (StringUtils.startsWith(pathPart, Const.SLASH)) {
                tenantDomain = tenantDomain + pathPart;
            } else {
                tenantDomain = tenantDomain + Const.SLASH + pathPart;
            }
        }
        return tenantDomain;
    }

    @PostMapping("/update/status/{id}/{status}")
    @ApiOperation("更新租户启用状态")
    @Transactional(rollbackFor = Exception.class)
    public ResultModel updateStatus(@PathVariable Long id, @PathVariable Integer status) {
        checkProvider();
        SysTenantInfoPO tenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted,Const.ZERO).eq(SysTenantInfoPO::getId, id).one();
        Assert.notNull(tenant, "租户不存在");
        if (Objects.equals(status, tenant.getStatus())) {
            return ResultModel.success(null);
        }
        if (Objects.equals(Const.ONE, status)) {
            //状态 0未启用 1正常 2停用
            tenant.setStatus(Const.ONE);
            //初始化根部门
            //初始化admin用户
            TenantAdmUserPO one = sysAdmUserService.lambdaQuery()
                    .eq(TenantAdmUserPO::getTenantCode, tenant.getTenantCode()).eq(TenantAdmUserPO::getAccount, ADMIN)
                    .eq(TenantAdmUserPO::getStatus, Const.ONE).one();
            if (Objects.isNull(one)) {
                initAdminUser(tenant.getTenantCode(),getSystemCode());
            }
        } else {
            //状态 0未启用 1正常 2停用
            tenant.setStatus(Const.TWO);
        }
        tenantInfoManager.updateById(tenant);
        tenantInfoManager.refreshSingleTenantCache(tenant.getTenantCode());

        return ResultModel.success(null);
    }

    @PostMapping("/del/{id}")
    @ApiOperation("删除租户")
    @Transactional(rollbackFor = Exception.class)
    public ResultModel del(@PathVariable Long id) {
        checkProvider();
        SysTenantInfoPO tenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted,Const.ZERO).eq(SysTenantInfoPO::getId, id).one();
        Assert.notNull(tenant, "租户不存在");
        Assert.isTrue(Objects.equals(Const.ZERO, tenant.getStatus()), "租户非未启用状态，不允许删除");
        tenant.setIsDeleted(true);
        tenant.setModifyDt(new Date());
        tenantInfoManager.updateById(tenant);
        tenantInfoManager.refreshSingleTenantCache(tenant.getTenantCode());
        return ResultModel.success(null);
    }

    @PostMapping("/update/trial")
    @ApiOperation("修改试用状态")
    public ResultModel updateTrial(@RequestBody UpdateTenantTrialParam param) {
        checkProvider();
        SysTenantInfoPO tenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted,Const.ZERO).eq(SysTenantInfoPO::getTenantCode, param.getTenantCode()).one();
        Assert.notNull(tenant, "租户不存在");
        tenant.setIsTrial(param.getTrialStatus());
        tenant.setModifyDt(new Date());
        tenantInfoManager.updateById(tenant);
        tenantInfoManager.refreshSingleTenantCache(tenant.getTenantCode());
        return ResultModel.success(null);
    }

    private void checkProvider() {
        SysTenantInfoPO currentTenant =
                tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getTenantCode, getTenantCode()).one();
        Assert.isTrue(Objects.equals(Const.ONE, currentTenant.getIsProvider()), "您不是服务商，无权配置!");
    }

    private String generatePwd(String rawPassword) {
        BCryptPasswordEncoder passwordEncoder = passwordEncoder();
        return passwordEncoder.encode(rawPassword);
    }

    private static BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(BCryptPasswordEncoder.BCryptVersion.$2A, new SecureRandom());
    }
}
