package com.dl.basicservice.biz.common.tencentcloud.cos;

import com.dl.basicservice.biz.common.BaseDTO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class CosTempCredentialDTO extends BaseDTO {

    String tmpSecretId;

    String tmpSecretKey;

    String sessionToken;

    Long startTime;

    Long expiredTime;

    String bucketId;

    String region;

}
