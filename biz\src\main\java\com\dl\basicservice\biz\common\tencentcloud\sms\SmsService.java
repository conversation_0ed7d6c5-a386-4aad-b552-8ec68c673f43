package com.dl.basicservice.biz.common.tencentcloud.sms;

import com.dl.basicservice.biz.common.tencentcloud.properties.TencentCloudProperties;
import com.dl.basicservice.biz.common.tencentcloud.sms.bo.SmsBO;
import com.dl.basicservice.biz.common.tencentcloud.sms.dto.SmsDTO;
import com.dl.basicservice.biz.common.tencentcloud.sms.dto.SmsStatus;
import com.dl.basicservice.biz.common.tencentcloud.sms.properties.SmsProperties;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.sms.v20210111.SmsClient;
import com.tencentcloudapi.sms.v20210111.models.SendSmsRequest;
import com.tencentcloudapi.sms.v20210111.models.SendSmsResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SmsService implements InitializingBean {
    @Autowired
    private TencentCloudProperties properties;
    @Autowired
    private SmsProperties smsProperties;

    private SmsClient client = null;

    @Override
    public void afterPropertiesSet() throws Exception {
        // 实例化一个认证对象，入参需要传入腾讯云账户secretId，secretKey,此处还需注意密钥对的保密
        // 密钥可前往https://console.cloud.tencent.com/cam/capi网站进行获取
        Credential cred = new Credential(properties.getApi().getSecretId(), properties.getApi().getSecretKey());
        // 实例化要请求产品的client对象
        this.client = new SmsClient(cred, smsProperties.getSmsRegion());
    }

    public SmsDTO send(SmsBO smsBO) throws TencentCloudSDKException {
        // 实例化一个请求对象,每个接口都会对应一个request对象
        SendSmsRequest req = new SendSmsRequest();
        //手机号数组
        req.setPhoneNumberSet(smsBO.getPhoneNumberSet());

        //短信应用id
        req.setSmsSdkAppId(smsProperties.getSmsSdkAppId());
        //签名内容
        req.setSignName(smsBO.getSignName());
        //正文模板id
        req.setTemplateId(smsBO.getTemplateId());
        //模板参数数组
        req.setTemplateParamSet(smsBO.getTemplateParamSet());

        // 返回的resp是一个SendSmsResponse的实例，与请求对象对应
        SendSmsResponse resp = client.SendSms(req);

        SmsDTO smsDTO = null;
        if(resp != null){
            smsDTO = new SmsDTO();
            smsDTO.setRequestId(resp.getRequestId());
            smsDTO.setSmsStatusList(Arrays.stream(resp.getSendStatusSet()).map(sendStatus -> {
                SmsStatus smsStatus  = new SmsStatus();
                smsStatus.setCode(sendStatus.getCode());
                smsStatus.setFee(sendStatus.getFee());
                smsStatus.setIsoCode(sendStatus.getIsoCode());
                smsStatus.setMessage(sendStatus.getMessage());
                smsStatus.setSerialNo(sendStatus.getSerialNo());
                smsStatus.setPhoneNumber(sendStatus.getPhoneNumber());
                smsStatus.setSessionContext(sendStatus.getSessionContext());
                return smsStatus;
            }).collect(Collectors.toList()));
        }

        return smsDTO;
    }

}
