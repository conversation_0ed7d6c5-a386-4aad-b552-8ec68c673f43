package com.dl.basicservice.biz.manager.sys.oplog.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 15:18
 */
@Data
public class OpLogAddBO {

    @ApiModelProperty(value = "接入业务的编码", required = true)
    private String bizCode;

    @ApiModelProperty(value = "操作对象", required = true)
    private String opObject;

    @ApiModelProperty(value = "操作对象主键", required = true)
    private String opKey;

    @ApiModelProperty(value = "操作类型 自定义（add、update、delete）", required = true)
    private String opType;

    @ApiModelProperty("操作前数据 格式自定义")
    private String opBefore;

    @ApiModelProperty("操作后数据 格式自定义")
    private String opAfter;

    @ApiModelProperty("操作说明")
    private String remark;

    @ApiModelProperty(value = "租户编码", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "操作人", required = true)
    private String opUserId;

    @ApiModelProperty(value = "操作人姓名", required = true)
    private String opUserName;

    @ApiModelProperty(value = "操作时间", required = true)
    private Date opDt;

    @ApiModelProperty("扩展信息")
    private String extData;
}
