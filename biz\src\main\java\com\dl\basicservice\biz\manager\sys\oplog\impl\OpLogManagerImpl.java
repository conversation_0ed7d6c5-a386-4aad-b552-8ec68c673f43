package com.dl.basicservice.biz.manager.sys.oplog.impl;

import com.alibaba.nacos.common.utils.Objects;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.dal.sys.oplog.OpLogMapper;
import com.dl.basicservice.biz.dal.sys.oplog.po.OpLogPO;
import com.dl.basicservice.biz.manager.sys.oplog.OpLogManager;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogAddBO;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogPageBO;
import com.dl.basicservice.biz.manager.sys.oplog.hepler.OpLogHelper;
import com.dl.framework.common.idg.HostTimeIdg;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 14:44
 */
@Component
public class OpLogManagerImpl extends ServiceImpl<OpLogMapper, OpLogPO> implements OpLogManager {

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Override
    public Long newOpLog(OpLogAddBO addBO) {
        OpLogPO po = OpLogHelper.cnvOpLogAddBO2PO(addBO);
        po.setLogId(hostTimeIdg.generateId().longValue());
        po.setCreateDt(new Date());
        po.setModifyDt(new Date());
        po.setIsDeleted(Const.ZERO);
        this.save(po);
        return po.getLogId();
    }

    @Override
    public Page<OpLogPO> page(OpLogPageBO bo) {
        LambdaQueryWrapper<OpLogPO> wrapper = Wrappers.lambdaQuery(OpLogPO.class)
                .eq(OpLogPO::getBizCode, bo.getBizCode())
                .eq(StringUtils.isNotBlank(bo.getOpObject()), OpLogPO::getOpObject, bo.getOpObject())
                .eq(StringUtils.isNotBlank(bo.getOpKey()), OpLogPO::getOpKey, bo.getOpKey())
                .eq(StringUtils.isNotBlank(bo.getOpType()), OpLogPO::getOpType, bo.getOpType())
                .eq(StringUtils.isNotBlank(bo.getOpUserId()), OpLogPO::getOpUserId, bo.getOpUserId())
                .eq(StringUtils.isNotBlank(bo.getTenantCode()), OpLogPO::getTenantCode, bo.getTenantCode())
                .ge(Objects.nonNull(bo.getOpSince()), OpLogPO::getOpDt, bo.getOpSince())
                .le(Objects.nonNull(bo.getOpUntil()), OpLogPO::getOpDt, bo.getOpUntil())
                .eq(OpLogPO::getIsDeleted, Const.ZERO).orderByDesc(OpLogPO::getId);

        return this.page(new Page<>(bo.getPageIndex(), bo.getPageSize()), wrapper);
    }
}
