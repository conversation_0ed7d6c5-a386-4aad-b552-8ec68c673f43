package com.dl.basicservice.biz.manager.sys.user.dto;

import com.dl.basicservice.biz.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@ApiModel("权限结果")
@Builder
public class SysPermissionDTO extends BaseDTO {

    @ApiModelProperty("权限编码")
    private String functionCode;

    @ApiModelProperty("结果，0-无权限，1-有权限")
    private String permission;
}

