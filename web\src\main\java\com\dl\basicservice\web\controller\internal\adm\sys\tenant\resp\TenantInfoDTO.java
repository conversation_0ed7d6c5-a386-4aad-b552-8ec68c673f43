package com.dl.basicservice.web.controller.internal.adm.sys.tenant.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-16 14:32
 */
@Data
public class TenantInfoDTO implements Serializable {
    private static final long serialVersionUID = 4607625909041615992L;

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("租户名称")
    private String name;

    @ApiModelProperty("租户code")
    private String tenantCode;

    @ApiModelProperty("logo图片")
    private String logoImg;

    @ApiModelProperty("租户状态")
    private Integer status;

    @ApiModelProperty("租户访问域名，包含scheme")
    private String tenantDomain;

    @ApiModelProperty("租户访问路径片段，常用于本地化部署")
    private String pathPart;

    @ApiModelProperty("是否隐藏二维码登录：0 否，默认；1 是")
    private Integer isHideQrLogin;

    @ApiModelProperty("是否供应商：0 否，默认； 1 是")
    private Integer isProvider;

    @ApiModelProperty("是否企微入驻租户：0 否； 1 是 默认")
    private Integer isWeWork;

    @ApiModelProperty("授权模式，1-微信全托管模式，2-微信第三方平台")
    private Integer authMode;

    @ApiModelProperty("租户信息描述")
    private String tenantDesc;

    @ApiModelProperty("私钥")
    private String privateKey;

    @ApiModelProperty("0-纯页面模式，1-纯API提供模式，2-页面+API模式，默认值是0")
    private Integer produceType;

    @ApiModelProperty("存在API模式时，合作方视频回调地址")
    private String videoCallbackUrl;

    @ApiModelProperty("小logo")
    private String smallLogo;
    @ApiModelProperty("租户访问地址")
    private String tenantUrl;

    @ApiModelProperty("租户三级域名")
    private String thirdLevelDomain;

    @ApiModelProperty("是否试用租户 0-否 1-是")
    private Integer isTrial;

    @ApiModelProperty("自研编辑器数字人视频合成方式。 0-模板每个卡片合成1次请求数字人合成视频，并通过ASR识别时间戳。1-模板每个卡片都请求数字人合成视频方式。")
    private Integer dmProduceMode;

    /**
     * 模拟本地化预览 0-否，1-是
     * 像安信这类客户，要 抹除 所有和 “定力数影”有关的设置，比如banner图不一样等等的
     */
    @ApiModelProperty("模拟本地化预览 0-否，1-是")
    private Integer simulateLocalPreview;

    private Date createDt;

    private Long createBy;

    private Date modifyDt;

    private Long modifyBy;
}
