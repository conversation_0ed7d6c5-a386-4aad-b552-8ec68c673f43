package com.dl.basicservice.biz.common.service;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.function.Function;

@Service
public class CosPathService implements ObjectStoragePathService {

    @Value("${objectStorage.cos.resourcePrefix}")
    private String objectStorageResourcePrefix;

    @Override
    public String getObjectStorageResourcePrefix() {
        return objectStorageResourcePrefix;
    }

}
