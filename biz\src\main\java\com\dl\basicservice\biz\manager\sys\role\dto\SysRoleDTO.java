package com.dl.basicservice.biz.manager.sys.role.dto;
import com.dl.basicservice.biz.common.BaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SysRoleDTO extends BaseDTO {

    private Long roleId;

    private String roleName;
    /**
     * @see com.dl.basicservice.biz.manager.sys.role.enums.RoleTypeEnum
     */
    private String roleType;

    //    private String roleTypeName;

    private String tenantCode;

    private Long createBy;

    private Long modifyBy;

    //    private String createName;

    //    private String modifyName;

    @JsonFormat(pattern="yyyy-MM-dd")
    private Date createDt;

    @JsonFormat(pattern="yyyy-MM-dd")
    private Date modifyDt;

}
