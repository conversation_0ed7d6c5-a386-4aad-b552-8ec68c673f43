package com.dl.basicservice.biz.dal.sys.userbind.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-01 16:51
 */
@Data
@TableName("sys_tenant_admuser_bind")
public class TenantAdmUserBindPO {

    @TableId("id")
    private Long id;

    private String tenantCode;

    private String extUserId;

    private Long userId;

    private Integer isDeleted;

    @TableField(value = "create_dt", fill = FieldFill.INSERT)
    private Date createDt;

    @TableField(value = "modify_dt", fill = FieldFill.INSERT_UPDATE)
    private Date modifyDt;

}
