package com.dl.basicservice.web.controller.internal.adm.sys.oplog.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 15:00
 */
@Data
@ApiModel("操作日志DTO")
public class OpLogDTO {

    @ApiModelProperty("日志id")
    private Long logId;

    @ApiModelProperty(value = "接入业务的编码")
    private String bizCode;

    @ApiModelProperty(value = "操作对象")
    private String opObject;

    @ApiModelProperty(value = "操作对象主键")
    private String opKey;

    @ApiModelProperty(value = "操作类型 自定义（add、update、delete）")
    private String opType;

    @ApiModelProperty("操作前数据 格式自定义")
    private String opBefore;

    @ApiModelProperty("操作后数据 格式自定义")
    private String opAfter;

    @ApiModelProperty("操作说明")
    private String remark;

    @ApiModelProperty(value = "租户编码")
    private String tenantCode;

    @ApiModelProperty(value = "操作人")
    private String opUserId;

    @ApiModelProperty(value = "操作人姓名")
    private String opUserName;

    @ApiModelProperty(value = "操作时间")
    private Date opDt;

    @ApiModelProperty("扩展信息")
    private String extData;

}
