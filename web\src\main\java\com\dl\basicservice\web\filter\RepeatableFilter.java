package com.dl.basicservice.web.filter;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Repeatable 过滤器
 */
@Slf4j
public class RepeatableFilter implements Filter {
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        ServletRequest requestWrapper = null;
        if (request instanceof HttpServletRequest && (
                StringUtils.equalsIgnoreCase(request.getContentType(), MediaType.APPLICATION_JSON_VALUE)
                        || StringUtils.equalsIgnoreCase(request.getContentType(),
                        MediaType.APPLICATION_JSON_UTF8_VALUE))) {
            requestWrapper = new RepeatedlyRequestWrapper((HttpServletRequest) request, response);
        }
        long a = System.currentTimeMillis();
        if (null == requestWrapper) {
            chain.doFilter(request, response);
        } else {
            chain.doFilter(requestWrapper, response);
        }
        long b = System.currentTimeMillis();
        if (request instanceof HttpServletRequest) {
            log.info("接口:{}, 耗时:{} 毫秒", ((HttpServletRequest) request).getRequestURI(), b - a);

        }
    }

}
