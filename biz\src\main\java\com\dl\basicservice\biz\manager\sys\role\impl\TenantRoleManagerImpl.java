package com.dl.basicservice.biz.manager.sys.role.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.common.util.OperatorUtil;
import com.dl.basicservice.biz.dal.sys.role.TenantRoleMapper;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.dal.sys.user.TenantUserRoleMapper;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserRolePO;
import com.dl.basicservice.biz.manager.sys.role.enums.RoleTypeEnum;
import com.dl.basicservice.biz.manager.sys.menu.impl.UserRoleMenuRedisCache;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.biz.manager.sys.role.bo.RoleParamBO;
import com.dl.basicservice.biz.manager.sys.role.bo.RoleSearchParamBO;
import com.dl.basicservice.biz.manager.sys.role.dto.SysRoleDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class TenantRoleManagerImpl extends ServiceImpl<TenantRoleMapper, TenantRolePO> implements TenantRoleManager {
    @Autowired
    private TenantUserRoleMapper tenantUserRoleMapper;

    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @Resource
    private OperatorUtil operatorUtil;

    @Override
    public boolean belongUsers(Long roleId) {
        Assert.notNull(roleId, "角色ID不能为空");
        Integer integer = tenantUserRoleMapper
                .selectCount(Wrappers.<TenantUserRolePO>lambdaQuery().eq(TenantUserRolePO::getRoleId, roleId));
        return integer > 0;
    }

    @Override
    public IPage<SysRoleDTO> findRoles(RoleSearchParamBO roleParamBO) {
        LambdaQueryWrapper<TenantRolePO> wrapper = Wrappers.lambdaQuery();
        String roleName = roleParamBO.getRoleName();
        String tenantCode = roleParamBO.getTenantCode();
        List<Long> roleIds;
        if (StringUtils.isNoneBlank(roleName)) {
            wrapper.like(TenantRolePO::getName, roleName);
        }
        //按角色id查询，如果角色相关的角色-菜单为空，则返回空
        if (roleParamBO.getUserId() != null) {
            LambdaQueryWrapper<TenantUserRolePO> userRoleWrapper = Wrappers.lambdaQuery();
            userRoleWrapper.eq(TenantUserRolePO::getUserId, roleParamBO.getUserId());
            roleIds = tenantUserRoleMapper.selectList(userRoleWrapper).stream().map(TenantUserRolePO::getRoleId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(roleIds)) {
                return new Page<>(roleParamBO.getPageIndex(), roleParamBO.getPageSize());
            }
            wrapper.in(TenantRolePO::getRoleId, roleIds);
        }
        wrapper.eq(TenantRolePO::getTenantCode, tenantCode);
        wrapper.eq(TenantRolePO::getSystemCode,roleParamBO.getSystemCode());
        IPage<TenantRolePO> page = baseMapper.selectPage(convert(roleParamBO), wrapper);
        return page.convert(
                po -> SysRoleDTO.builder().roleName(po.getName()).roleId(po.getRoleId()).tenantCode(po.getTenantCode())
                        .roleType(po.getRoleType()).createBy(po.getCreateBy()).createDt(po.createDt)
                        .modifyBy(po.modifyBy).modifyDt(po.modifyDt).build());
    }

    @Override
    public void deleteRole(Long roleId) {
        Assert.notNull(roleId, "角色ID不能为空");
        LambdaQueryWrapper<TenantRolePO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(TenantRolePO::getRoleId, roleId);
        baseMapper.delete(wrappers);
    }

    @Override
    public void update(RoleParamBO roleParamBO) {
        LambdaQueryWrapper<TenantRolePO> wrappers = Wrappers.lambdaQuery();
        wrappers.eq(TenantRolePO::getRoleId, roleParamBO.getRoleId());
        TenantRolePO po = baseMapper.selectOne(wrappers);

        boolean f = false;
        if (StringUtils.isNotEmpty(roleParamBO.getRoleName())) {
            po.setName(roleParamBO.getRoleName());
            f = true;
        }
        if (StringUtils.isNotEmpty(roleParamBO.getRoleType())) {
            po.setRoleType(roleParamBO.getRoleType());
            f = true;
        }

        if (f) {
            baseMapper.update(po, wrappers);
        }
    }

    @Override
    public void setDefaultRole(String tenantCode, String systemCode, Long roleId) {
        //先查询已有的默认角色
        TenantRolePO exisitDefaultRole = baseMapper.selectOne(
                Wrappers.lambdaQuery(TenantRolePO.class).eq(TenantRolePO::getTenantCode, tenantCode)
                        .eq(TenantRolePO::getSystemCode, systemCode)
                        .eq(TenantRolePO::getRoleType, RoleTypeEnum.DEFAULT.getCode()));
        //若已有默认角色，则先将老的默认角色置为非默认角色
        if (Objects.nonNull(exisitDefaultRole)) {
            if (roleId.equals(exisitDefaultRole.getRoleId())) {
                return;
            }
            exisitDefaultRole.setRoleType(RoleTypeEnum.NORMAL.getCode());
            this.updateById(exisitDefaultRole);
        }
        this.update(Wrappers.lambdaUpdate(TenantRolePO.class).eq(TenantRolePO::getTenantCode, tenantCode)
                .eq(TenantRolePO::getSystemCode, systemCode).eq(TenantRolePO::getRoleId, roleId)
                .set(TenantRolePO::getRoleType, RoleTypeEnum.DEFAULT.getCode())
                .set(TenantRolePO::getModifyDt, new Date()).set(TenantRolePO::getModifyBy, operatorUtil.getOperator()));
    }

    @Override
    public SysRoleDTO queryDefaultRole(String tenantCode, String systemCode) {
        TenantRolePO po = baseMapper.selectOne(
                Wrappers.lambdaQuery(TenantRolePO.class).eq(TenantRolePO::getTenantCode, tenantCode)
                        .eq(TenantRolePO::getSystemCode, systemCode)
                        .eq(TenantRolePO::getRoleType, RoleTypeEnum.DEFAULT.getCode()));
        if (Objects.isNull(po)) {
            return null;
        }

        return SysRoleDTO.builder().roleName(po.getName()).roleId(po.getRoleId()).tenantCode(po.getTenantCode())
                .roleType(po.getRoleType()).createBy(po.getCreateBy()).createDt(po.createDt).modifyBy(po.modifyBy)
                .modifyDt(po.modifyDt).build();
    }

    @Override
    public List<SysRoleDTO> queryDefaultRoles(String tenantCode) {
        List<TenantRolePO> poList = baseMapper.selectList(
                Wrappers.lambdaQuery(TenantRolePO.class).eq(TenantRolePO::getTenantCode, tenantCode)
                        .eq(TenantRolePO::getRoleType, RoleTypeEnum.DEFAULT.getCode()));

        return poList.stream().map(po -> SysRoleDTO.builder().roleName(po.getName()).roleId(po.getRoleId())
                .tenantCode(po.getTenantCode()).roleType(po.getRoleType()).createBy(po.getCreateBy())
                .createDt(po.createDt).modifyBy(po.modifyBy).modifyDt(po.modifyDt).build())
                .collect(Collectors.toList());
    }
}
