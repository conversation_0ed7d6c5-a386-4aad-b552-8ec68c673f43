package com.dl.basicservice.web.controller.adm.sys.tenant.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "租户试用账户信息")
public class TenantTrialAccountVO {
    @ApiModelProperty(value = "账户ID")
    private String accountId;

    @ApiModelProperty(value = "状态：1正常，2冻结")
    private Integer status;

    @ApiModelProperty(value = "租户编号")
    private String tenantCode;

    @ApiModelProperty(value = "剩余使用次数")
    private Integer remainingAttempts;
}
