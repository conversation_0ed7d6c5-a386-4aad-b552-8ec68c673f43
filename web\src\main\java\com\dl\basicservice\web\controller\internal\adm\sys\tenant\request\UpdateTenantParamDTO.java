package com.dl.basicservice.web.controller.internal.adm.sys.tenant.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 11:05
 */
@Data
public class UpdateTenantParamDTO {

    @ApiModelProperty(value = "租户id", required = true)
    @NotNull(message = "租户ID必填")
    private Long id;

    @ApiModelProperty(value = "租户code", required = true)
    @NotBlank
    private String tenantCode;

    @ApiModelProperty("租户名称")
    private String name;

    @ApiModelProperty("租户访问路径片段，常用于本地化部署")
    private String pathPart;

    @ApiModelProperty("是否服务商： 0 否 ；1 是")
    private Integer isProvider;

    @ApiModelProperty("是否隐藏扫码登录： 0 否 ；1 是")
    private Integer isHideQrLogin;

    @Length(max = 512, message = "logo图片地址过长")
    @ApiModelProperty("logo图片")
    private String logoImg;

    @Length(max = 100, message = "租户描述不能超过100字符")
    @ApiModelProperty("租户描述")
    private String tenantDesc;

    @ApiModelProperty("授权模式，1-微信全托管模式，2-微信第三方平台")
    private Integer authMode;

    @ApiModelProperty("页面小logo")
    private String smallLogo;

    /**
     * 模拟本地化预览 0-否，1-是 默认为0
     * 像安信这类客户，要 抹除 所有和 “定力数影”有关的设置，比如banner图不一样等等的
     */
    @ApiModelProperty("模拟本地化预览 0-否，1-是")
    private Integer simulateLocalPreview;

    @ApiModelProperty("操作人id")
    @NotNull(message = "操作人id不能为空")
    private Long operatorId;

}
