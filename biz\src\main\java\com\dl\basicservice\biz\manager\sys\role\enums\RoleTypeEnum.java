package com.dl.basicservice.biz.manager.sys.role.enums;

/**
 * @describe: 角色类型
 * @author: zhousx
 * @date: 2022/7/7 9:57
 */
public enum RoleTypeEnum {
    SUPER_ADMIN("super_admin", "超级管理员"),
    NORMAL("normal", "常规"),
    DEFAULT("default","默认角色")
    ;

    private String code;

    private String desc;

    RoleTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
