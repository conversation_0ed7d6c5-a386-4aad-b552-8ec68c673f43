package com.dl.basicservice.biz.common.util.spider;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.dl.basicservice.biz.common.constant.Const;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 文章爬取工具类
 *
 * <AUTHOR>
 * @since 2020/01/04
 */
@Slf4j
public class SpiderUtil {

    // 微信公众号文章域名
    private static final String WX_DOMAIN = "https://mp.weixin.qq.com";
    // 文章返回前端统一key常量
    public static final String KEY_TITLE = "title"; // 文章标题
    public static final String KEY_COVER_URL = "coverLink"; // 文章封面图链接
    private static final String KEY_REFER_NAME = "referName"; // 文章出处作者
    private static final String KEY_REFER_URL = "referLink"; // 文章出处链接
    public static final String KEY_DESCRIPTION = "description"; // 文章出处链接
    public static final String KEY_JS_ARTICLE = "js_article"; // 文章div内容

    /**
     * 根据文章链接抓取文章内容
     *
     * @param url 文章链接
     * @return 文章内容
     */
    public static Resp<Document> getDocument(String url) {
        // 检测链接是否合法
        String msg = checkUrl(url);
        if (msg != null) {
            return Resp.error(msg);
        }
        // 请求与响应
        String resp = HttpTool.get(url, getWxHeaderMap());
        if (resp == null || resp.trim().length() == 0) {
            return Resp.error("文章获取失败，请检查链接是否正确");
        }
        try {
            return Resp.success(Jsoup.parse(resp));
        } catch (Exception e) {
            log.error("文章解析失败>", e);
            return Resp.error("文章解析失败");
        }
    }

    /**
     * 根据文章链接抓取文章内容
     *
     * @param url 文章链接
     * @return 文章内容
     */
    public static Resp<JSONObject> getArticle(String url) {
        // 解析
        Resp<JSONObject> resp = getWxArticleContent(url);
        if (resp.isError()) {
            return Resp.error(resp.getMsg());
        }
        return resp;
    }

    /**
     * 检测文章链接是否合法
     */
    public static String checkUrl(String url) {
        if (url == null) {
            return "请输入文章链接";
        }
        if (!url.startsWith(WX_DOMAIN)) {
            return "请输入微信公众号文章链接";
        }
        return null;
    }

    /**
     * 微信公众号请求头设置
     */
    public static Map<String, String> getWxHeaderMap() {
        Map<String, String> map = new HashMap<>(new LinkedHashMap<>());
        map.put("Accept", "text/html, application/xhtml+xml, image/jxr, */*");
        map.put("Accept-Encoding", "gzip, deflate");
        map.put("Accept-Language", "zh-Hans-CN, zh-Hans; q=0.8, en-US; q=0.5, en; q=0.3");
        map.put("Host", "mp.weixin.qq.com");
        map.put("If-Modified-Since", DateUtil.formatDate(new Date()));
        map.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko");
        return map;
    }

    /**
     * 解析微信公众号文章
     *
     * @param url 文章链接
     * @return 文章信息
     */
    public static Resp<JSONObject> getWxArticleContent(String url) {
        Resp<Document> resp = getDocument(url);
        if (!resp.isSuccess()) {
            return Resp.error(resp.getMsg());
        }
        Document document = resp.getBody();
        try {
            // 文章封面图链接
            Element coverUrlElement = select(document, "meta[property=\"og:image\"]");
            // 文章摘要
            Element desc = select(document, "meta[name=\"description\"]");
            JSONObject json = new JSONObject(new LinkedHashMap<>());
            json.put(KEY_TITLE, getTitle(document));
            json.put(KEY_COVER_URL,
                    Objects.nonNull(coverUrlElement) ? coverUrlElement.attr("content") : StringUtils.EMPTY);
            // 文章出处（作者）
            json.put(KEY_REFER_NAME, get(document, "profile_nickname"));
            json.put(KEY_REFER_URL, url);
            json.put(KEY_DESCRIPTION, Objects.nonNull(desc) ? desc.attr("content") : StringUtils.EMPTY);
            return Resp.success(json);
        } catch (Exception e) {
            log.error("文章解析失败>", e);
            return Resp.error("文章解析失败");
        }
    }

    private static String getTitle(Document document) {
        // 文章标题
        Element titleElement = select(document, "meta[property=\"og:title\"]");
        if (Objects.nonNull(titleElement)) {
            return titleElement.attr("content");
        }
        titleElement = document.getElementById("activity-name");
        if (Objects.nonNull(titleElement)) {
            return titleElement.text();
        }
        return StringUtils.EMPTY;
    }

    private static String get(Document document, String attr) {
        Elements nickNameElement = document.getElementsByClass(attr);
        if (Objects.isNull(nickNameElement) || CollectionUtils.isEmpty(nickNameElement)) {
            return StringUtils.EMPTY;
        }
        Element element = nickNameElement.get(Const.ZERO);
        if (Objects.isNull(element)) {
            return StringUtils.EMPTY;
        }
        return element.text();
    }

    private static Element select(Document document, String attr) {
        Elements select = document.select(attr);
        if (Objects.isNull(select) || CollectionUtils.isEmpty(select)) {
            return null;
        }
        return select.get(Const.ZERO);
    }
}
