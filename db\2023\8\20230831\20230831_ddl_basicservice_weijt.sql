CREATE TABLE `account_tenant_trial` (
                                        `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
                                        `tenant_code` varchar(255) NOT NULL COMMENT '租户编号',
                                        `balance` bigint NOT NULL COMMENT '余额',
                                        `create_dt` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
                                        `create_by` bigint NOT NULL COMMENT '创建人userId',
                                        `modify_dt` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                        `modify_by` bigint NOT NULL COMMENT '更新人userId',
                                        `is_deleted` tinyint NOT NULL DEFAULT '0' COMMENT '是否删除 0否 1是',
                                        PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='租户试用账户表';

ALTER TABLE  `sys_tenant_info`
    ADD COLUMN `is_trial` tinyint NULL DEFAULT 0 COMMENT '是否试用租户： 0否 1是';