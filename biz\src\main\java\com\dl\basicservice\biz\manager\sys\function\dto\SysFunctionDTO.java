package com.dl.basicservice.biz.manager.sys.function.dto;

import com.dl.basicservice.biz.common.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 功能权限
 */
@Data
@ApiModel("功能权限")
@AllArgsConstructor
@Builder
@NoArgsConstructor
public class SysFunctionDTO extends BaseDTO {

    @ApiModelProperty("功能名称")
    private String name;

    @ApiModelProperty("编码")
    private String functionCode;

    @ApiModelProperty("功能图标")
    private String icon;

    @ApiModelProperty("显示顺序")
    private Integer sort;

}
