package com.dl.basicservice.biz.manager.sys.role.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.common.annotation.Logical;
import com.dl.basicservice.biz.dal.sys.role.TenantRoleMapper;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.dal.sys.user.TenantUserRoleMapper;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserRolePO;
import com.dl.basicservice.biz.manager.sys.menu.impl.UserRoleMenuRedisCache;
import com.dl.basicservice.biz.manager.sys.role.TenantUserRoleManager;
import com.dl.basicservice.biz.manager.sys.role.bo.PermissionBO;
import com.dl.basicservice.biz.manager.sys.role.dto.SysRoleIdDTO;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserRolesParamBO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class TenantUserRoleManagerImpl extends ServiceImpl<TenantUserRoleMapper, TenantUserRolePO>
        implements TenantUserRoleManager {

    @Autowired
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @Autowired
    private TenantRoleMapper roleMapper;

    @Autowired
    private SysAdmUserService sysAdmUserService;

    private void validateRoleIds(String systemCode, List<Long> roleIds, String tenantCode) {
        if(CollectionUtils.isEmpty(roleIds)){
            return;
        }

        LambdaQueryWrapper<TenantRolePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(TenantRolePO::getRoleId, roleIds);
        lambdaQueryWrapper.in(TenantRolePO::getTenantCode, tenantCode);
        lambdaQueryWrapper.eq(TenantRolePO::getSystemCode, systemCode);
        Integer cnt = roleMapper.selectCount(lambdaQueryWrapper);
        Assert.isTrue(roleIds.size() == cnt, "roleids非法");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveUserRoles(SysUserRolesParamBO bo) {
        Long userId = bo.getUserId();
        List<Long> roleIds = bo.getRoleIds();
        String systemCode = bo.getSystemCode();
        Assert.notNull(userId, "用户id不能为空");
        LambdaQueryWrapper<TenantUserRolePO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        //校验角色id
        validateRoleIds(systemCode,roleIds, bo.getTenantCode());
        //1、删除旧用户关联的角色
        lambdaQueryWrapper.eq(TenantUserRolePO::getUserId, userId);
        lambdaQueryWrapper.eq(TenantUserRolePO::getSystemCode,systemCode);
        baseMapper.delete(lambdaQueryWrapper);

        //2、增加用户关联的角色
        if (CollectionUtils.isNotEmpty(roleIds)) {
            List<TenantUserRolePO> list = roleIds.stream().map(roleId -> createSysUserRole(roleId, userId, systemCode))
                    .collect(Collectors.toList());
            saveBatch(list);
        }
        //3、更新用户-角色缓存
        userRoleMenuRedisCache.updateUserRoles(bo);
    }

    @Override
    public List<SysRoleIdDTO> findByUserId(Long userId) {
        List<TenantUserRolePO> list = baseMapper
                .selectList(Wrappers.<TenantUserRolePO>lambdaQuery().eq(TenantUserRolePO::getUserId, userId));
        return list.stream().map(t -> SysRoleIdDTO.builder().roleId(t.getRoleId()).build())
                .collect(Collectors.toList());
    }

    @Override
    public Set<Long> findUserRoleByUserId(String systemCode, Long userId) {
        return userRoleMenuRedisCache.getUserRoleIds(systemCode, userId);
    }

    @Override
    public boolean permissionLogic(PermissionBO permission, String systemCode, Long userId) {
        //是否判断权限
        if (!permission.isRequired()) {
            return true;
        }
        Set<Long> roleIds = this.findUserRoleByUserId(systemCode, userId);
        String[] functionCodes = permission.getValue();
        //权限码逻辑关系 AND  OR
        Logical logical = permission.getLogical();
        if (functionCodes != null && functionCodes.length > 0) {
            for (String funCode : functionCodes) {
                boolean hasPermission = sysAdmUserService.hasPermission(roleIds, funCode);
                if (logical == Logical.AND) {
                    if (!hasPermission) {
                        return false;
                    }
                } else {
                    if (hasPermission) {
                        return true;
                    }
                }
            }
        }
        return permission.getLogical() == Logical.AND;
    }

    private TenantUserRolePO createSysUserRole(Long roleId, Long userId, String systemCode) {
        TenantUserRolePO po = new TenantUserRolePO();
        po.setRoleId(roleId);
        po.setUserId(userId);
        po.setSystemCode(systemCode);
        return po;
    }
}
