spring:
  application:
    name: dl-basic-service
  cloud:
    compatibility-verifier:
      enabled: false
  profiles:
    active: dev

# 日志配置
logging:
  level:
    root: INFO
    com.dl.basicservice: DEBUG

# Ribbon配置
ribbon:
  nacos:
    enabled: false

# Dubbo配置
dubbo:
  cloud:
    subscribed-services: qywx_app
  application:
    id: ${spring.application.name}
    qos-enable: false
  protocol:
    name: dubbo
    port: -1
  registry:
    address: spring-cloud://localhost
  consumer:
    check: false

# Swagger配置
swagger:
  enable: true

# SM4加密盐值
sm4:
  salt: da024ustkidtclu3

---
# 生产环境配置
spring:
  profiles: prod
  datasource:
    url: ************************************************************************************************************************************************************************************
    username: pelot
    password: Dingli@0301
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: r-bp1rgz6nqi3c07bxfnpd.redis.rds.aliyuncs.com
    port: 6379
    password: r-bp1rgz6nqi3c07bxfn:Dingli@0301
    database: 0
#  elasticsearch:
#    rest:
#      uris: http://prod-es-host:9200
#      username: ENC(encrypted-prod-es-username)
#      password: ENC(encrypted-prod-es-password)
#  cloud:
#    stream:
#      rocketmq:
#        binder:
#          name-server: prod-rocketmq-host:9876

# DL生产环境配置
dl:
  tencentcloud:
    defaultRegion: ap-guangzhou
    api:
      appId: prod-app-id
      secretId: 111111
      secretKey: 11111
    cos:
      bucketId: prod-bucket-id
      region: ap-guangzhou
    sms:
      smsSdkAppId: prod-sms-sdk-app-id
      smsRegion: ap-guangzhou
    captcha:
      web:
        captchaAppId: 1111
        appSecretKey: 1111
      mini:
        captchaAppId: 1111
        appSecretKey: 1111
  session:
    adm:
      expire: 7200
    app:
      expire: 7200
    toCust:
      expire: 7200
  redis:
    comName: dl-
    appName: basicservice
  store:
    policy: cos
  minio:
    minioHost: prod-minio-host
    minioPort: 9000
    mcUserName: minioadmin
    mcPwd: 1111
    bucketId: dl-basic-service-prod
    tslSecure: false
    assignTenantCode:

# 对象存储生产环境配置
objectStorage:
  cos:
    resourcePrefix: https://prod-bucket.cos.ap-guangzhou.myqcloud.com

# 财富工作台生产环境配置
wealth:
  workbench:
    domainSuffix: example.com
    domain: https://example.com
  magic:
    tenantspecial:
      loginurl:
        default: http://127.0.0.1/magic/login
