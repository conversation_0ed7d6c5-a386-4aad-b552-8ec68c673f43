package com.dl.basicservice.biz.common.util;

import org.springframework.util.StringUtils;

import java.util.regex.Pattern;

public class StringUtil {

    static final String CHINESE = "[\u0391-\uFFE5]";

    public static int lengthOfUTF8(String value) {
        int i = 0;
        if (!StringUtils.hasLength(value)) {
            return i;
        }
        for (char c : value.toCharArray()) {
            if (Pattern.matches(CHINESE, String.valueOf(c))) {
                i += 3;
            } else {
                i += 1;
            }
        }
        return i;
    }

}
