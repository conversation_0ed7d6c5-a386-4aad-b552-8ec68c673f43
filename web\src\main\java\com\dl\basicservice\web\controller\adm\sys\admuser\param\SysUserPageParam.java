package com.dl.basicservice.web.controller.adm.sys.admuser.param;

import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Size;

/**
 * @describe: SysUserPageParam
 * @author: zhousx
 * @date: 2022/7/5 14:20
 */
@Data
@ApiModel("员工搜索条件")
public class SysUserPageParam extends AbstractPageParam {
    @ApiModelProperty("账号")
    @Size(max = 50)
    private String account;

    @ApiModelProperty("姓名")
    @Size(max = 50)
    private String userName;

    @ApiModelProperty("用户状态 1启用 2停用")
    private Integer status;

    @ApiModelProperty("用户账号类型 0 B端用户 1 小程序端用户 默认为B端用户")
    private Integer accountType = 0;
}
