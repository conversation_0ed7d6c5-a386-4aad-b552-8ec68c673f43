package com.dl.basicservice.web.controller.adm.sys.menu;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.dal.sys.function.po.TenantRoleFunctionPO;
import com.dl.basicservice.biz.dal.sys.menu.po.TenantRoleMenuPO;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.manager.sys.role.enums.RoleTypeEnum;
import com.dl.basicservice.biz.manager.sys.function.TenantRoleFunctionManager;
import com.dl.basicservice.biz.manager.sys.function.dto.FunctionDTO;
import com.dl.basicservice.biz.manager.sys.menu.PrivilegeMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.TenantMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.TenantRoleMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleIdsMenuParamBO;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleMenuParamBO;
import com.dl.basicservice.biz.manager.sys.menu.bo.TenantMenuSaveParamBO;
import com.dl.basicservice.biz.manager.sys.menu.dto.MenuDTO;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.web.controller.adm.sys.menu.param.RoleMenuParam;
import com.dl.basicservice.web.controller.adm.sys.menu.param.RoleMenuSaveParam;
import com.dl.basicservice.web.controller.adm.sys.menu.param.SysMenuParam;
import com.dl.basicservice.web.controller.adm.sys.menu.param.TenantMenuSaveParam;
import com.dl.basicservice.web.controller.adm.sys.menu.vo.FunctionVO;
import com.dl.basicservice.web.controller.adm.sys.menu.vo.MenuObjectVO;
import com.dl.basicservice.web.controller.adm.sys.menu.vo.MenuVO;
import com.dl.basicservice.web.controller.adm.base.AbstractController;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/menu")
@Api("菜单功能")
@Validated
public class MenuController extends AbstractController {
    @Autowired
    private PrivilegeMenuManager privilegeMenuManager;
    @Autowired
    private TenantRoleMenuManager tenantRoleMenuManager;
    @Autowired
    private TenantRoleFunctionManager tenantRoleFunctionManager;
    @Autowired
    private TenantMenuManager tenantMenuManager;
    @Autowired
    private TenantRoleManager tenantRoleManager;
    @Autowired
    private TenantInfoManager tenantInfoManager;

    @PostMapping("/role/list")
    //@Permission("adm:rolemenu:list") 列表查询不再校验权限
    @ApiOperation("查询租户下角色的拥有的菜单列表")
    public ResultModel<MenuObjectVO> tenantlist(@RequestBody @Validated RoleMenuParam p) {
        String tenantCode = getTenantCode();
        Assert.isTrue(StringUtils.isNotBlank(tenantCode), "租户id参数为空");

        MenuObjectVO vo = new MenuObjectVO();

        //租户下的系统菜单列表
        List<MenuVO> sysmenulist =  this.getsyslist(tenantCode,getSystemCode());
        //过滤租户拥有权限的菜单列表
        if (CollectionUtils.isEmpty(sysmenulist)) {
            return ResultModel.success(vo);
        }
        List<MenuVO> menuList = this.gettenantlist(sysmenulist);
        //重置权限标识
        this.restOwnerTo0(menuList);
        //没有roleId代表取租户下全部的菜单以及权限点位
        if (Objects.isNull(p.getRoleId())){
            vo.setAll(menuList);
            return ResultModel.success(vo);
        }

        //第2步：获取当前角色拥有的菜单和功能列表
        List<TenantRoleMenuPO> rmlist = tenantRoleMenuManager.list(Wrappers.lambdaQuery(TenantRoleMenuPO.class).eq(TenantRoleMenuPO::getRoleId,p.getRoleId()));
        Map<String, String> menuMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rmlist)) {
            rmlist.forEach(m -> {
                String menuId = String.valueOf(m.getMenuId());
                menuMap.put(menuId,menuId);
            });
        }

        List<TenantRoleFunctionPO> rflist = tenantRoleFunctionManager.list(Wrappers.lambdaQuery(TenantRoleFunctionPO.class).eq(TenantRoleFunctionPO::getRoleId,p.getRoleId()));
        Map<String, String> functionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rflist)) {
            rflist.forEach(mf -> {
                String functionId = String.valueOf(mf.getFunctionId());
                functionMap.put(functionId,functionId);
            });
        }

        //第3步：根据当前角色拥有的菜单和功能列表，重置权限标识为1
        this.restOwnerTo1(menuList,menuMap,functionMap);
        vo.setAll(menuList);

        //获取有分配权限的菜单id列表
        if(CollectionUtils.isNotEmpty(menuList)){
            List<String> owners = new ArrayList<>();
            this.getOwners(owners,menuList);
            vo.setOwners(owners);
        }

        return ResultModel.success(vo);
    }

    @PostMapping("/role/save")
    //@Permission("adm:rolemenu:save")
    @ApiOperation("某角色下的角色-菜单,角色-功能配置")
    public ResultModel<Boolean> saveRoleMenu(@Validated @RequestBody RoleMenuSaveParam p) {
        RoleMenuParamBO bo= new RoleMenuParamBO();
        bo.setRoleId(Long.valueOf(p.getRoleId()));
        bo.setMenuIds(p.getMenuIds().stream().map(Long::parseLong).collect(Collectors.toList()));
        bo.setLoginUserId(getUserId());
        bo.setFunctionIds(p.getFunctionIds().stream().map(Long::parseLong).collect(Collectors.toList()));
        bo.setSystemCode(getSystemCode());
        Boolean f = tenantRoleMenuManager.saveRoleMenu(bo);
        return ResultModel.success(f);
    }

    @PostMapping("/tenant/save")
    //@Permission("adm:tenantmenu:save")
    @ApiOperation("保存指定租户的菜单和功能项")
    public ResultModel<Boolean> saveTenantMenu(@Validated @RequestBody TenantMenuSaveParam p) {
        Assert.isTrue(StringUtils.isNotBlank(p.getTenantCode()), "租户id参数为空");

        List<Long> menuIds = p.getMenuIds().stream().map(Long::parseLong).collect(Collectors.toList());
        List<Long> functionIds = p.getFunctionIds().stream().map(Long::parseLong).collect(Collectors.toList());

        // 给租户赋权
        TenantMenuSaveParamBO bo= new TenantMenuSaveParamBO();
        bo.setTenantCode(p.getTenantCode());
        bo.setMenuIds(menuIds);
        bo.setLoginUserId(getUserId());
        bo.setFunctionIds(functionIds);
        bo.setSystemCode(getSystemCode());
        tenantMenuManager.saveTenantMenu(bo);

        //获取租户下的角色列表
        LambdaQueryWrapper<TenantRolePO> tmquery = new LambdaQueryWrapper<>();
        tmquery.eq(StringUtils.isNotBlank(p.getTenantCode()),TenantRolePO::getTenantCode,p.getTenantCode());
        tmquery.eq(TenantRolePO::getSystemCode, getSystemCode());
        List<TenantRolePO> roles = tenantRoleManager.list(tmquery);
        if(CollectionUtils.isNotEmpty(roles)){
            // 给超级管理员角色赋权
            Optional<TenantRolePO> opt = roles.stream().filter(f -> RoleTypeEnum.SUPER_ADMIN.getCode().equals(f.getRoleType())).findFirst();
            if(opt.isPresent()){
                TenantRolePO po = opt.get();
                RoleMenuParamBO roleMenuParamBO= new RoleMenuParamBO();
                roleMenuParamBO.setRoleId(po.getRoleId());
                roleMenuParamBO.setMenuIds(menuIds);
                roleMenuParamBO.setLoginUserId(getUserId());
                roleMenuParamBO.setFunctionIds(functionIds);
                roleMenuParamBO.setSystemCode(getSystemCode());
                tenantRoleMenuManager.saveRoleMenu(roleMenuParamBO);
            }

            //非超级管理员角色过滤已去掉的权限
           List<Long> roleIds = roles.stream().filter(f -> !RoleTypeEnum.SUPER_ADMIN.getCode().equals(f.getRoleType())).map(r->r.getRoleId()).collect(Collectors.toList());
           if(CollectionUtils.isNotEmpty(roleIds)){
               RoleIdsMenuParamBO roleIdsMenuParamBO = new RoleIdsMenuParamBO();
               roleIdsMenuParamBO.setRoleIds(roleIds);
               roleIdsMenuParamBO.setMenuIds(menuIds);
               roleIdsMenuParamBO.setLoginUserId(getUserId());
               roleIdsMenuParamBO.setFunctionIds(functionIds);
               roleIdsMenuParamBO.setSystemCode(getSystemCode());
               tenantRoleMenuManager.delRoleMenu(roleIdsMenuParamBO);
           }
        }

        return ResultModel.success(Boolean.TRUE);
    }

    @PostMapping("/tenant/list")
    //@Permission("adm:tenantmenu:list")
    @ApiOperation("查询可分配给租户的系统菜单列表")
    public ResultModel<MenuObjectVO> syslist(@RequestBody @Validated SysMenuParam p) {
        Assert.isTrue(StringUtils.isNotBlank(p.getTenantCode()), "租户编码为空");
        //此菜单只有是定力科技的租户，并且当前用户是超级管理员才能有权限
        SysAdmUserDTO user = getCurrentDetail();
        Assert.isTrue(user != null, "无法获取当前用户信息");
        Assert.isTrue(("DL".equals(user.getTenantCode()) && Objects.equals(user.getIsSuperAdm(), 1)) || Objects.equals(
                user.getIsSuperAdm(), 9), "当前用户非定力超级管理员");
        SysTenantInfoPO currentTenant =
                tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO).eq(SysTenantInfoPO::getTenantCode, getTenantCode()).one();
        Assert.isTrue(Objects.equals(Const.ONE, currentTenant.getIsProvider()), "您无权修改租户");
        //获取系统菜单列表
        List<MenuVO> all = this.getsyslist(p.getTenantCode(),getSystemCode());
        MenuObjectVO vo = new MenuObjectVO();
        vo.setAll(all);
        if (CollectionUtils.isNotEmpty(all)) {
            List<String> owners = new ArrayList<>();
            this.getOwners(owners, all);
            vo.setOwners(owners);
        }
        return ResultModel.success(vo);
    }

    //获取有分配权限的菜单id列表
    private void getOwners(List<String> owners, List<MenuVO> list) {
        list.stream().filter(entity -> entity.getOwner() == 1).forEach(menu->{
            owners.add(String.valueOf(menu.getMenuId()));
            List<FunctionVO> functions = menu.getFunctions();
            if(CollectionUtils.isNotEmpty(functions)){
                functions.stream().filter(f -> f.getOwner() == 1).forEach(function->{
                    //为了前端处理方便，跟菜单id区分，加F_字母前缀
                    owners.add("F_"+String.valueOf(function.getFunctionId()));
                });
            }
            this.getOwners(owners,menu.getChildren());
        });
    }

    //根据当前角色拥有的菜单和功能列表，重置权限标识为1
    private void restOwnerTo1(List<MenuVO> menuList,Map<String, String> menuMap,Map<String, String> functionMap){
        if(CollectionUtils.isNotEmpty(menuList) && MapUtils.isNotEmpty(menuMap)){
            menuList.forEach(m -> {
                String menuId = String.valueOf(m.getMenuId());
                if(menuMap.containsKey(menuId)){
                    m.setOwner(1);
                }
                List<FunctionVO> functions = m.getFunctions();
                if(CollectionUtils.isNotEmpty(functions) && MapUtils.isNotEmpty(functionMap)){
                    functions.forEach(f -> {
                        String functionId = String.valueOf(f.getFunctionId());
                        if(functionMap.containsKey(functionId)){
                            f.setOwner(1);
                        }
                    });
                }
                restOwnerTo1(m.getChildren(),menuMap,functionMap);
            });
        }
    }

    //把分配权限标识重新设置为未分配，后面再根据角色是否有权限再重新打上1
    private void restOwnerTo0(List<MenuVO> menuList){
        if(CollectionUtils.isNotEmpty(menuList)){
            menuList.forEach(m -> {
                m.setOwner(0);
                List<FunctionVO> functions = m.getFunctions();
                if(CollectionUtils.isNotEmpty(functions)){
                    functions.forEach(f -> f.setOwner(0));
                }
                restOwnerTo0(m.getChildren());
            });
        }
    }

    //过滤租户拥有权限的菜单列表
    private List<MenuVO> gettenantlist(List<MenuVO> list) {
        return list.stream().filter(entity -> entity.getOwner() == 1).peek(menu -> {
            List<FunctionVO> functions = menu.getFunctions();
            if(CollectionUtils.isNotEmpty(functions)){
                List<FunctionVO> result = new ArrayList<>();
                for (FunctionVO f : functions) {
                    if (f.getOwner() == 1) {
                        result.add(f);
                    }
                }
                functions = result;
                menu.setFunctions(functions);
            }
            menu.setChildren(this.gettenantlist(menu.getChildren()));
        }).collect(Collectors.toList());
    }

    //获取租户系统菜单列表
    private List<MenuVO> getsyslist(String tenantCode,String systemCode){
        //获取系统菜单列表
        List<MenuDTO> list = privilegeMenuManager.listMenusAndFunctions(tenantCode, systemCode);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        // 组装成父子的树形结构
        return this.getChildrens(list);
    }

    // 递归查找所有菜单的子菜单
    private List<MenuVO> getChildrens(List<MenuDTO> list) {
        return list.stream().map(this::apply).collect(Collectors.toList());
    }

    private MenuVO copyMenus(MenuDTO m){
        MenuVO vo = new MenuVO();
        vo.setDisable(m.getDisable());
        vo.setMenuId(String.valueOf(m.getMenuId()));
        vo.setIcon(m.getIcon());
        vo.setLevel(m.getLevel());
        vo.setName(m.getName());
        vo.setOwner(m.getOwner());
        vo.setSort(m.getSort());
        vo.setParentId(String.valueOf(m.getParentId()));
        vo.setUrl(m.getUrl());

        List<FunctionDTO> functions = m.getFunctions();
        if(CollectionUtils.isNotEmpty(functions)){
            functions.forEach(f -> {
                FunctionVO functionVO = new FunctionVO();
                functionVO.setFunctionId(String.valueOf(f.getFunctionId()));
                functionVO.setOwner(f.getOwner());
                functionVO.setIcon(f.getIcon());
                functionVO.setName(f.getName());
                functionVO.setSort(f.getSort());
                vo.getFunctions().add(functionVO);
            });
        }
        return vo;
    }

    private MenuVO apply(MenuDTO menu) {
        MenuVO vo = this.copyMenus(menu);
        vo.setChildren(getChildrens(menu.getChildren()));
        return vo;
    }
}
