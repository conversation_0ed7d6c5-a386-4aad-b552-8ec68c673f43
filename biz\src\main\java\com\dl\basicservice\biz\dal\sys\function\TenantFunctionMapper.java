package com.dl.basicservice.biz.dal.sys.function;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.basicservice.biz.common.annotation.BaseDao;
import com.dl.basicservice.biz.dal.sys.function.dto.TenantFunctionDTO;
import com.dl.basicservice.biz.dal.sys.function.po.TenantFunctionPO;

import java.util.List;

@BaseDao
public interface TenantFunctionMapper extends BaseMapper<TenantFunctionPO> {
    /**
     * 获取所有租户功能列表
     *
     * @return
     */
    List<TenantFunctionDTO> listFunctions();
}
