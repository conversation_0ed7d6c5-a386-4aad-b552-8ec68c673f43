package com.dl.basicservice.biz.manager.sys.role.bo;

import com.dl.basicservice.biz.common.validation.ValidateStrategy;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.validation.constraints.Size;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("角色信息")
public class RoleParamBO {

    @NotNull(groups = ValidateStrategy.update.class)
    @Min(value = 1, groups = ValidateStrategy.update.class)
    @ApiModelProperty("角色ID")
    private Long roleId;

    @Size(min = 2, max = 20)
    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色类型")
    private String roleType;

    @Null
    @ApiModelProperty(hidden = true)
    private String tenantCode;

}

