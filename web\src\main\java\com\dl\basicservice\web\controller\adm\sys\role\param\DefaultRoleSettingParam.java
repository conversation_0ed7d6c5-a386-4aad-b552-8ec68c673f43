package com.dl.basicservice.web.controller.adm.sys.role.param;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-03 10:28
 */
@Data
@ApiModel("默认角色设置参数")
public class DefaultRoleSettingParam {

    @NotNull(message = "角色ID不能为空")
    @ApiModelProperty("角色ID")
    private Long roleId;

}
