[32m2025-08-20 17:40:43.539[0;39m [background-preinit] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mo.h.validator.internal.util.Version     [0;39m [2m:[0;39m [TID: N/A]  HV000001: Hibernate Validator 6.1.7.Final
[32m2025-08-20 17:40:44.018[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mptablePropertiesBeanFactoryPostProcessor[0;39m [2m:[0;39m [TID: N/A]  Post-processing PropertySource instances
[32m2025-08-20 17:40:44.103[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[32m2025-08-20 17:40:44.104[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource bootstrap [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:44.105[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:44.105[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[32m2025-08-20 17:40:44.106[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-20 17:40:44.106[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:44.106[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/bootstrap.yaml] (document #0) [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:44.106[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource moduleDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:44.182[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m [2m:[0;39m [TID: N/A]  Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[32m2025-08-20 17:40:44.351[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.r.DefaultLazyPropertyResolver     [0;39m [2m:[0;39m [TID: N/A]  Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[32m2025-08-20 17:40:44.353[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.d.DefaultLazyPropertyDetector     [0;39m [2m:[0;39m [TID: N/A]  Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[32m2025-08-20 17:40:44.933[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.c.c.impl.LocalConfigInfoProcessor [0;39m [2m:[0;39m [TID: N/A]  LOCAL_SNAPSHOT_PATH:C:\Users\<USER>\nacos\config
[32m2025-08-20 17:40:44.975[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.nacos.client.config.impl.Limiter    [0;39m [2m:[0;39m [TID: N/A]  limitTime:5.0
[31m2025-08-20 17:40:46.005[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.c.config.http.ServerHttpAgent     [0;39m [2m:[0;39m [TID: N/A]  [NACOS SocketTimeoutException httpGet] currentServerAddr:http://localhost:8848， err : connect timed out
[31m2025-08-20 17:40:47.013[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.c.config.http.ServerHttpAgent     [0;39m [2m:[0;39m [TID: N/A]  [NACOS SocketTimeoutException httpGet] currentServerAddr:http://localhost:8848， err : connect timed out
[31m2025-08-20 17:40:48.022[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.c.config.http.ServerHttpAgent     [0;39m [2m:[0;39m [TID: N/A]  [NACOS SocketTimeoutException httpGet] currentServerAddr:http://localhost:8848， err : connect timed out
[31m2025-08-20 17:40:48.022[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.c.config.http.ServerHttpAgent     [0;39m [2m:[0;39m [TID: N/A]  no available server
[31m2025-08-20 17:40:48.026[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.client.config.impl.ClientWorker   [0;39m [2m:[0;39m [TID: N/A]  [fixed-localhost_8848] [sub-server] get server config exception, dataId=dl-basic-service, group=DEFAULT_GROUP, tenant=

java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:141)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:52)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:98)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:623)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:367)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.dl.basicservice.war.Application.main(Application.java:20)

[33m2025-08-20 17:40:48.027[0;39m [main] [33m WARN[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.client.config.NacosConfigService  [0;39m [2m:[0;39m [TID: N/A]  [fixed-localhost_8848] [get-config] get from server error, dataId=dl-basic-service, group=DEFAULT_GROUP, tenant=, msg=ErrCode:500, ErrMsg:no available server
[33m2025-08-20 17:40:48.028[0;39m [main] [33m WARN[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.client.config.NacosConfigService  [0;39m [2m:[0;39m [TID: N/A]  [fixed-localhost_8848] [get-config] get snapshot ok, dataId=dl-basic-service, group=DEFAULT_GROUP, tenant=, config=
[33m2025-08-20 17:40:48.029[0;39m [main] [33m WARN[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m [2m:[0;39m [TID: N/A]  Ignore the empty nacos configuration and get it based on dataId[dl-basic-service] & group[DEFAULT_GROUP]
[31m2025-08-20 17:40:49.046[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.c.config.http.ServerHttpAgent     [0;39m [2m:[0;39m [TID: N/A]  [NACOS SocketTimeoutException httpGet] currentServerAddr:http://localhost:8848， err : connect timed out
[31m2025-08-20 17:40:50.055[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.c.config.http.ServerHttpAgent     [0;39m [2m:[0;39m [TID: N/A]  [NACOS SocketTimeoutException httpGet] currentServerAddr:http://localhost:8848， err : connect timed out
[31m2025-08-20 17:40:51.063[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.c.config.http.ServerHttpAgent     [0;39m [2m:[0;39m [TID: N/A]  [NACOS SocketTimeoutException httpGet] currentServerAddr:http://localhost:8848， err : connect timed out
[31m2025-08-20 17:40:51.064[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.c.config.http.ServerHttpAgent     [0;39m [2m:[0;39m [TID: N/A]  no available server
[31m2025-08-20 17:40:51.064[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.client.config.impl.ClientWorker   [0;39m [2m:[0;39m [TID: N/A]  [fixed-localhost_8848] [sub-server] get server config exception, dataId=dl-basic-service.properties, group=DEFAULT_GROUP, tenant=

java.net.ConnectException: no available server
	at com.alibaba.nacos.client.config.http.ServerHttpAgent.httpGet(ServerHttpAgent.java:134)
	at com.alibaba.nacos.client.config.http.MetricsHttpAgent.httpGet(MetricsHttpAgent.java:51)
	at com.alibaba.nacos.client.config.impl.ClientWorker.getServerConfig(ClientWorker.java:274)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfigInner(NacosConfigService.java:155)
	at com.alibaba.nacos.client.config.NacosConfigService.getConfig(NacosConfigService.java:98)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.loadNacosData(NacosPropertySourceBuilder.java:85)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceBuilder.build(NacosPropertySourceBuilder.java:73)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosPropertySource(NacosPropertySourceLocator.java:199)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadNacosDataIfPresent(NacosPropertySourceLocator.java:186)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.loadApplicationConfiguration(NacosPropertySourceLocator.java:144)
	at com.alibaba.cloud.nacos.client.NacosPropertySourceLocator.locate(NacosPropertySourceLocator.java:103)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:52)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:98)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:623)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:367)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:311)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1247)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1236)
	at com.dl.basicservice.war.Application.main(Application.java:20)

[33m2025-08-20 17:40:51.065[0;39m [main] [33m WARN[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.client.config.NacosConfigService  [0;39m [2m:[0;39m [TID: N/A]  [fixed-localhost_8848] [get-config] get from server error, dataId=dl-basic-service.properties, group=DEFAULT_GROUP, tenant=, msg=ErrCode:500, ErrMsg:no available server
[33m2025-08-20 17:40:51.065[0;39m [main] [33m WARN[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.client.config.NacosConfigService  [0;39m [2m:[0;39m [TID: N/A]  [fixed-localhost_8848] [get-config] get snapshot ok, dataId=dl-basic-service.properties, group=DEFAULT_GROUP, tenant=, config=
[33m2025-08-20 17:40:51.065[0;39m [main] [33m WARN[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.c.n.c.NacosPropertySourceBuilder    [0;39m [2m:[0;39m [TID: N/A]  Ignore the empty nacos configuration and get it based on dataId[dl-basic-service.properties] & group[DEFAULT_GROUP]
[32m2025-08-20 17:40:51.103[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mcom.dl.basicservice.war.Application     [0;39m [2m:[0;39m [TID: N/A]  No active profile set, falling back to default profiles: default
[32m2025-08-20 17:40:58.632[0;39m [main] [32mDEBUG[0;39m [35m22712[0;39m [2m---[0;39m [36morg.apache.ibatis.logging.LogFactory    [0;39m [2m:[0;39m [TID: N/A]  Logging initialized using 'class org.apache.ibatis.logging.slf4j.Slf4jImpl' adapter.
[32m2025-08-20 17:40:58.744[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mptablePropertiesBeanFactoryPostProcessor[0;39m [2m:[0;39m [TID: N/A]  Post-processing PropertySource instances
[32m2025-08-20 17:40:58.756[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource bootstrapProperties-dl-basic-service.properties,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[32m2025-08-20 17:40:58.756[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource bootstrapProperties-dl-basic-service,DEFAULT_GROUP [org.springframework.cloud.bootstrap.config.BootstrapPropertySource] to EncryptableEnumerablePropertySourceWrapper
[32m2025-08-20 17:40:58.756[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource configurationProperties [org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource] to AOP Proxy
[32m2025-08-20 17:40:58.756[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource servletConfigInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-20 17:40:58.756[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource servletContextInitParams [org.springframework.core.env.PropertySource$StubPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-20 17:40:58.756[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:58.756[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
[32m2025-08-20 17:40:58.757[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-20 17:40:58.757[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:58.757[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource applicationConfig: [classpath:/config/application.properties] [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:58.757[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource springCloudDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:58.757[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
[32m2025-08-20 17:40:58.757[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.EncryptablePropertySourceConverter[0;39m [2m:[0;39m [TID: N/A]  Converting PropertySource moduleDefaultProperties [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
[32m2025-08-20 17:40:58.886[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.filter.DefaultLazyPropertyFilter  [0;39m [2m:[0;39m [TID: N/A]  Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
[32m2025-08-20 17:40:59.418[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.r.DefaultLazyPropertyResolver     [0;39m [2m:[0;39m [TID: N/A]  Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
[32m2025-08-20 17:40:59.418[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mc.u.j.d.DefaultLazyPropertyDetector     [0;39m [2m:[0;39m [TID: N/A]  Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
[33m2025-08-20 17:41:02.026[0;39m [main] [33m WARN[0;39m [35m22712[0;39m [2m---[0;39m [36mc.d.f.scanner.ClassPathClientScanner    [0;39m [2m:[0;39m [TID: N/A]  [Forest] No Forest client is found in package '[com.dl.basicservice.war]'.
[32m2025-08-20 17:41:03.022[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mo.a.coyote.http11.Http11NioProtocol     [0;39m [2m:[0;39m [TID: N/A]  Initializing ProtocolHandler ["http-nio-8080"]
[32m2025-08-20 17:41:03.023[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m [TID: N/A]  Starting service [Tomcat]
[32m2025-08-20 17:41:03.023[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36morg.apache.catalina.core.StandardEngine [0;39m [2m:[0;39m [TID: N/A]  Starting Servlet engine: [Apache Tomcat/9.0.46]
[32m2025-08-20 17:41:03.318[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mo.a.c.c.C.[Tomcat].[localhost].[/]      [0;39m [2m:[0;39m [TID: N/A]  Initializing Spring embedded WebApplicationContext
[31m2025-08-20 17:41:03.823[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mo.s.b.web.embedded.tomcat.TomcatStarter [0;39m [2m:[0;39m [TID: N/A]  Error starting Tomcat context. Exception: org.springframework.beans.factory.UnsatisfiedDependencyException. Message: Error creating bean with name 'webConfig': Unsatisfied dependency expressed through field 'authenticationInterceptor'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'authenticationInterceptor': Unsatisfied dependency expressed through field 'tenantUserRoleManager'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tenantUserRoleManagerImpl': Unsatisfied dependency expressed through field 'baseMapper'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'tenantUserRoleMapper' defined in file [C:\Users\<USER>\IdeaProjects\gitee\szr\dl-basic-service\biz\target\classes\com\dl\basicservice\biz\dal\sys\user\TenantUserRoleMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Unsatisfied dependency expressed through method 'sqlSessionFactory' parameter 0; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dataSource' defined in class path resource [org/springframework/boot/autoconfigure/jdbc/DataSourceConfiguration$Hikari.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [com.zaxxer.hikari.HikariDataSource]: Factory method 'dataSource' threw exception; nested exception is org.springframework.boot.autoconfigure.jdbc.DataSourceProperties$DataSourceBeanCreationException: Failed to determine a suitable driver class
[32m2025-08-20 17:41:03.901[0;39m [main] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mo.apache.catalina.core.StandardService  [0;39m [2m:[0;39m [TID: N/A]  Stopping service [Tomcat]
[32m2025-08-20 17:41:03.944[0;39m [NettyClientSelector_1] [32m INFO[0;39m [35m22712[0;39m [2m---[0;39m [36mRocketmqRemoting                        [0;39m [2m:[0;39m [TID: N/A]  closeChannel: close the connection to remote address[] result: true
[31m2025-08-20 17:41:04.024[0;39m [main] [31mERROR[0;39m [35m22712[0;39m [2m---[0;39m [36mo.s.b.d.LoggingFailureAnalysisReporter  [0;39m [2m:[0;39m [TID: N/A]  

***************************
APPLICATION FAILED TO START
***************************

Description:

Failed to configure a DataSource: 'url' attribute is not specified and no embedded datasource could be configured.

Reason: Failed to determine a suitable driver class


Action:

Consider the following:
	If you want an embedded database (H2, HSQL or Derby), please put it on the classpath.
	If you have database settings to be loaded from a particular profile you may need to activate it (no profiles are currently active).

[33m2025-08-20 17:41:07.008[0;39m [Thread-6] [33m WARN[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [TID: N/A]  [HttpClientBeanHolder] Start destroying common HttpClient
[33m2025-08-20 17:41:07.009[0;39m [Thread-6] [33m WARN[0;39m [35m22712[0;39m [2m---[0;39m [36mc.a.n.common.http.HttpClientBeanHolder  [0;39m [2m:[0;39m [TID: N/A]  [HttpClientBeanHolder] Destruction of the end
