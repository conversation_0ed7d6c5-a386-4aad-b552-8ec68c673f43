package com.dl.basicservice.web.controller.internal.adm.sys.userbind;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dl.basicservice.biz.common.enums.CommonCode;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.biz.manager.sys.userbind.TenantAdmUserBindManager;
import com.dl.basicservice.biz.manager.sys.userbind.dto.TenantAdmUserBindDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.userbind.request.InternalTenantUserBindBatchQueryParam;
import com.dl.basicservice.web.controller.internal.adm.sys.userbind.request.InternalTenantUserBindQueryParam;
import com.dl.basicservice.web.controller.internal.adm.sys.userbind.resp.InternalTenantUserSimpleDTO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-06 15:28
 */
@RequestMapping("/internal/tenantuserbind")
@RestController
public class InternalTenantUserBindController {
    private static final Logger LOGGER = LoggerFactory.getLogger(InternalTenantUserBindController.class);

    @Resource
    private TenantAdmUserBindManager tenantAdmUserBindManager;

    @Resource
    private SysAdmUserService sysAdmUserService;

    @ApiOperation("根据外部用户id查询绑定的用户信息")
    @PostMapping("/querybyextuserid")
    public ResultModel<InternalTenantUserSimpleDTO> queryByExtUserId(@RequestBody @Validated InternalTenantUserBindQueryParam param) {
        Assert.isTrue(StringUtils.isNotBlank(param.getExtUserId()), "外部用户id不能为空");
        TenantAdmUserBindDTO tenantAdmUserBindDTO = tenantAdmUserBindManager
                .queryByExtUserId(param.getTenantCode(), param.getExtUserId());
        if (Objects.isNull(tenantAdmUserBindDTO)) {
            return ResultModel.success(null);
        }

        SysAdmUserDTO sysAdmUserDTO = sysAdmUserService.findUserDetailFromCache(tenantAdmUserBindDTO.getUserId());
        if (Objects.isNull(sysAdmUserDTO)) {
            LOGGER.error("数据有误!用户不存在!,param:{},,,tenantAdmUserBindDTO:{}", JSONUtil.toJsonStr(param),
                    JSONUtil.toJsonStr(tenantAdmUserBindDTO));
            return ResultModel.error(CommonCode.DATA_NOT_FOUND.getCode(), "数据有误!用户不存在!");
        }

        InternalTenantUserSimpleDTO simpleDTO = new InternalTenantUserSimpleDTO();
        simpleDTO.setUserId(sysAdmUserDTO.getUserId());
        simpleDTO.setUserName(sysAdmUserDTO.getUserName());
        return ResultModel.success(simpleDTO);
    }

    @ApiOperation("根据用户id查询外部用户id")
    @PostMapping("/querybyuserid")
    public ResultModel<String> queryByUserId(@RequestBody @Validated InternalTenantUserBindQueryParam param) {
        Assert.notNull(param.getUserId(), "用户id不能为空");
        TenantAdmUserBindDTO tenantAdmUserBindDTO = tenantAdmUserBindManager
                .queryByUserId(param.getTenantCode(), param.getUserId());
        if (Objects.isNull(tenantAdmUserBindDTO)) {
            return ResultModel.success(null);
        }
        return ResultModel.success(tenantAdmUserBindDTO.getExtUserId());
    }

    @ApiOperation("根据用户id集合批量查询外部用户id")
    @PostMapping("/batchquerybyuserids")
    public ResultModel<Map<Long, String>> batchQueryByUserIds(
            @RequestBody @Validated InternalTenantUserBindBatchQueryParam param) {
        Assert.notNull(param.getUserIds(), "用户id列表不能为空");
        return ResultModel.success(tenantAdmUserBindManager.queryByUserIds(param.getTenantCode(), param.getUserIds()));
    }

}
