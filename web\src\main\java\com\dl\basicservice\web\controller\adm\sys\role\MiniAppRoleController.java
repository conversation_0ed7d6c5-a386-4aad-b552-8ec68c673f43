package com.dl.basicservice.web.controller.adm.sys.role;

import com.dl.basicservice.biz.common.annotation.Permission;
import com.dl.basicservice.biz.manager.sys.menu.TenantRoleMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.bo.MinAppRoleMenuParamBO;
import com.dl.basicservice.web.controller.adm.sys.role.param.MinAppRoleParam;
import com.dl.basicservice.web.controller.adm.base.AbstractController;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/miniapprole")
@Api("小程序B端角色管理")
public class MiniAppRoleController extends AbstractController {

    @Autowired
    private TenantRoleMenuManager tenantRoleMenuManager;

    //@Permission("adm:minniapp:role:add")
    @PostMapping("/add")
    @ApiOperation("添加角色并配置权限")
    public ResultModel<String> add(@Validated @RequestBody MinAppRoleParam param) {
        MinAppRoleMenuParamBO minAppRoleMenuParamBO = new MinAppRoleMenuParamBO();
        minAppRoleMenuParamBO.setFunctionIds(param.getFunctionIds());
        minAppRoleMenuParamBO.setMenuIds(param.getMenuIds());
        minAppRoleMenuParamBO.setName(param.getName());
        minAppRoleMenuParamBO.setTenantCode(getTenantCode());
        minAppRoleMenuParamBO.setSystemCode(getSystemCode());
        return ResultModel.success(tenantRoleMenuManager.addMiniAppRoleMenu(minAppRoleMenuParamBO));
    }

    //@Permission("adm:minniapp:role:edit")
    @PostMapping("/edit")
    @ApiOperation("修改角色并配置权限")
    public ResultModel<String> edit(@Validated @RequestBody MinAppRoleParam param) {
        MinAppRoleMenuParamBO minAppRoleMenuParamBO = new MinAppRoleMenuParamBO();
        minAppRoleMenuParamBO.setRoleId(param.getRoleId());
        minAppRoleMenuParamBO.setFunctionIds(param.getFunctionIds());
        minAppRoleMenuParamBO.setMenuIds(param.getMenuIds());
        minAppRoleMenuParamBO.setName(param.getName());
        minAppRoleMenuParamBO.setTenantCode(getTenantCode());
        minAppRoleMenuParamBO.setSystemCode(getSystemCode());
        return ResultModel.success(tenantRoleMenuManager.editMiniAppRoleMenu(minAppRoleMenuParamBO));
    }

}
