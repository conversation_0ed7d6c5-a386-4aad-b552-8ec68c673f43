package com.dl.basicservice.biz.manager.sys.oplog.bo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.dl.framework.core.controller.param.AbstractPageParam;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 15:29
 */
@Data
public class OpLogPageBO extends AbstractPageParam {

    @ApiModelProperty(value = "接入业务的编码", required = true)
    private String bizCode;

    @ApiModelProperty(value = "租户编码", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "操作对象")
    private String opObject;

    @ApiModelProperty(value = "操作对象主键")
    private String opKey;

    @ApiModelProperty(value = "操作类型 自定义（add、update、delete）")
    private String opType;

    @ApiModelProperty("操作人id")
    private String opUserId;

    @ApiModelProperty("操作开始时间")
    private Date opSince;

    @ApiModelProperty("操作结束时间")
    private Date opUntil;

}
