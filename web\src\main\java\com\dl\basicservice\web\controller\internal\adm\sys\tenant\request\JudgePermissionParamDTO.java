package com.dl.basicservice.web.controller.internal.adm.sys.tenant.request;

import com.dl.basicservice.biz.common.annotation.Logical;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-17 16:12
 */
@Data
public class JudgePermissionParamDTO {

    /**
     * @return 权限编码
     */
    private String[] value;

    /**
     * 是否需要校验
     */
    private boolean required;

    /**
     * 权限编码是否需要同时拥有
     */
    private Logical logical;

    @NotNull(message = "用户id不能为空")
    private Long userId;
}
