package com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 10:38
 */
@Data
public class AdmUserDTO implements Serializable {
    private static final long serialVersionUID = 2576100699225459794L;

    public Long id;

    /**
     * userid
     */
    private Long userId;

    /**
     * 登录账号
     */
    private String account;

    /**
     * 密码
     */
    private String password;

    /**
     * 账号状态
     * 0-启用
     * 1-锁定
     * 2-禁用
     * <p>
     * ADM_USER_STATUS
     */
    private Integer status;

    /**
     * 是否超管
     * 1-是
     * 0-否
     */
    private Integer isSuperAdm;

    private String tenantCode;

    /**
     * 0 B端用户 1 小程序端理财师 2. 小程序端意向理财师
     */
    private Integer accountType;

    public Date createDt;

    public Long createBy;

    public Date modifyDt;

    public Long modifyBy;
}
