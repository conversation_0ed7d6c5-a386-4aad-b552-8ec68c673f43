package com.dl.basicservice.biz.dal.sys.role.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.Data;

@Data
@TableName("sys_tenant_role")
public class TenantRolePO extends BasePO {

    @TableId
    public Long id;

    @TableField("role_id")
    private Long roleId;

    @TableField("name")
    private String name;

    /**
     * 租户
     */
    @TableField("tenant_code")
    private String tenantCode;

    /**
     * 角色类型
     */
    @TableField("role_type")
    private String roleType;

    /**
     * 系统code，如dl-wealth-center
     */
    @TableField("system_code")
    private String systemCode;

}
