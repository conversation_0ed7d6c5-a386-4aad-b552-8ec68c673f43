package com.dl.basicservice.biz.manager.sys.user.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("密码修改返回结果")
public class SysUpdPwdDTO {

    @ApiModelProperty("结果信息")
    public String info;
    @ApiModelProperty("是否修改成功 1-成功 0-失败")
    public String success;
}
