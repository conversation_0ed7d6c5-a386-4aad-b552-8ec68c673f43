package com.dl.basicservice.web.controller.internal.adm.sys.tenant;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.dl.basicservice.biz.dal.sys.user.po.TenantUserRolePO;
import com.dl.basicservice.biz.manager.sys.role.TenantUserRoleManager;
import com.dl.basicservice.biz.manager.sys.role.bo.PermissionBO;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserRolesParamBO;
import com.dl.basicservice.web.controller.internal.adm.base.InternalAbstractController;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.JudgePermissionParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.UserRolesSaveParamDTO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.Api;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-17 11:04
 */
@Api("内部调用-租户账号权限控制器")
@RestController
@RequestMapping("/internal/tenantuserrole")
public class InternalTenantUserRoleController extends InternalAbstractController {

    @Resource
    private TenantUserRoleManager tenantUserRoleManager;

    @PostMapping("/save")
    public ResultModel<Void> saveUserRoles(@RequestBody @Validated UserRolesSaveParamDTO paramDTO) {
        SysUserRolesParamBO paramBO = new SysUserRolesParamBO();
        paramBO.setTenantCode(paramDTO.getTenantCode());
        paramBO.setUserId(paramDTO.getUserId());
        paramBO.setRoleIds(paramDTO.getRoleIds());
        paramBO.setSystemCode(getSystemCode());
        tenantUserRoleManager.saveUserRoles(paramBO);
        return ResultModel.success(null);
    }

    @PostMapping("/haspermission")
    public ResultModel<Boolean> hasPermission(@RequestBody JudgePermissionParamDTO paramDTO) {
        PermissionBO permissionBO = new PermissionBO();
        permissionBO.setLogical(paramDTO.getLogical());
        permissionBO.setRequired(paramDTO.isRequired());
        permissionBO.setValue(paramDTO.getValue());
        return ResultModel
                .success(tenantUserRoleManager.permissionLogic(permissionBO, getSystemCode(), paramDTO.getUserId()));
    }

    @GetMapping("/remove")
    public ResultModel remove(@RequestParam Long userId){
        //删除用户与角色的对应关系
        LambdaQueryWrapper<TenantUserRolePO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TenantUserRolePO::getUserId, userId);
        wrapper.eq(TenantUserRolePO::getSystemCode, getSystemCode());
        tenantUserRoleManager.remove(wrapper);
        return ResultModel.success(null);
    }

}
