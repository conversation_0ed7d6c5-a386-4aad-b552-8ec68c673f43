package com.dl.basicservice.biz.manager.sys.oplog.hepler;

import com.dl.basicservice.biz.dal.sys.oplog.po.OpLogPO;
import com.dl.basicservice.biz.manager.sys.oplog.bo.OpLogAddBO;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 15:19
 */
public class OpLogHelper {

    public static OpLogPO cnvOpLogAddBO2PO(OpLogAddBO input) {
        OpLogPO result = new OpLogPO();
        result.setBizCode(input.getBizCode());
        result.setOpObject(input.getOpObject());
        result.setOpKey(input.getOpKey());
        result.setOpType(input.getOpType());
        result.setOpBefore(input.getOpBefore());
        result.setOpAfter(input.getOpAfter());
        result.setRemark(input.getRemark());
        result.setOpUserId(input.getOpUserId());
        result.setOpUserName(input.getOpUserName());
        result.setOpDt(input.getOpDt());
        result.setExtData(input.getExtData());
        result.setTenantCode(input.getTenantCode());
        return result;
    }
}
