package com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 16:09
 */
@Data
public class RoleDTO implements Serializable {
    private static final long serialVersionUID = 8528491762653791270L;

    @ApiModelProperty("角色id")
    private String roleId;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色类型")
    private String roleType;

    @ApiModelProperty("租户id")
    private String tenantCode;

    @ApiModelProperty("创建时间")
    private Long createBy;

    @ApiModelProperty("修改时间")
    private Long modifyBy;

    @ApiModelProperty("创建人")
    private Date createDt;

    @ApiModelProperty("修改人")
    private Date modifyDt;
}
