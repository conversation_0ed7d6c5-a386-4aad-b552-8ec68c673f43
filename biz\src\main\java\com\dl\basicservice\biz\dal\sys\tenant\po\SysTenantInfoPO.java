package com.dl.basicservice.biz.dal.sys.tenant.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@FieldDefaults(level = AccessLevel.PRIVATE)
@TableName(value = "sys_tenant_info")
@Data
public class SysTenantInfoPO extends BasePO {

    @TableField(exist = false)
    private static final long serialVersionUID = -1L;

    @TableId(type = IdType.AUTO)
    Long id;

    /**
     * 租户名称
     */
    @TableField("name")
    String name;

    /**
     * 租户id
     */
    @TableField("tenant_code")
    String tenantCode;

    /**
     * 状态 0未启用 1正常 2停用
     */
    @TableField("status")
    Integer status;

    @TableField("logo_img")
    private String logoImg;

    /**
     * 租户访问域名，包含scheme
     */
    @TableField("tenant_domain")
    String tenantDomain;

    /**
     * 租户访问路径片段，常用于本地化部署
     */
    @TableField("path_part")
    String pathPart;

    /**
     * 是否供应商：0 否，默认； 1 是
     */
    @TableField("is_provider")
    Integer isProvider;

    /**
     * 是否隐藏二维码登录：0 否，默认；1 是
     */
    @TableField("is_hide_qr_login")
    Integer isHideQrLogin;

    /**
     * 是否企微入驻租户：0 否； 1 是 默认
     */
    @TableField("is_we_work")
    Integer isWeWork;

    /**
     * 租户信息描述
     */
    @TableField("tenant_desc")
    String tenantDesc;

    /**
     * 授权模式，1-微信全托管模式，2-微信第三方平台
     */
    @TableField("auth_mode")
    Integer authMode;

    /**
     * 私钥
     */
    @TableField("private_key")
    String privateKey;

    @TableField("is_deleted")
    Boolean isDeleted;

    /**
     * '0-纯页面模式，1-纯API提供模式，2-页面+API模式，默认值是0'
     */
    @TableField("produce_type")
    Integer produceType;

    /**
     * 存在API模式时，合作方视频回调地址
     */
    @TableField("video_callback_url")
    String videoCallbackUrl;

    /**
     * 租户小logo
     */
    @TableField("small_logo")
    String smallLogo;

    /**
     *
     * 是否试用租户 0-否 1-是
     */
    @TableField("is_trial")
    Integer isTrial;

    /**
     * 自研编辑器数字人视频合成方式
     * 0-模板每个卡片合成1次请求数字人合成视频，并通过ASR识别时间戳。1-模板每个卡片都请求数字人合成视频方式。
     * 默认值是0
     */
    @TableField("dm_produce_mode")
    Integer dmProduceMode;

    /**
     * 模拟本地化预览 0-否，1-是
     * 像安信这类客户，要 抹除 所有和 “定力数影”有关的设置，比如banner图不一样等等的
     */
    @TableField("simulate_local_preview")
    Integer simulateLocalPreview;

}