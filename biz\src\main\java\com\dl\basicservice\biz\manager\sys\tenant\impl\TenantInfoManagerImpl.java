package com.dl.basicservice.biz.manager.sys.tenant.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dl.basicservice.biz.dal.sys.tenant.SysTenantInfoMapper;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.manager.sys.menu.impl.UserRoleMenuRedisCache;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @ClassName SysTenantInfoServiceImp
 * @Description
 * <AUTHOR>
 * @Date 2022/4/11 9:46
 * @Version 1.0
 **/
@Service
public class TenantInfoManagerImpl extends ServiceImpl<SysTenantInfoMapper, SysTenantInfoPO>
        implements TenantInfoManager {

    @Resource
    private UserRoleMenuRedisCache userRoleMenuRedisCache;

    @Override
    public SysTenantInfoPO getTenantInfoFromCache(String tenantCode) {
        return userRoleMenuRedisCache.getTenantInfo(tenantCode);
    }

    @Override
    public void refreshSingleTenantCache(String tenantCode) {
        userRoleMenuRedisCache.refreshSingleTenantInfo(tenantCode);
    }
}
