package com.dl.basicservice.web.controller.adm.sys.tenant.convert;

import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.web.controller.adm.sys.tenant.vo.TenantBaseVO;
import com.dl.basicservice.web.controller.adm.sys.tenant.vo.TenantTrialAccountVO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2022-09-01 16:43
 */
public class TenantConvert {

    public static void fillTenantBaseVO(SysTenantInfoPO input, TenantBaseVO result) {
        if (Objects.isNull(input)) {
            return;
        }
        result.setTenantCode(input.getTenantCode());
        result.setName(input.getName());
        result.setLogoImg(input.getLogoImg());
        result.setCreateBy(String.valueOf(input.getCreateBy()));
        result.setCreateDt(input.getCreateDt());
        result.setModifyBy(String.valueOf(input.getModifyBy()));
        result.setModifyDt(input.getModifyDt());
        result.setStatus(input.getStatus());
        result.setIsProvider(input.getIsProvider());
        result.setIsWeWork(input.getIsWeWork());
        result.setAuthMode(input.getAuthMode());
        result.setSmallLogo(input.getSmallLogo());
        result.setTenantDomain(input.getTenantDomain());
        result.setPathPart(input.getPathPart());
        result.setTenantDesc(input.getTenantDesc());
        result.setPrivateKey(input.getPrivateKey());
        result.setProduceType(input.getProduceType());
        result.setIsTrial(input.getIsTrial());
        if (Objects.equals(Const.ZERO, input.getIsWeWork())) {
            result.setIsHideQrLogin(Const.ONE);
        } else {
            result.setIsHideQrLogin(input.getIsHideQrLogin());
        }
        result.setSimulateLocalPreview(input.getSimulateLocalPreview());
    }

}
