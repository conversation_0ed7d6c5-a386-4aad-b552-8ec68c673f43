package com.dl.basicservice.biz.common.util;

import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.Velocity;
import org.apache.velocity.app.VelocityEngine;

import java.io.StringWriter;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

public class VelocityUtil {
    private static Properties props = new Properties();
    private static VelocityEngine engine = new VelocityEngine(props);

    static {
        props.setProperty(Velocity.INPUT_ENCODING, "UTF-8");
        props.setProperty(Velocity.RESOURCE_LOADER, "class");
        props.setProperty("class.resource.loader.class",
                "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
    }

    public static VelocityContext createContext(Map<String, ?>... vars) {
        VelocityContext velocityContext = new VelocityContext();
        if (vars != null) {
            for (Map<String, ?> map : vars) {
                if (map != null) {
                    map.entrySet().forEach(en -> velocityContext.put(en.getKey(), en.getValue()));
                }
            }
        }
        return velocityContext;
    }

    public static String render(VelocityContext context, String template) {
        StringWriter writer = new StringWriter();
        engine.evaluate(context, writer, "", template);
        return writer.toString();
    }

    public static boolean calculateBoolean(VelocityContext context, String expression) {
        StringWriter writer = new StringWriter();
        String exp = "#set ($v = (" + expression + "))$v";
        engine.evaluate(context, writer, "", exp);
        return Boolean.parseBoolean(writer.toString());
    }

    public static void main(String[] args) {
        Map<String, Object> map = new HashMap<>();
        map.put("isFemale", true);
        VelocityContext context = createContext(map);
        StringWriter writer = new StringWriter();
        System.out.println(engine.evaluate(context, writer, "", "#set ($v = (${isFemale}==false))$v"));
        System.out.println(writer.toString());
    }
}
