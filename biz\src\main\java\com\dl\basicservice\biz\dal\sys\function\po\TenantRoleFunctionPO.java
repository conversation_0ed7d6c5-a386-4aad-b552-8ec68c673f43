package com.dl.basicservice.biz.dal.sys.function.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.dl.basicservice.biz.common.BasePO;
import lombok.Data;

/**
 * <p>
 * 角色-功能表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-30
 */
@Data
@TableName("sys_tenant_role_function")
public class TenantRoleFunctionPO extends BasePO {

    @TableId("id")
    public Long id;

    @TableField("role_id")
    private Long roleId;

    @TableField("function_id")
    private Long functionId;

    /**
     * 系统code，如dl-wealth-center
     */
    @TableField("system_code")
    private String systemCode;
}
