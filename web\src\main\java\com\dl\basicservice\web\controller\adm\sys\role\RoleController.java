package com.dl.basicservice.web.controller.adm.sys.role;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dl.basicservice.biz.common.validation.ValidateStrategy;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.manager.sys.role.enums.RoleTypeEnum;
import com.dl.basicservice.biz.manager.sys.menu.TenantRoleMenuManager;
import com.dl.basicservice.biz.manager.sys.menu.bo.RoleMenuParamBO;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.biz.manager.sys.role.bo.RoleParamBO;
import com.dl.basicservice.biz.manager.sys.role.bo.RoleSearchParamBO;
import com.dl.basicservice.biz.manager.sys.role.dto.SysRoleDTO;
import com.dl.basicservice.web.controller.adm.base.AbstractController;
import com.dl.basicservice.web.controller.adm.sys.role.convert.RoleConvert;
import com.dl.basicservice.web.controller.adm.sys.role.param.DefaultRoleSettingParam;
import com.dl.basicservice.web.controller.adm.sys.role.param.RoleParam;
import com.dl.basicservice.web.controller.adm.sys.role.param.RoleSearchParam;
import com.dl.basicservice.web.controller.adm.sys.role.vo.RoleVO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/role")
@Api("角色管理")
public class RoleController extends AbstractController {

    @Autowired
    private TenantRoleManager tenantRoleManager;
    @Autowired
    private TenantRoleMenuManager tenantRoleMenuManager;
    @Autowired
    private HostTimeIdg hostTimeIdg;

    //@Permission("adm:role:query") 列表查询不再校验权限
    @PostMapping("/page")
    @ApiOperation("查询角色列表")
    public ResultPageModel<RoleVO> list(@Validated @RequestBody RoleSearchParam roleSearchParam) {
        RoleSearchParamBO roleParamBO = new RoleSearchParamBO();
        roleParamBO.setTenantCode(getTenantCode());
        roleParamBO.setRoleName(roleSearchParam.getRoleName());
        roleParamBO.setUserId(roleSearchParam.getUserId());
        roleParamBO.setPageIndex(roleSearchParam.getPageIndex());
        roleParamBO.setPageSize(roleSearchParam.getPageSize());
        roleParamBO.setSystemCode(getSystemCode());

        IPage<SysRoleDTO> page = tenantRoleManager.findRoles(roleParamBO);
        if (CollectionUtils.isEmpty(page.getRecords())) {
            return pageQueryModel(page, Collections.emptyList());
        }
        List<RoleVO> resultList = page.getRecords().stream().map(dto -> RoleConvert.cnvRoleDTO2VO(dto))
                .collect(Collectors.toList());
        return pageQueryModel(page, resultList);
    }

    //@Permission("adm:role:add")
    @PostMapping("/add")
    @ApiOperation("新增角色")
    public ResultModel<Boolean> add(@Validated @RequestBody RoleParam roleParam) {
        TenantRolePO po = new TenantRolePO();
        po.setRoleType(RoleTypeEnum.NORMAL.getCode());
        po.setName(roleParam.getRoleName());
        po.setTenantCode(getTenantCode());
        po.setCreateBy(getUserId());
        po.setRoleId(hostTimeIdg.generateId().longValue());
        po.setSystemCode(getSystemCode());
        tenantRoleManager.save(po);
        return ResultModel.success(true);
    }

    //@Permission("adm:role:update")
    @PostMapping("/update")
    @ApiOperation("修改角色基本信息")
    public ResultModel<Boolean> update(@Validated(ValidateStrategy.update.class) @RequestBody RoleParam roleParam) {
        RoleParamBO roleParamBO = new RoleParamBO();
        roleParamBO.setRoleId(roleParam.getRoleId());
        roleParamBO.setRoleName(roleParam.getRoleName());
        roleParamBO.setTenantCode(getTenantCode());
        tenantRoleManager.update(roleParamBO);
        return ResultModel.success(true);
    }

    //@Permission(value = { "adm:role:delete", "adm:minniapp:role:delete" }, logical = Logical.OR)
    @PostMapping("/delete")
    @ApiOperation("删除角色信息")
    public ResultModel<Boolean> delete(@Validated @RequestBody RoleParam roleParam) {
        if (tenantRoleManager.belongUsers(roleParam.getRoleId())) {
            return ResultModel.error("1", "用户拥有此角色，不能删除。请先解除用户与此角色的关联");
        }

        //清空该角色关联的菜单和权限
        RoleMenuParamBO bo = new RoleMenuParamBO();
        bo.setRoleId(roleParam.getRoleId());
        bo.setMenuIds(Collections.emptyList());
        bo.setFunctionIds(Collections.emptyList());
        bo.setSystemCode(getSystemCode());
        tenantRoleMenuManager.saveRoleMenu(bo);

        //删除角色
        tenantRoleManager.deleteRole(roleParam.getRoleId());
        return ResultModel.success(true);
    }

    @ApiOperation("设置默认角色")
    @PostMapping("/setdefaultrole")
    public ResultModel<Void> setDefaultRole(@Validated @RequestBody DefaultRoleSettingParam param) {
        tenantRoleManager.setDefaultRole(getTenantCode(), getSystemCode(), param.getRoleId());
        return ResultModel.success(null);
    }

    @ApiOperation("查询默认角色")
    @PostMapping("/querydefaultrole")
    public ResultModel<RoleVO> queryDefaultRole() {
        SysRoleDTO roleDTO = tenantRoleManager.queryDefaultRole(getTenantCode(), getSystemCode());
        return ResultModel.success(RoleConvert.cnvRoleDTO2VO(roleDTO));
    }
}
