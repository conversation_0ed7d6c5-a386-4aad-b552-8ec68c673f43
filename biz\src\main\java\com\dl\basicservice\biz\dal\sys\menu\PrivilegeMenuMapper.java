package com.dl.basicservice.biz.dal.sys.menu;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dl.basicservice.biz.common.annotation.BaseDao;
import com.dl.basicservice.biz.dal.sys.menu.po.MenuFunctionPO;
import com.dl.basicservice.biz.dal.sys.menu.po.PrivilegeMenuPO;

import java.util.List;

@BaseDao
public interface PrivilegeMenuMapper extends BaseMapper<PrivilegeMenuPO> {
    /**
     * 获取菜单功能列表
     *
     * @return
     */
    List<MenuFunctionPO> listMenuFunction();

}
