package com.dl.basicservice.web.controller.internal.adm.auth.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 10:00
 */
@Data
public class LoginParamDTO implements Serializable {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("账号")
    private String account;

    private String tenantCode;
}
