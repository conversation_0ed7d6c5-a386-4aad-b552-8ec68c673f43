package com.dl.basicservice.biz.common.util;

import com.dl.basicservice.biz.common.enums.SymbolE;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-03 15:27
 */
public class NameConverter {

    public static String convertToAbbreviation(String name) {
        if (StringUtils.isBlank(name)) {
            return SymbolE.BLANK.name();
        }
        // 检查是否为中文名字
        if (name.matches("[\\u4e00-\\u9fa5]+")) {
            StringBuilder sb = new StringBuilder();

            for (int i = 0; i < name.length(); i++) {
                char c = name.charAt(i);
                // 将汉字转换为拼音的首字母
                if (Character.UnicodeBlock.of(c) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS) {
                    String[] pinyinArray = PinyinHelper.toHanyuPinyinStringArray(c);
                    if (pinyinArray != null && pinyinArray.length > 0) {
                        if (i == 0) {
                            sb.append(pinyinArray[0].replaceAll("\\d+", ""));
                        } else {
                            sb.append(pinyinArray[0].charAt(0));
                        }
                    }
                } else {
                    sb.append(Character.toLowerCase(c));
                }
            }

            return sb.toString();
        } else {
            // 英文名字处理逻辑
            // 移除英文名字中除字母和空格之外的字符和特殊符号
            name = name.replaceAll("[^a-zA-Z\\s]", "");

            // 分割英文名字中的单词
            String[] words = name.toLowerCase().split("\\s+");
            StringBuilder sb = new StringBuilder();

            // 取每个单词的首字母
            for (String word : words) {
                if (!word.isEmpty()) {
                    sb.append(word);
                }
            }

            return sb.toString();
        }
    }

    public static void main(String[] args) {
        String name1 = "王安石";
        String name2 = "陈楚生";
        String name3 = "John Doe!";
        String name4 = "Alice Smith123";

        String abbreviation1 = convertToAbbreviation(name1);
        String abbreviation2 = convertToAbbreviation(name2);
        String abbreviation3 = convertToAbbreviation(name3);
        String abbreviation4 = convertToAbbreviation(name4);

        System.out.println(name1 + "：" + abbreviation1);
        System.out.println(name2 + "：" + abbreviation2);
        System.out.println(name3 + "：" + abbreviation3);
        System.out.println(name4 + "：" + abbreviation4);
    }
}
