package com.dl.basicservice.biz.manager.sys.oplog.enums;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 17:38
 */
public enum OpObjectEnum {

    TEMPLATE("template", "模板"),
    USER("user", "用户");

    private String code;

    private String desc;

    OpObjectEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
