package com.dl.basicservice.biz.manager.sys.menu.assist;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.dl.basicservice.biz.manager.sys.function.dto.FunctionDTO;
import com.dl.basicservice.biz.manager.sys.menu.dto.MenuDTO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.util.Assert;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MenuTreeHelp {
    public static List<MenuDTO> listWithTree(List<MenuDTO> entities) {
        // 2 组装成父子的树形结构
        List<MenuDTO> level1Menus = entities.stream().filter(entity ->
                entity.getParentId().longValue() == 0
        ).map((menu)->{
            menu.setChildren(getChildrens(menu,entities));
            return menu;
        }).sorted((menu1,menu2)->{
            return (menu1.getSort() == null?0:menu1.getSort()) - (menu2.getSort() == null?0:menu2.getSort());
        }).collect(Collectors.toList());
        return level1Menus;
    }

    // 递归查找所有菜单的子菜单
    private static List<MenuDTO> getChildrens(MenuDTO root, List<MenuDTO> all) {
        List<MenuDTO> children = all.stream().filter(entity -> {
            return entity.getParentId().longValue() == root.getMenuId().longValue();  // 注意此处应该用longValue()来比较，否则会出先bug，因为parentCid和catId是long类型
        }).map(menu -> {
            // 1 找到子菜单
            menu.setChildren(getChildrens(menu, all));
            return menu;
        }).sorted((menu1, menu2) -> {
            // 2 菜单的排序
            return (menu1.getSort() == null?0:menu1.getSort()) - (menu2.getSort() == null?0:menu2.getSort());
        }).collect(Collectors.toList());
        return children;
    }


    //设置对应租户是否有权限标识
    public static void listSetOwnerWithTree(List<MenuDTO> list,Map<String, String> menuMap,Map<String, String> functionMap) {
        if (CollectionUtils.isNotEmpty(list) && MapUtils.isNotEmpty(menuMap)) {
            list.forEach(m -> {
                String menuId = String.valueOf(m.getMenuId());
                if(menuMap.containsKey(menuId)){
                    m.setOwner(1);
                }

                List<FunctionDTO> functions = m.getFunctions();
                if (CollectionUtils.isNotEmpty(functions) && MapUtils.isNotEmpty(functionMap)) {
                    functions.forEach(f -> {
                        String functionId = String.valueOf(f.getFunctionId());
                        if(functionMap.containsKey(functionId)){
                            f.setOwner(1);
                        }
                    });
                }
                listSetOwnerWithTree(m.getChildren(),menuMap,functionMap);
            });
        }
    }


    /**
     * 参数的存在性校验
     *
     * @param mapper
     * @param list
     * @param f
     * @param msg
     * @param <T>
     * @param <K>
     */
    public static <T, K> void validate(BaseMapper<T> mapper, List<K> list, SFunction<T, ?> f, String msg) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(list)) {
            LambdaQueryWrapper<T> wrapper = Wrappers.lambdaQuery();
            wrapper.in(f, list);
            Integer cnt = mapper.selectCount(wrapper);
            Assert.isTrue(cnt == list.size(), msg);
        }
    }

    public static <T> void validate(BaseMapper<T> mapper, Serializable id, String msg) {
        if (id != null) {
            Assert.isTrue(mapper.selectById(id) != null, msg);
        }
    }
}
