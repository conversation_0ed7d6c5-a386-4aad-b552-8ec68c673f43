package com.dl.basicservice.web.controller.adm.config;


import com.dl.basicservice.web.controller.adm.interceptor.AuthenticationInterceptor;
import com.dl.basicservice.web.filter.RepeatableFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Autowired
    AuthenticationInterceptor authenticationInterceptor;


    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(authenticationInterceptor);
    }

    //    @Bean
    //    public FilterRegistrationBean xssFilterRegistration()
    //    {
    //        FilterRegistrationBean registration = new FilterRegistrationBean();
    //        registration.setDispatcherTypes(DispatcherType.REQUEST);
    //        registration.setFilter(new XssFilter());
    //        registration.addUrlPatterns(StringUtils.split(urlPatterns, ","));
    //        registration.setName("xssFilter");
    //        registration.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE);
    //        Map<String, String> initParameters = new HashMap<String, String>();
    //        initParameters.put("excludes", excludes);
    //        initParameters.put("enabled", enabled);
    //        registration.setInitParameters(initParameters);
    //        return registration;
    //    }

    @Bean
    public FilterRegistrationBean someFilterRegistration() {
        FilterRegistrationBean registration = new FilterRegistrationBean();
        registration.setFilter(new RepeatableFilter());
        registration.addUrlPatterns("/*");
        registration.setName("repeatableFilter");
        registration.setOrder(FilterRegistrationBean.LOWEST_PRECEDENCE);
        return registration;
    }

}
