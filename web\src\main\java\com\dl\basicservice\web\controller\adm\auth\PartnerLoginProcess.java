package com.dl.basicservice.web.controller.adm.auth;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.MapUtils;
import java.util.Objects;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.enums.SymbolE;
import com.dl.basicservice.biz.common.util.DateUtil;
import com.dl.basicservice.biz.common.util.NameConverter;
import com.dl.basicservice.biz.common.util.OperatorUtil;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.biz.manager.sys.role.dto.SysRoleDTO;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.bo.AddUserBO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysAdmUserDTO;
import com.dl.basicservice.biz.manager.sys.user.dto.SysBasicUserDTO;
import com.dl.basicservice.biz.manager.sys.userbind.TenantAdmUserBindManager;
import com.dl.basicservice.biz.manager.sys.userbind.bo.PartnerTokenInfo;
import com.dl.basicservice.biz.manager.sys.userbind.bo.TenantAdmUserBindBO;
import com.dl.basicservice.biz.manager.sys.userbind.dto.TenantAdmUserBindDTO;
import com.dl.basicservice.biz.manager.sys.userbind.utils.CommonSecretUtil;
import com.dl.basicservice.biz.manager.transaction.TransactionProxyManager;
import com.dl.basicservice.web.controller.adm.auth.param.GenerateToken4TestParam;
import com.dl.basicservice.web.controller.adm.auth.param.PartnerLoginParam;
import com.dl.basicservice.web.controller.adm.auth.vo.PartnerLoginVO;
import com.dl.basicservice.web.controller.adm.base.AbstractController;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-02 15:04
 */
@Component
public class PartnerLoginProcess extends AbstractController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PartnerLoginProcess.class);

    @Resource
    private TenantInfoManager tenantInfoManager;

    @Resource
    private SysAdmUserService sysAdmUserService;

    @Resource
    private TenantAdmUserBindManager tenantAdmUserBindManager;

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Resource
    private TenantRoleManager tenantRoleManager;

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private TransactionProxyManager transactionProxyManager;

    public ResultModel<PartnerLoginVO> entrance(PartnerLoginParam param) {
        //1.查询租户信息
        SysTenantInfoPO tenantInfoPO = tenantInfoManager.getTenantInfoFromCache(param.getTenantCode());
        Assert.notNull(tenantInfoPO, "租户不存在");
        LOGGER.info("entrance,,,,tenantInfoPO:{}", JSONUtil.toJsonStr(tenantInfoPO));

        //2.解密合作方token
        PartnerTokenInfo partnerTokenInfo = this.verifyTokenWithDes(tenantInfoPO, param);
        LOGGER.info("entrance,,,,partnerTokenInfo:{}", JSONUtil.toJsonStr(partnerTokenInfo));
        Assert.notNull(partnerTokenInfo, "租户token解密失败");

        //3.登录
        SysBasicUserDTO basicUserDTO = this.extLogin(tenantInfoPO, partnerTokenInfo);
        LOGGER.info("entrance,,,,basicUserDTO:{}", JSONUtil.toJsonStr(basicUserDTO));

        return ResultModel.success(this.buildPartnerLoginVO(basicUserDTO, partnerTokenInfo));
    }

    public ResultModel<String> generateToken4Test(GenerateToken4TestParam param) {
        //查询租户信息
        SysTenantInfoPO tenantInfoPO = tenantInfoManager.getTenantInfoFromCache(getTenantCode());
        Assert.notNull(tenantInfoPO, "租户不存在");

        try {
            String token = URLEncoder.encode(CommonSecretUtil.encrypt(JSONUtil.toJsonStr(param.getParamMap()),
                    this.generateDesKey(tenantInfoPO.getTenantCode(), tenantInfoPO.getPrivateKey())), "UTF-8");
            return ResultModel.success(token);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return ResultModel.error("-1", "生成token失败");
    }

    /**
     * 校验token--des
     *
     * @param param
     * @return
     */
    public PartnerTokenInfo verifyTokenWithDes(SysTenantInfoPO tenantInfoPO, PartnerLoginParam param) {
        try {
            String decodeStr = URLDecoder.decode(param.getToken(), "UTF-8");
            Map<String, Object> map = CommonSecretUtil
                    .unSecurity(decodeStr, this.generateDesKey(tenantInfoPO.getTenantCode(), tenantInfoPO.getPrivateKey()));
            if (MapUtils.isEmpty(map)) {
                return null;
            }

            String mapStr = JSONObject.toJSONString(map);
            PartnerTokenInfo partnerTokenInfo = JSONObject.parseObject(mapStr, PartnerTokenInfo.class);
            checkTokenParam(partnerTokenInfo);
            return partnerTokenInfo;
        } catch (UnsupportedEncodingException e) {
            LOGGER.warn("des转码失败", e);
        } catch (Exception ex) {
            LOGGER.warn("验证des参数失败", ex);
        }
        return null;
    }

    private void checkTokenParam(PartnerTokenInfo partnerTokenInfo) {
        if (StringUtils.isBlank(partnerTokenInfo.getTarget())) {
            partnerTokenInfo.setTarget("/");
        }
    }

    /**
     * 外部用户登录
     *
     * @param tenantInfoPO
     * @param partnerTokenInfo
     */
    public SysBasicUserDTO extLogin(SysTenantInfoPO tenantInfoPO, PartnerTokenInfo partnerTokenInfo) {
        AtomicReference<SysBasicUserDTO> basicUserDTO = new AtomicReference<>();
        transactionProxyManager.process(() -> {
            //1.查询绑定记录
            TenantAdmUserBindDTO bindDTO = tenantAdmUserBindManager
                    .queryByExtUserId(tenantInfoPO.getTenantCode(), partnerTokenInfo.getExtUserId());
            //2.1有绑定记录，则直接登录。
            if (Objects.nonNull(bindDTO)) {
                SysAdmUserDTO userDTO = sysAdmUserService.findUserDetailFromCache(bindDTO.getUserId());
                basicUserDTO.set(sysAdmUserService
                        .directLogin(userDTO.getAccount(), userDTO.getTenantCode(), userDTO.getUserId()));
                return;
            }

            //2.2无绑定记录，则创建用户
            String account = this.generateAccount(tenantInfoPO.getTenantCode(), partnerTokenInfo.getName());
            AddUserBO addUserBO = AddUserBO.builder().account(account).mobile(SymbolE.BLANK.getValue())
                    .password(DateUtil.format(tenantInfoPO.getCreateDt(), DateUtil.YMD))
                    .name(partnerTokenInfo.getName()).tenantCode(tenantInfoPO.getTenantCode())
                    .systemCode(Const.MAGIC_SYSTEM_CODE).build();

            //查询默认角色
            SysRoleDTO defaultRole = tenantRoleManager
                    .queryDefaultRole(tenantInfoPO.getTenantCode(), Const.MAGIC_SYSTEM_CODE);
            List<Long> defaultRoleIds = Objects.nonNull(defaultRole) ?
                    Lists.newArrayList(defaultRole.getRoleId()) :
                    Collections.emptyList();
            operatorUtil.init(Const.SYS_USER_ID, tenantInfoPO.getTenantCode());
            //创建用户，并分配默认角色
            Long userId = sysAdmUserService.addUser(addUserBO, defaultRoleIds);

            //创建绑定记录
            TenantAdmUserBindBO bindBO = new TenantAdmUserBindBO();
            bindBO.setExtUserId(partnerTokenInfo.getExtUserId());
            bindBO.setTenantCode(tenantInfoPO.getTenantCode());
            bindBO.setUserId(userId);
            tenantAdmUserBindManager.doBind(bindBO);

            //登录
            basicUserDTO.set(sysAdmUserService.directLogin(account, tenantInfoPO.getTenantCode(), userId));
        });

        return basicUserDTO.get();
    }

    private String generateAccount(String tenantCode, String name) {
        if (StringUtils.isBlank(name)) {
            return hostTimeIdg.generateId().toString();
        }

        //根据名字首字母生成
        String account = NameConverter.convertToAbbreviation(name);
        if (StringUtils.isBlank(account)) {
            return hostTimeIdg.generateId().toString();
        }

        //判断用户名是否已存在
        TenantAdmUserPO exisitAdmUserPO = sysAdmUserService
                .getOne(Wrappers.lambdaQuery(TenantAdmUserPO.class).eq(TenantAdmUserPO::getTenantCode, tenantCode)
                        .eq(TenantAdmUserPO::getAccount, account));
        if (Objects.isNull(exisitAdmUserPO)) {
            return account;
        }

        return hostTimeIdg.generateId().toString();
    }

    private PartnerLoginVO buildPartnerLoginVO(SysBasicUserDTO basicUserDTO, PartnerTokenInfo partnerTokenInfo) {
        PartnerLoginVO partnerLoginVO = new PartnerLoginVO();
        partnerLoginVO.setAccount(basicUserDTO.getAccount());
        partnerLoginVO.setUserId(String.valueOf(basicUserDTO.getUserId()));
        partnerLoginVO.setToken(basicUserDTO.getToken());
        partnerLoginVO.setTargetUrl(partnerTokenInfo.getTarget());
        return partnerLoginVO;
    }

    private String generateDesKey(String tenantCode, String privateKey) {
        String key = tenantCode + privateKey;
        if (key.length() < 24) {
            LOGGER.error("联合登录秘钥长度有误，tenantCode:{},,,privateKey:{}", tenantCode, privateKey);
            throw BusinessServiceException.getInstance("联合登录秘钥长度有误");
        }
        return key.substring(0, 24);
    }

}
