package com.dl.basicservice.biz.manager.storepolicy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AccessLevel;
import lombok.Data;
import lombok.experimental.FieldDefaults;

@Data
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MinioResultDTO extends BaseTempCredentialDTO {

    /**
     * minio服务器ip
     */
    @ApiModelProperty("minio服务器IP")
    String minioHost;

    /**
     * minio服务器port
     */
    @ApiModelProperty("minio服务器Port")
    Integer minioPort;

    @ApiModelProperty("minio临时accessKey")
    String accessKey;

    @ApiModelProperty("minio临时secretKey")
    String secretKey;

    @ApiModelProperty("请求协议：http://或者https://")
    String schemePrefix;

    @ApiModelProperty("true代表使用HTTPS")
    boolean useSSL;

    @ApiModelProperty("存储桶ID")
    String bucketId;

    String sessionToken;
}
