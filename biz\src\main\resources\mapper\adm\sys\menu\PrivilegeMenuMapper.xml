<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.dl.basicservice.biz.dal.sys.menu.PrivilegeMenuMapper">

    <select id="listMenuFunction" resultType="com.dl.basicservice.biz.dal.sys.menu.po.MenuFunctionPO">
        SELECT mf.menu_id AS menuId,mf.function_id AS functionId,f.function_code AS functionCode,f.name,f.icon,f.sort
        FROM sys_privilege_menu m,sys_privilege_function f,sys_privilege_menu_function mf
        WHERE m.menu_id = mf.menu_id
        AND   f.function_id = mf.function_id
        AND   m.disable = 0
    </select>

</mapper>