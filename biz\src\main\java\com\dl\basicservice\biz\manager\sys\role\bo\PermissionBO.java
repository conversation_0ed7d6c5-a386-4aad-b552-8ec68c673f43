package com.dl.basicservice.biz.manager.sys.role.bo;

import com.dl.basicservice.biz.common.annotation.Logical;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-17 16:29
 */
public class PermissionBO {

    /**
     * @return 权限编码
     */
    private String[] value;

    /**
     * 是否需要校验
     */
    private boolean required;

    /**
     * 权限编码是否需要同时拥有
     */
    private Logical logical;

    public String[] getValue() {
        return value;
    }

    public void setValue(String[] value) {
        this.value = value;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public Logical getLogical() {
        return logical;
    }

    public void setLogical(Logical logical) {
        this.logical = logical;
    }
}
