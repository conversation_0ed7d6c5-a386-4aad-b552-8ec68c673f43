CREATE TABLE `op_log` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
  `log_id` bigint NOT NULL COMMENT '日志id',
  `biz_code` varchar(100) NOT NULL COMMENT '接入业务的编码',
  `op_object` varchar(200) NOT NULL COMMENT '操作对象',
  `op_key` varchar(100) NOT NULL COMMENT '操作对象主键',
  `op_type` varchar(50) NOT NULL COMMENT '操作类型，自定义（add、update、delete）',
  `op_before` text COMMENT '操作前数据 格式自定义',
  `op_after` text COMMENT '操作后数据 格式自定义',
  `remark` text COMMENT '操作说明',
  `ext_data` text COMMENT '扩展信息',
  `op_user_id` varchar(100) NOT NULL COMMENT '操作人',
  `op_user_name` varchar(100) NOT NULL COMMENT '操作人姓名',
  `op_dt` datetime NOT NULL COMMENT '操作时间',
  `create_dt` datetime NOT NULL COMMENT '创建时间',
  `modify_dt` datetime NOT NULL COMMENT '修改时间',
  `is_deleted` tinyint NOT NULL COMMENT '是否删除，0-否，1-是'
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_logId` (`log_id`) USING BTREE,
  KEY `idx_bizCode_opObject_opKey` (`biz_code`,`op_object`,`op_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='操作日志表';


ALTER TABLE `basic_service`.`op_log`
ADD COLUMN `tenant_code` varchar(100) NOT NULL COMMENT '租户编码';