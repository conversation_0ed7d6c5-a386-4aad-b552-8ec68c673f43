package com.dl.basicservice.web.controller.adm.storepolicy;

import com.dl.basicservice.biz.common.tencentcloud.cos.CosGetTempCredentialBO;
import com.dl.basicservice.biz.manager.storepolicy.StorePolicyManager;
import com.dl.basicservice.biz.manager.storepolicy.dto.BaseTempCredentialDTO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @ClassName StoragePolicyController
 * @Description
 * <AUTHOR>
 * @Date 2022/10/9 18:47
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/store/policy")
public class StorePolicyController {

    @Resource
    private StorePolicyManager storePolicyManager;

    @PostMapping("/sts/get")
    @ApiOperation("获取临时访问资质")
    public ResultModel<BaseTempCredentialDTO> stsGet(@RequestBody @Validated CosGetTempCredentialBO param) {
        return ResultModel.success(storePolicyManager.getTempCredential(param));
    }
}
