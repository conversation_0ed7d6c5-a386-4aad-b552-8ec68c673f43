package com.dl.basicservice.biz.manager.mino;

import cn.hutool.json.JSONUtil;
import com.dl.basicservice.biz.common.enums.SymbolE;
import com.dl.basicservice.biz.common.util.RedisUtil;
import com.dl.basicservice.biz.manager.mino.dto.MinioTempCredentialDTO;
import com.dl.basicservice.biz.manager.mino.policy.MinioPolicy;
import com.dl.basicservice.biz.manager.mino.policy.MinioStatement;
import com.dl.basicservice.biz.manager.mino.properties.MinioProperties;
import com.dl.framework.core.interceptor.expdto.BusinessServiceException;
import com.google.common.collect.Lists;
import io.minio.credentials.AssumeRoleProvider;
import io.minio.credentials.Credentials;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class MinioApiManager {

    /**
     * 单位：秒
     */
    private static final int DURATION_SECONDS = 7200;
    private static final String ROLE_SESSION_NAME = "any_session";
    private static final String HTTPS = "https://";
    private static final String HTTP = "http://";
    private static final String MINIO_LOCK_KEY = "dl.minio_sts_lock";
    private static final String MINIO_CACHE_KEY = "dl.minio_sts_cache";

    @Resource
    private MinioProperties minioProperties;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private RedissonClient redissonClient;

    /**
     * 获取临时token
     *
     * @return
     */
    public MinioTempCredentialDTO getTempCredential() {
        Object o = redisUtil.get(MINIO_CACHE_KEY);
        if (Objects.nonNull(o)) {
            return JSONUtil.toBean((String) o, MinioTempCredentialDTO.class);
        }
        String lockKey = MINIO_LOCK_KEY;
        RLock lock = redissonClient.getLock(lockKey);
        MinioTempCredentialDTO result = new MinioTempCredentialDTO();
        result.setMinioHost(minioProperties.getMinioHost());
        result.setMinioPort(minioProperties.getMinioPort());
        result.setUseSSL(minioProperties.isTslSecure());
        result.setSchemePrefix(getScheme());
        result.setBucketId(minioProperties.getBucketId());
        try {
            if (!lock.tryLock()) {
                log.error("获取锁失败！lockKey={}", lockKey);
                throw BusinessServiceException.getInstance("获取凭证失败");
            }
            //创建签名对象,默认有效3600s，若有设置其他值则以设置为准
            AssumeRoleProvider provider =
                    new AssumeRoleProvider(getEndPoint(), minioProperties.getMcUserName(), minioProperties.getMcPwd(),
                            DURATION_SECONDS, buildPolicy(), null, null, ROLE_SESSION_NAME, null, null);
            Credentials creResult = provider.fetch();
            result.setAccessKey(creResult.accessKey());
            result.setSecretKey(creResult.secretKey());
            result.setSessionToken(creResult.sessionToken());
            redisUtil.set(MINIO_CACHE_KEY, JSONUtil.toJsonStr(result), DURATION_SECONDS - 100);
            return result;
        } catch (NoSuchAlgorithmException e) {
            log.error("", e);
            throw BusinessServiceException.getInstance("获取凭证失败");
        } finally {
            if (lock.isLocked()) {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
    }

    private String getScheme() {
        return minioProperties.isTslSecure() ? HTTPS : HTTP;
    }

    private String getEndPoint() {
        String endPoint = getScheme() + minioProperties.getMinioHost();
        if (Objects.nonNull(minioProperties.getMinioPort())) {
            endPoint = endPoint + SymbolE.COLON_EN.getValue() + minioProperties.getMinioPort();
        }
        return endPoint;
    }

    private String buildPolicy() {
        MinioPolicy minioPolicy = new MinioPolicy();
        List<MinioStatement> list = Lists.newArrayList();
        MinioStatement minioStatement = new MinioStatement();
        minioStatement.setAction(Lists.newArrayList("s3:GetObject", "s3:GetBucketLocation", "s3:PutObject"));
        minioStatement.setResource(
                Lists.newArrayList(String.format("arn:aws:s3:::%s/*", minioProperties.getBucketId())));
        list.add(minioStatement);
        minioPolicy.setStatement(list);
        return JSONUtil.toJsonStr(minioPolicy);
    }
}
