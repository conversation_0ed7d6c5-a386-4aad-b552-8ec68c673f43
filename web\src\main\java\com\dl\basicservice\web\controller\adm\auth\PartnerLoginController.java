package com.dl.basicservice.web.controller.adm.auth;

import com.alibaba.fastjson.JSON;
import com.dl.basicservice.biz.common.annotation.NotLogin;
import com.dl.basicservice.web.controller.adm.auth.param.GenerateToken4TestParam;
import com.dl.basicservice.web.controller.adm.auth.param.PartnerLoginParam;
import com.dl.basicservice.web.controller.adm.auth.vo.PartnerLoginVO;
import com.dl.framework.common.model.ResultModel;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-01 18:34
 */
@RestController
@RequestMapping("/partner/login")
public class PartnerLoginController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PartnerLoginController.class);

    @Resource
    private PartnerLoginProcess partnerLoginProcess;

    @NotLogin
    @PostMapping("/entrance")
    public ResultModel<PartnerLoginVO> entrance(@RequestBody @Validated PartnerLoginParam param) {
        LOGGER.info("调用entrace接口 入参：{}", JSON.toJSONString(param));
        return partnerLoginProcess.entrance(param);
    }

    @ApiOperation("生成测试用的token")
    @PostMapping("/generatetoken4test")
    public ResultModel<String> generateToken4Test(@RequestBody @Validated GenerateToken4TestParam param) {
        return partnerLoginProcess.generateToken4Test(param);
    }

}
