package com.dl.basicservice.biz.mq.producer;

import cn.hutool.json.JSONUtil;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.mq.DlChannels;
import com.dl.basicservice.biz.mq.producer.dto.TenantStatusChangeMsgDTO;
import com.dl.basicservice.biz.mq.producer.enums.DelayLevelEnum;
import org.apache.rocketmq.common.message.MessageConst;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 租户状态变动消息的发送者
 *
 * <AUTHOR>
 * @version V1.0
 * @since 2022-12-30 09:36
 */
@Component
public class TenantStatusChangeProducer {
    private static final Logger LOGGER = LoggerFactory.getLogger(TenantStatusChangeProducer.class);

    @Resource
    private DlChannels dlChannels;

    public void sendMQ(SysTenantInfoPO sysTenantInfoPO) {
        TenantStatusChangeMsgDTO msgDTO = new TenantStatusChangeMsgDTO();
        msgDTO.setTenantCode(sysTenantInfoPO.getTenantCode());
        msgDTO.setName(sysTenantInfoPO.getName());
        msgDTO.setStatus(sysTenantInfoPO.getStatus());
        msgDTO.setCreateDt(sysTenantInfoPO.getCreateDt());
        msgDTO.setModifyDt(sysTenantInfoPO.getModifyDt());

        try {
            this.doSendMQ(msgDTO);
        } catch (Exception e) {
            LOGGER.error("发送租户状态变动的mq 发送异常，msgDTO:{},,,,e:{}", JSONUtil.toJsonStr(msgDTO), e);
        }
    }

    public void doSendMQ(TenantStatusChangeMsgDTO msgDTO) {
        Message message = MessageBuilder.withPayload(msgDTO)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, DelayLevelEnum.ONE_SECEND.getValue()).build();
        boolean sendResult = dlChannels.tenantstatuschange().send(message, 1000L);
        LOGGER.info("发送租户状态变动的mq ,message:{},sendResult:{}", JSONUtil.toJsonStr(message), sendResult);
    }
}
