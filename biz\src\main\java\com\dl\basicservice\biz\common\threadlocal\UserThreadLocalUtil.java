package com.dl.basicservice.biz.common.threadlocal;

import org.springframework.stereotype.Component;

/**
 * @ClassName UserThreadLocalUtil
 * @Description
 * <AUTHOR>
 * @Date 2022/4/30 15:40
 * @Version 1.0
 **/
@Component
public class UserThreadLocalUtil {

    private static ThreadLocal<UserDTO> userHolder = new ThreadLocal<>();

    public void init(UserDTO user) {
        userHolder.set(user);
    }

    public UserDTO getUser() {
        return userHolder.get();
    }

    public void remove() {
        userHolder.remove();
    }
}
