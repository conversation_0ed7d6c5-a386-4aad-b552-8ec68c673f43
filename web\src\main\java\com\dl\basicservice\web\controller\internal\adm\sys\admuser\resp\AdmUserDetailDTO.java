package com.dl.basicservice.web.controller.internal.adm.sys.admuser.resp;

import com.dl.basicservice.web.controller.internal.adm.sys.tenant.resp.TenantInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-06-19 15:50
 */
@Data
public class AdmUserDetailDTO implements Serializable {
    private static final long serialVersionUID = -1784043386535491538L;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("token")
    private String token;

    @ApiModelProperty("资源中心token")
    private String rcToken;

    @ApiModelProperty("租户id")
    private String tenantCode;

    @ApiModelProperty("是否超级管理员")
    private String isSuperAdm;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty(value = "状态: 1=已激活，2=已禁用，4=未激活，5=退出企业")
    private String status;

    @ApiModelProperty(value = "状态: 1=已激活，2=已禁用，4=未激活，5=退出企业")
    private String statusStr;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("拥有的权限编码")
    private Set<FuncDTO> functions;

    @ApiModelProperty("用户的菜单")
    private List<UserMenuDTO> menus;

    @ApiModelProperty("租户信息")
    private TenantInfoDTO tenantInfo;

    @ApiModelProperty("用户的角色")
    private List<RoleDTO> roles;

}
