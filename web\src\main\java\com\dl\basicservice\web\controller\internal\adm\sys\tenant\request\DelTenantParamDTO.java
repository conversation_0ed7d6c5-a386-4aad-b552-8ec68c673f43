package com.dl.basicservice.web.controller.internal.adm.sys.tenant.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 13:55
 */
@Data
public class DelTenantParamDTO {

    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty("操作人id")
    @NotNull(message = "操作人id不能为空")
    private Long operatorId;

}
