package com.dl.basicservice.web.controller.adm.sys.admuser.vo;
import com.dl.basicservice.web.controller.adm.sys.menu.vo.FuncVO;
import com.dl.basicservice.web.controller.adm.sys.role.vo.RoleVO;
import com.dl.basicservice.web.controller.adm.sys.tenant.vo.TenantBaseVO;
import com.dl.basicservice.web.controller.adm.sys.tenant.vo.UserMenuVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
@ApiModel("系统用户信息")
public class AdmUserVO {
    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("账号")
    private String account;

    @ApiModelProperty("性别")
    private String gender;

    @ApiModelProperty("token")
    private String token;

    @ApiModelProperty("租户id")
    private String tenantCode;

    @ApiModelProperty("资源中心token")
    private String rcToken;

    @ApiModelProperty("是否超级管理员")
    private String isSuperAdm;

    @ApiModelProperty("姓名")
    private String userName;

    @ApiModelProperty(value = "状态: 1=已激活，2=已禁用，4=未激活，5=退出企业")
    private String status;

    @ApiModelProperty(value = "状态: 1=已激活，2=已禁用，4=未激活，5=退出企业")
    private String statusStr;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("拥有的权限编码")
    private Set<FuncVO> functions;

    @ApiModelProperty("用户的菜单")
    private List<UserMenuVO> menus;

    @ApiModelProperty("租户信息")
    private TenantBaseVO tenantInfo;

    @ApiModelProperty("用户的角色")
    private List<RoleVO> roles;

    private String thumbAvatar;
}
