package com.dl.basicservice.biz.common.tencentcloud.cos.policy;

import com.dl.basicservice.biz.common.tencentcloud.cos.policy.action.BucketApiAction;
import com.dl.basicservice.biz.common.tencentcloud.cos.policy.action.ObjectApiAction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Statement {
    /**
     * 动作
     */
    private List<String> action;

    private String effect;

    private List<String> resource;

    public static Statement createSimpleObjectPolicy(List<String> resources, boolean editable) {
        List<String> action = new ArrayList<>();
        action.add(ObjectApiAction.GET_OBJECT.getValue());
        action.add(BucketApiAction.HEAD.getValue());
        action.add(ObjectApiAction.LIST_PARTS.getValue());
        action.add(ObjectApiAction.MULTI_UPLOADS.getValue());
        action.add(ObjectApiAction.HEAD_OBJECT.getValue());
        action.add(ObjectApiAction.OPTIONS_OBJECT.getValue());
        if (editable) {
            action.add(ObjectApiAction.INIT_MULTI_UPLOAD.getValue());
            action.add(ObjectApiAction.COMPLETE_MULTI_UPLOAD.getValue());
            action.add(ObjectApiAction.ABORT_MULTI_UPLOAD.getValue());
            action.add(ObjectApiAction.UPLOAD_PART.getValue());
            action.add(ObjectApiAction.DELETE.getValue());
            action.add(ObjectApiAction.POST.getValue());
            action.add(ObjectApiAction.PUT.getValue());
        }
        return Statement.builder().action(action).effect(Effect.ALLOW.getValue()).resource(resources).build();
    }
}
