package com.dl.basicservice.web.controller.internal.adm.sys.tenant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dl.basicservice.biz.common.constant.Const;
import com.dl.basicservice.biz.common.service.ObjectStoragePathService;
import com.dl.basicservice.biz.common.util.DateUtil;
import com.dl.basicservice.biz.common.util.NameInitialsUtil;
import com.dl.basicservice.biz.common.util.OperatorUtil;
import com.dl.basicservice.biz.config.TenantDomainConfig;
import com.dl.basicservice.biz.dal.sys.role.po.TenantRolePO;
import com.dl.basicservice.biz.dal.sys.tenant.po.SysTenantInfoPO;
import com.dl.basicservice.biz.dal.sys.user.po.TenantAdmUserPO;
import com.dl.basicservice.biz.manager.sys.role.TenantRoleManager;
import com.dl.basicservice.biz.manager.sys.role.TenantUserRoleManager;
import com.dl.basicservice.biz.manager.sys.role.consts.RoleConsts;
import com.dl.basicservice.biz.manager.sys.role.enums.RoleTypeEnum;
import com.dl.basicservice.biz.manager.sys.tenant.TenantInfoManager;
import com.dl.basicservice.biz.manager.sys.user.SysAdmUserService;
import com.dl.basicservice.biz.manager.sys.user.bo.SysUserRolesParamBO;
import com.dl.basicservice.web.controller.adm.sys.tenant.param.TenantParam;
import com.dl.basicservice.web.controller.adm.sys.tenant.vo.AddTenantVO;
import com.dl.basicservice.web.controller.internal.adm.base.InternalAbstractController;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.convert.InternalTenantConvert;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.AddTenantParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.DelTenantParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.TenantInfoQueryListParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.TenantInfoQueryParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.UpdateTenantParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.UpdateTenantStatusParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.request.UpdateTenantTrialParamDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.resp.TenantInfoDTO;
import com.dl.basicservice.web.controller.internal.adm.sys.tenant.resp.TenantListDTO;
import com.dl.framework.common.idg.HostTimeIdg;
import com.dl.framework.common.model.ResultModel;
import com.dl.framework.common.model.ResultPageModel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.security.SecureRandom;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Api("内部调用-租户控制器")
@Slf4j
@RestController
@RequestMapping("/internal/tenant")
public class InternalTenantController extends InternalAbstractController {

    @Autowired
    private TenantInfoManager tenantInfoManager;

    @Autowired
    private ObjectStoragePathService objectStoragePathService;

    @Resource
    private HostTimeIdg hostTimeIdg;

    @Resource
    private TenantDomainConfig tenantDomainConfig;

    @Resource
    private OperatorUtil operatorUtil;

    @Resource
    private SysAdmUserService sysAdmUserService;

    @Autowired
    private TenantRoleManager tenantRoleManager;

    @Autowired
    private TenantUserRoleManager tenantUserRoleManager;

    @PostMapping("/info")
    @ApiOperation("租户-查询租户详情")
    public ResultModel<TenantInfoDTO> tenantInfoForNoLogin(@RequestBody @Validated TenantInfoQueryParamDTO param) {
        SysTenantInfoPO currentTenant = tenantInfoManager.getTenantInfoFromCache(param.getTenantCode());
        Assert.notNull(currentTenant, "租户不存在!");

        TenantInfoDTO tenantInfoDTO = new TenantInfoDTO();
        InternalTenantConvert.fillTenantDTO(currentTenant, tenantInfoDTO);
        tenantInfoDTO.setLogoImg(objectStoragePathService.compositeObjectStoragePath(currentTenant.getLogoImg()));
        tenantInfoDTO.setSmallLogo(objectStoragePathService.compositeObjectStoragePath(currentTenant.getSmallLogo()));
        tenantInfoDTO.setThirdLevelDomain(tenantDomainConfig.getTenantThirdLevelDomain(param.getTenantCode()));
        return ResultModel.success(tenantInfoDTO);
    }

    @PostMapping("/listall")
    @ApiOperation("租户-查询所有租户列表")
    public ResultModel<List<TenantInfoDTO>> listAll() {
        List<SysTenantInfoPO> tenantInfoPOList = tenantInfoManager
                .list(Wrappers.lambdaQuery(SysTenantInfoPO.class).eq(SysTenantInfoPO::getIsDeleted, Const.ZERO));

        List<TenantInfoDTO> dtoList = tenantInfoPOList.stream().filter(Objects::nonNull).map(po -> {
            TenantInfoDTO tenantInfoDTO = new TenantInfoDTO();
            InternalTenantConvert.fillTenantDTO(po, tenantInfoDTO);
            tenantInfoDTO.setLogoImg(objectStoragePathService.compositeObjectStoragePath(po.getLogoImg()));
            tenantInfoDTO.setSmallLogo(objectStoragePathService.compositeObjectStoragePath(po.getSmallLogo()));
            return tenantInfoDTO;
        }).collect(Collectors.toList());
        return ResultModel.success(dtoList);
    }

    @PostMapping("/list")
    @ApiOperation("租户-查询租户列表")
    public ResultModel<List<TenantInfoDTO>> list(@RequestBody @Validated TenantInfoQueryListParamDTO param) {
        List<SysTenantInfoPO> tenantInfoPOList = tenantInfoManager.list(Wrappers.lambdaQuery(SysTenantInfoPO.class)
                .in(SysTenantInfoPO::getTenantCode, param.getTenantCodeList())
                .eq(SysTenantInfoPO::getIsDeleted, Const.ZERO));

        List<TenantInfoDTO> dtoList = tenantInfoPOList.stream().filter(Objects::nonNull).map(po -> {
            TenantInfoDTO tenantInfoDTO = new TenantInfoDTO();
            InternalTenantConvert.fillTenantDTO(po, tenantInfoDTO);
            tenantInfoDTO.setLogoImg(objectStoragePathService.compositeObjectStoragePath(po.getLogoImg()));
            tenantInfoDTO.setSmallLogo(objectStoragePathService.compositeObjectStoragePath(po.getSmallLogo()));
            return tenantInfoDTO;
        }).collect(Collectors.toList());
        return ResultModel.success(dtoList);
    }

    @PostMapping("/add")
    @ApiOperation("租户-创建")
    @Transactional(rollbackFor = Exception.class)
    public ResultModel<String> addTenant(@RequestBody @Validated AddTenantParamDTO param) {
        Assert.isTrue(StringUtils.length(param.getTenantDesc()) <= Const.ONE_HUNDRED, "租户描述不能超过100个字符");
        // 生成租户编号
        String tenantCode = this.generateTenantCode(param);
        //生成租户三级域名
        String tenantThirdDomain = tenantDomainConfig.getTenantThirdLevelDomain(tenantCode);
        // 新增租户
        SysTenantInfoPO tenant = new SysTenantInfoPO();
        tenant.setTenantCode(tenantCode);
        tenant.setName(param.getName());
        objectStoragePathService.preProcessObjectStorageUrl(param::getLogoImg, tenant::setLogoImg);
        objectStoragePathService.preProcessObjectStorageUrl(param::getSmallLogo, tenant::setSmallLogo);
        tenant.setTenantDomain(tenantThirdDomain);
        tenant.setPathPart(param.getPathPart());
        tenant.setIsWeWork(param.getIsWeWork());
        tenant.setTenantDesc(param.getTenantDesc());
        if (Objects.equals(param.getIsProvider(), Const.ONE)) {
            tenant.setIsProvider(param.getIsProvider());
        }
        tenant.setAuthMode(param.getAuthMode());
        tenant.setSimulateLocalPreview(param.getSimulateLocalPreview());
        tenant.setPrivateKey(Const.TENANT_DEFAULT_PRIVATE_KEY);
        tenant.setDmProduceMode(Const.TWO);
        tenant.setCreateBy(param.getOperatorId());
        tenant.setModifyBy(param.getOperatorId());
        tenant.setCreateDt(new Date());
        tenant.setModifyDt(new Date());

        tenantInfoManager.save(tenant);
        //刷新租户缓存
        tenantInfoManager.refreshSingleTenantCache(tenant.getTenantCode());
        return ResultModel.success(tenantCode);
    }

    @PostMapping("/update")
    @ApiOperation("租户-修改")
    @Transactional(rollbackFor = Exception.class)
    public ResultModel updateTenant(@RequestBody @Validated UpdateTenantParamDTO param) {
        SysTenantInfoPO tenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .eq(SysTenantInfoPO::getId, param.getId()).one();
        Assert.notNull(tenant, "租户不存在");
        Assert.isTrue(Objects.equals(tenant.getStatus(), Const.ZERO) || StringUtils
                .equals(param.getTenantCode(), tenant.getTenantCode()), "租户编号仅可在初次启用前修改！");
        if (!StringUtils.equals(param.getTenantCode(), tenant.getTenantCode())) {
            Assert.isNull(tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                    .eq(SysTenantInfoPO::getTenantCode, param.getTenantCode()).one(), "该租户编号已存在，请修改！");
        }
        if (Objects.isNull(param.getIsProvider())) {
            param.setIsProvider(Const.ZERO);
        }

        String originTenantCode = tenant.getTenantCode();
        tenant.setName(param.getName());
        tenant.setTenantCode(param.getTenantCode());
        tenant.setTenantDomain(tenantDomainConfig.getTenantThirdLevelDomain(param.getTenantCode()));
        tenant.setPathPart(param.getPathPart());
        if (StringUtils.isBlank(param.getLogoImg())){
            tenant.setLogoImg("");
        } else {
            objectStoragePathService.preProcessObjectStorageUrl(param::getLogoImg, tenant::setLogoImg);
        }
        objectStoragePathService.preProcessObjectStorageUrl(param::getSmallLogo, tenant::setSmallLogo);
        tenant.setIsHideQrLogin(Objects.isNull(param.getIsHideQrLogin()) ? Const.ZERO : param.getIsHideQrLogin());
        tenant.setTenantDesc(param.getTenantDesc());
        tenant.setSimulateLocalPreview(param.getSimulateLocalPreview());
        tenant.setModifyBy(param.getOperatorId());
        tenant.setModifyDt(new Date());
        tenantInfoManager.updateById(tenant);
        if (!StringUtils.equals(originTenantCode, param.getTenantCode())) {
            //清理老租户缓存
            tenantInfoManager.refreshSingleTenantCache(originTenantCode);
        }

        //刷新租户缓存
        tenantInfoManager.refreshSingleTenantCache(param.getTenantCode());
        return ResultModel.success(null);
    }

    @PostMapping("/page")
    @ApiOperation("租户-查询分页列表")
    public ResultPageModel<TenantListDTO> pageTenant(@RequestBody @Validated TenantParam p) {
        LambdaQueryChainWrapper<SysTenantInfoPO> queryWrapper = tenantInfoManager.lambdaQuery()
                .eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .likeRight(StringUtils.isNotBlank(p.getName()), SysTenantInfoPO::getName, p.getName());
        queryWrapper.orderByDesc(SysTenantInfoPO::getId);

        SysTenantInfoPO currentTenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .eq(SysTenantInfoPO::getTenantCode, operatorUtil.getOpTenantCode()).one();
        if (!Objects.equals(Const.ONE, currentTenant.getIsProvider())) {
            queryWrapper.eq(SysTenantInfoPO::getTenantCode, operatorUtil.getOpTenantCode());
        }
        IPage<SysTenantInfoPO> tenantPage = queryWrapper.orderByDesc(SysTenantInfoPO::getId)
                .page(new Page<>(p.getPageIndex(), p.getPageSize()));
        if (CollectionUtils.isEmpty(tenantPage.getRecords())) {
            return pageQueryModel(tenantPage, Collections.emptyList());
        }
        List<String> tenantCodeList = tenantPage.getRecords().stream().map(SysTenantInfoPO::getTenantCode).distinct()
                .collect(Collectors.toList());
        List<TenantAdmUserPO> superAdminUsers = sysAdmUserService.lambdaQuery()
                .in(TenantAdmUserPO::getTenantCode, tenantCodeList)
                .eq(TenantAdmUserPO::getIsSuperAdm, NumberUtils.INTEGER_ONE).list();
        Map<String, List<String>> superAdminUserMap = new HashMap<>();
        superAdminUserMap.putAll(superAdminUsers.stream().collect(Collectors.groupingBy(TenantAdmUserPO::getTenantCode,
                Collectors.mapping(TenantAdmUserPO::getAccount, Collectors.toList()))));
        List<TenantListDTO> resultList = tenantPage.getRecords().stream().map(po -> {
            TenantListDTO tenantVO = new TenantListDTO();
            tenantVO.setId(po.getId().toString());
            tenantVO.setTenantCode(po.getTenantCode());
            tenantVO.setName(po.getName());
            tenantVO.setCreateBy(po.getCreateBy());
            tenantVO.setCreateDt(po.getCreateDt());
            tenantVO.setModifyBy(po.getModifyBy());
            tenantVO.setModifyDt(po.getModifyDt());
            tenantVO.setStatus(po.getStatus());
            tenantVO.setIsProvider(po.getIsProvider());
            tenantVO.setIsHideQrLogin(po.getIsHideQrLogin());
            tenantVO.setTenantDomain(po.getTenantDomain());
            tenantVO.setPathPart(po.getPathPart());
            tenantVO.setIsWeWork(po.getIsWeWork());
            tenantVO.setTenantDesc(po.getTenantDesc());
            tenantVO.setIsTrial(po.getIsTrial());
            tenantVO.setAuthMode(po.getAuthMode());
            tenantVO.setSimulateLocalPreview(po.getSimulateLocalPreview());
            tenantVO.setLogoImg(objectStoragePathService.compositeObjectStoragePath(po.getLogoImg()));
            tenantVO.setSmallLogo(objectStoragePathService.compositeObjectStoragePath(po.getSmallLogo()));
            tenantVO.setAdminAccounts(superAdminUserMap.get(po.getTenantCode()));
            //租户三级域名访问地址判断
            String tenantUrl = po.getTenantDomain().endsWith("/") ?
                    po.getTenantDomain() + "adm" : po.getTenantDomain() + "/adm";
            if (!po.getTenantDomain().contains(po.getTenantCode().toLowerCase())) {
                tenantUrl = tenantUrl + "/login?agentId=" + po.getTenantCode();
            }
            tenantVO.setTenantUrl(tenantUrl);
            return tenantVO;
        }).collect(Collectors.toList());
        return pageQueryModel(tenantPage, resultList);
    }

    @PostMapping("/update/status")
    @ApiOperation("更新租户启用状态")
    @Transactional(rollbackFor = Exception.class)
    public ResultModel updateStatus(@RequestBody @Validated UpdateTenantStatusParamDTO paramDTO) {
        SysTenantInfoPO tenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .eq(SysTenantInfoPO::getId, paramDTO.getId()).one();
        Assert.notNull(tenant, "租户不存在");
        if (Objects.equals(paramDTO.getStatus(), tenant.getStatus())) {
            return ResultModel.success(null);
        }
        if (Objects.equals(Const.ONE, paramDTO.getStatus())) {
            //状态 0未启用 1正常 2停用
            tenant.setStatus(Const.ONE);
            //初始化admin用户
            TenantAdmUserPO one = sysAdmUserService.lambdaQuery()
                    .eq(TenantAdmUserPO::getTenantCode, tenant.getTenantCode())
                    .eq(TenantAdmUserPO::getAccount, RoleConsts.ADMIN).eq(TenantAdmUserPO::getStatus, Const.ONE).one();
            if (Objects.isNull(one)) {
                this.initAdminUser(tenant.getTenantCode(), getSystemCode());
            }
        } else {
            //状态 0未启用 1正常 2停用
            tenant.setStatus(Const.TWO);
        }
        tenant.setModifyBy(paramDTO.getOperatorId());
        tenant.setModifyDt(new Date());
        tenantInfoManager.updateById(tenant);
        tenantInfoManager.refreshSingleTenantCache(tenant.getTenantCode());
        return ResultModel.success(null);
    }

    @PostMapping("/del")
    @ApiOperation("删除租户")
    @Transactional(rollbackFor = Exception.class)
    public ResultModel del(@RequestBody @Validated DelTenantParamDTO paramDTO) {
        SysTenantInfoPO tenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .eq(SysTenantInfoPO::getId, paramDTO.getId()).one();
        Assert.notNull(tenant, "租户不存在");
        Assert.isTrue(Objects.equals(Const.ZERO, tenant.getStatus()), "租户非未启用状态，不允许删除");
        tenant.setIsDeleted(true);
        tenant.setModifyDt(new Date());
        tenant.setModifyBy(paramDTO.getOperatorId());
        tenantInfoManager.updateById(tenant);
        tenantInfoManager.refreshSingleTenantCache(tenant.getTenantCode());
        return ResultModel.success(null);
    }

    @PostMapping("/update/trial")
    @ApiOperation("修改租户是否试用")
    public ResultModel updateTrial(@RequestBody @Validated UpdateTenantTrialParamDTO param) {
        SysTenantInfoPO tenant = tenantInfoManager.lambdaQuery().eq(SysTenantInfoPO::getIsDeleted, Const.ZERO)
                .eq(SysTenantInfoPO::getTenantCode, param.getTenantCode()).one();
        Assert.notNull(tenant, "租户不存在");
        tenant.setModifyDt(new Date());
        tenant.setIsTrial(param.getTrialStatus());
        tenant.setModifyBy(param.getOperatorId());
        tenantInfoManager.updateById(tenant);
        tenantInfoManager.refreshSingleTenantCache(tenant.getTenantCode());
        return ResultModel.success(null);
    }

    private String generateTenantCode(AddTenantParamDTO param) {
        //根据名称首字母生成
        String tenantCode = NameInitialsUtil.get(param.getName());
        if (StringUtils.isBlank(tenantCode)) {
            return hostTimeIdg.generateId().toString();
        }

        //判断租户编码是否已存在
        SysTenantInfoPO existTenant = tenantInfoManager
                .getOne(Wrappers.lambdaQuery(SysTenantInfoPO.class).eq(SysTenantInfoPO::getTenantCode, tenantCode));
        if (Objects.isNull(existTenant)) {
            return tenantCode;
        }

        return hostTimeIdg.generateId().toString();
    }

    private AddTenantVO initAdminUser(String tenantCode, String systemCode) {
        // 创建超级管理员角色
        Long roleId = hostTimeIdg.generateId().longValue();
        TenantRolePO po = new TenantRolePO();
        po.setRoleType(RoleTypeEnum.SUPER_ADMIN.getCode());
        po.setName(RoleConsts.ADMIN_ROLE_NAME);
        po.setTenantCode(tenantCode);
        po.setRoleId(roleId);
        po.setSystemCode(systemCode);
        tenantRoleManager.save(po);

        // 创建超级管理员账号
        Long userId = hostTimeIdg.generateId().longValue();
        String rawPwd = DateUtil.format(new Date(), DateUtil.YMD);
        TenantAdmUserPO sysAdmUserPO = new TenantAdmUserPO();
        sysAdmUserPO.setUserId(userId);
        sysAdmUserPO.setTenantCode(tenantCode);
        sysAdmUserPO.setIsSuperAdm(Const.ONE);
        sysAdmUserPO.setAccount(RoleConsts.ADMIN);
        sysAdmUserPO.setStatus(Const.ONE);
        sysAdmUserPO.setPassword(generatePwd(rawPwd));
        sysAdmUserService.save(sysAdmUserPO);

        // 赋予超管角色
        SysUserRolesParamBO bo = new SysUserRolesParamBO();
        bo.setUserId(userId);
        bo.setRoleIds(Arrays.asList(roleId));
        bo.setTenantCode(tenantCode);
        bo.setSystemCode(systemCode);
        tenantUserRoleManager.saveUserRoles(bo);
        AddTenantVO vo = new AddTenantVO();
        vo.setSuperAdminPwd(rawPwd);
        vo.setSuperAdminAccount(RoleConsts.ADMIN);
        return vo;
    }

    private String generatePwd(String rawPassword) {
        BCryptPasswordEncoder passwordEncoder = passwordEncoder();
        return passwordEncoder.encode(rawPassword);
    }

    private static BCryptPasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(BCryptPasswordEncoder.BCryptVersion.$2A, new SecureRandom());
    }
}
