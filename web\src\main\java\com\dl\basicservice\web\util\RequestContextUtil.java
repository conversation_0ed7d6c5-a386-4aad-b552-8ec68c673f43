package com.dl.basicservice.web.util;

import com.dl.basicservice.biz.common.constant.Const;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

@Component
public class RequestContextUtil {

    private HttpServletRequest getRequest() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (Objects.nonNull(requestAttributes)) {
            return ((ServletRequestAttributes) requestAttributes).getRequest();
        }
        return null;
    }

    /**
     * 获取C端请求header - app-id
     *
     * @return
     */
    public String getCustReqHeader() {
        HttpServletRequest request = getRequest();
        return Objects.isNull(request) ? null : request.getHeader(Const.CUSTOMER_REQUEST_HEADER);
    }

}