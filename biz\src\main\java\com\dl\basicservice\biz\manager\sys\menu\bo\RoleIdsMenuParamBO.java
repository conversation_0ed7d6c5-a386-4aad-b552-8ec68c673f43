package com.dl.basicservice.biz.manager.sys.menu.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@ApiModel("角色菜单权限配置")
public class RoleIdsMenuParamBO {

    @NotNull
    @ApiModelProperty("角色主键")
    private List<Long> roleIds;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("菜单主键数组")
    private List<Long> menuIds;

    @Size(min = 0, max = 5000)
    @ApiModelProperty("功能编码数组")
    private List<Long> functionIds;

    @ApiModelProperty(hidden = true)
    private Long loginUserId;

    @ApiModelProperty("系统code")
    private String systemCode;
}

