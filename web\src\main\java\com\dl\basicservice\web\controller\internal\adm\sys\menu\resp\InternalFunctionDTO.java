package com.dl.basicservice.web.controller.internal.adm.sys.menu.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2024-04-15 14:15
 */
@Data
@ApiModel("功能权限")
public class InternalFunctionDTO {

    @ApiModelProperty("功能id")
    private String functionId;

    @ApiModelProperty("功能名称")
    private String name;

    @ApiModelProperty("功能图标")
    private String icon;

    @ApiModelProperty("显示顺序")
    private Integer sort;

    @ApiModelProperty("是否已分配权限，0否1是")
    private Integer owner = 0;
}
