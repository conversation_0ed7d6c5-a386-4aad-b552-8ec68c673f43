package com.dl.basicservice.biz.manager.sys.userbind.bo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import static com.dl.basicservice.biz.manager.sys.userbind.consts.PartnerTokenConsts.PARTNER_TOKEN_EXT_USER;
import static com.dl.basicservice.biz.manager.sys.userbind.consts.PartnerTokenConsts.PARTNER_TOKEN_NAME;
import static com.dl.basicservice.biz.manager.sys.userbind.consts.PartnerTokenConsts.PARTNER_TOKE_TARGET;
import static com.dl.basicservice.biz.manager.sys.userbind.consts.PartnerTokenConsts.PARTNER_TOKE_TIMESTAMP;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-02 15:08
 */
@Data
public class PartnerTokenInfo {

    /**
     * 外部平台用户ID
     */
    @JSONField(name = PARTNER_TOKEN_EXT_USER)
    private String extUserId;

    /**
     * 姓名
     */
    @J<PERSON><PERSON>ield(name = PARTNER_TOKEN_NAME)
    private String name;

    /**
     * 绑定后跳转地址
     */
    @J<PERSON><PERSON><PERSON>(name = PARTNER_TOKE_TARGET)
    private String target;

    /**
     * 时间戳
     */
    @JSONField(name = PARTNER_TOKE_TIMESTAMP)
    private String timestamp;
}
