package com.dl.basicservice.biz.dal.sys.menu.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-05-24 10:31
 */
@Data
public class TenantMenuDTO {

    /**
     * 菜单id
     */
    private Long menuId;

    /**
     * 上级菜单,0表示一级菜单
     */
    private Long parentId;
    /**
     * 名称
     */
    private String name;

    /**
     * 图标
     */
    private String icon;

    /**
     * 菜单地址(静态url)
     */
    private String url;

    /**
     * 菜单级别 1：一级 2：二级 3：三级
     */
    private Integer menuLevel;

    /**
     * 显示顺序
     */
    private Integer sort;

    private String tenantCode;
}
