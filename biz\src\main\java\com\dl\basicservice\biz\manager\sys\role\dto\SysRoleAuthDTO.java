package com.dl.basicservice.biz.manager.sys.role.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel("角色权限信息")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SysRoleAuthDTO {

    @ApiModelProperty("角色ID")
    private Long roleId;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("角色拥有的菜单ID")
    private List<Long> menus;

    @ApiModelProperty("角色拥有的功能编码")
    private List<String> functionCodes;
}
