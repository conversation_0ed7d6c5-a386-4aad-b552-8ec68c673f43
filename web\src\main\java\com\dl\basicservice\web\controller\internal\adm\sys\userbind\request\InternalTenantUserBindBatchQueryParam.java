package com.dl.basicservice.web.controller.internal.adm.sys.userbind.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Set;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-11-06 16:20
 */
@Data
public class InternalTenantUserBindBatchQueryParam {

    @NotBlank(message = "租户号不能为空")
    @ApiModelProperty("租户号")
    private String tenantCode;

    @ApiModelProperty("用户id集合")
    private Set<Long> userIds;
}
