package com.dl.basicservice.biz.dal.sys.oplog.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version V1.0
 * @since 2023-10-17 14:32
 */
@Data
@TableName("op_log")
public class OpLogPO {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 日志id
     */
    @TableField("log_id")
    private Long logId;
    /**
     * 接入业务的编码
     */
    @TableField("biz_code")
    private String bizCode;
    /**
     * 操作对象
     */
    @TableField("op_object")
    private String opObject;
    /**
     * 操作对象主键
     */
    @TableField("op_key")
    private String opKey;
    /**
     * 操作类型 自定义（add、update、delete）
     */
    @TableField("op_type")
    private String opType;
    /**
     * 操作前数据 格式自定义
     */
    @TableField("op_before")
    private String opBefore;
    /**
     * 操作后数据 格式自定义
     */
    @TableField("op_after")
    private String opAfter;
    /**
     * 操作说明
     */
    @TableField("remark")
    private String remark;
    /**
     * 租户编码
     */
    @TableField("tenant_code")
    private String tenantCode;
    /**
     * 操作人
     */
    @TableField("op_user_id")
    private String opUserId;
    /**
     * 操作人姓名
     */
    @TableField("op_user_name")
    private String opUserName;
    /**
     * 操作时间
     */
    @TableField("op_dt")
    private Date opDt;
    /**
     * 扩展信息
     */
    @TableField("ext_data")
    private String extData;

    private Date createDt;

    private Date modifyDt;

    private Integer isDeleted;

}
