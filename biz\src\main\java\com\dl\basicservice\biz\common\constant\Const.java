package com.dl.basicservice.biz.common.constant;

import java.math.MathContext;
import java.math.RoundingMode;

public interface Const {
    String SIMPLE_JWT_SECRET = "ZGluZ2xpa2VqaV9oYW5nemhvdQ==";//JWT秘钥
    String SIMPLE_JWT_PREFIX = "dl_jwt_";//JWT前缀

    /**
     * 默认租户
     */
    String DEFAULT_TENANT_CODE = "DL";

    Integer ZERO = 0;

    Integer ONE = 1;

    Integer TWO = 2;

    Integer THREE = 3;

    Integer FOUR = 4;

    Integer FIVE = 5;

    Integer SIX = 6;

    Integer SEVEN = 7;

    Integer TEN = 10;

    Integer FOURTEEN = 14;

    Integer FIFTEEN = 15;

    Integer TWENTY = 20;

    Integer THIRTY = 30;

    Integer FIFTY = 50;

    Integer ONE_HUNDRED = 100;

    Integer TWO_HUNDREDS = 200;

    Integer FIVE_HUNDREDS = 500;

    String ZERO_STR = "0";

    String ONE_STR = "1";

    Long ZERO_LONG = 0L;

    String VERTICAL_LINE = "|";

    String SLASH = "/";

    String HTTP_PREFIX = "http";

    String REDISSON_LOCK_KEY = "lock_key:";

    String CUSTOMER_REQUEST_HEADER = "app-id";
    
    String UTF8 = "UTF-8";

    /**
     * 管理端 authorization
     */
    String TOKEN_HEADER_NAME = "X-Authorization";

    /**
     * 调用
     */
    String BASIC_SERVICE_INVOKE_OPERATOR = "X-invoke-operator";

    /**
     * 系统访问操作租户
     */
    String BASIC_SERVICE_INVOKE_TENANTCODE = "X-invoke-tenantCode";

    /**
     * app端 authorization
     */
    String APP_TOKEN_HEADER_NAME = "X-App-Authorization";

    /**
     * customer端 authorization
     */
    String CUSTOMER_TOKEN_HEADER_NAME = "X-Cust-Authorization";

    String WXWORK_API_SUCCESS_CODE = "0";

    String TIME_RULE_SPLITTER = "-";
    String HMS_RULE_SPLITTER = ":";

    String RESP_CODE_OK = "ok";

    /**
     * 会话存档es index名称前缀
     */
    String CONVERSATION_ES_INDEX_NAME_PREFIX = "conversation_index_";

    /**
     * 通讯录相关回调
     */
    String EVENT_EMPLOYEE = "change_contact";

    /**
     * 客户相关回调
     */
    String EVENT_CONTACT = "change_external_contact";

    /**
     * 客户群相关回调
     */
    String EVENT_CHAT = "change_external_chat";

    /**
     * 客户标签相关回调
     */
    String EVENT_CONTACT_TAG = "change_external_tag";

    Long SYS_USER_ID = 0L;

    String INDEX_CODE = "H30269.csi";

    /**
     * 默认密码
     */
    String ADM_USER_DEFAULT_PWD = "123456";

    String SM4_INIT = "dinglitec@666666";

    String GO_RIGHT_NOW = "立即前往";

    String NULL = "null";

    /**
     * 净值、收益率等5位有效数字
     */
    MathContext MATH_CONTEXT = new MathContext(5, RoundingMode.HALF_UP);

    /**
     * 系统编码
     */
    String SYSTEM_CODE = "X-System-code";

    String DEFAULT_SYSTEM_CODE = "dl-wealth-center";

    String MAGIC_SYSTEM_CODE = "dl-magicvideo";

    /**
     * 租户默认私钥
     */
    String TENANT_DEFAULT_PRIVATE_KEY = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCbJ759JkFrWSOWenRA0Wfn4jaUMjSX7EtkuY/9IfASnhkRtdxsK0O+8S2lDbEW3SiQmiiumt47GBbKdU+fmn9mo4DAp7/KzFPjoE+OuDaxr8SatFEdfPxkLUNIt7Uj4XgaYwzZXvkZhDaJr0OuLfa2XBtnpYFuDkZb/AqgnPeQsBK3FJKyKkmtb3M7Ljj8F9hdMckG2GRxoyBrl96IKCEdOa1qpg58zZI2FbZI5CEdpr8oZ1wQv6z39YS4022u3THy1c49gAtEld1cSubA/AusgMequ0oaTjHRbZ+CehHiprbhPWn7Pt8IJGj/0a9hqnbFhVUIWI44xcZXB4VGV2ZzAgMBAAECggEAUJofDWjYR2oxYLUQu7ONpRsRe39xbxMkP5lewgPlceaL43V0owT+1qz4J2yreNM/hY9wXvS5Sj3DaT30NVfPo4SBGJSMwE/RrMjVS7FLSJelLTFLimQpwej5sUcuZQt2l06pmCsMgUL8Ch7wrAXYyveijP+f84qa5qJS6wlgWtstIY7n4uzCL5OzRSKoW0HC54qrUFI/L0JYfsgI6MzJxN72CThvKpgqItyBanAz4/H1csbegVKX0kLC2w+R2rpiXevL8JId+yZ54HKiUmIFEtPDP+8fpOSPbT7FAD/kyxcdGj/3zbFY60e/s/1zH8K15KsG9Uh3wW3e+HzaYLS8IQKBgQDe0CovzXV7KSDGkAa4Qe1t6jeSA80Lq//RRF8jKXCuJT3SnangqqWJBvZZ3/NxcUEvAN0c/YowTCeMA7hES1D7WkTi2TyL+fIdNlUAFltKsz+PqI37Zrod5ZrHCq3y5Mew4Y6OPW1X76qJ33Y9D+P0kNMzoUTYqt21b9NTv0fEawKBgQCyQ8r9eaR7SpcrKnxS3El/x2gYOH6PbPHL/oIqyKZgSyBFZF+3PBuEs9E0NHlLFHbRGaCGFdUYx3S5jBPzjIK+7rhlhDCeqtZcxF89YsGpfgIWMHKT7GhEsUMmrOgR2w7MmgepAROxQj94Q+Jsbid+jmDdiBsV1NiHPPwUEg2oGQKBgQChrYVfKIQ9+UsKKkpl+5jQsCrgrhdkh8taS3WJazGTe/yPTs6M8uapNr8d2i6pO5gkBklsFuHmR+xOYgicrdY2fXsM7LtNivHPlrQ5Gv/lhYnysUlNim408X3NPoeYf8ATLqiluBcWvxcNcnQ2vMgZl9lZVoVBf0LfvCQpWdw2vwKBgEE3XAPvhKU2XKeGG4WU4a7FnOd/g42lJbCjo6tTTMrdsSix1/KJIughgN/Acr9s9Sr6XSewxQ0TqzhWbtYjCZIgc4VwHvltNo8pqE4k2wTO/KRxhPlo+5xl3VNA3oXpxjhEAZlqs3Gd8upkq2lPw1Mhc36YVJBgFfcj8HTHRgfBAoGAGkWuthKaxojsmzzah/MNo8IvrzLP9c0SLzLds8773TioOtG13OVL/HekNWvyUQxbX/txEOtqQgjJdui0PJs5gFFvlfU8EDuiLofxLNx2nCnUiTVvQ8P+lvZ0yAbNIYKnyGU50leMJ6/zTQ0YxzxGaQfQFtqDp31KCw/SkZJcgTs=";

}
